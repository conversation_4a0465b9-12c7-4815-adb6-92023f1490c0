var Oe=Object.create;var re=Object.defineProperty;var Me=Object.getOwnPropertyDescriptor;var Ce=Object.getOwnPropertyNames;var Ee=Object.getPrototypeOf,ke=Object.prototype.hasOwnProperty;var Ie=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ve=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ce(t))!ke.call(e,i)&&i!==r&&re(e,i,{get:()=>t[i],enumerable:!(n=Me(t,i))||n.enumerable});return e};var B=(e,t,r)=>(r=e!=null?Oe(Ee(e)):{},Ve(t||!e||!e.__esModule?re(r,"default",{value:e,enumerable:!0}):r,e));var H=Ie((G,ne)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof G<"u")t(ne);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:G,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=n=>{let i={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(i).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class o extends WeakMap{constructor(l,f=void 0){super(f),this.createItem=l}get(l){return this.has(l)||this.set(l,this.createItem(l)),super.get(l)}}let u=s=>s&&typeof s=="object"&&typeof s.then=="function",c=(s,l)=>(...f)=>{n.runtime.lastError?s.reject(new Error(n.runtime.lastError.message)):l.singleCallbackArg||f.length<=1&&l.singleCallbackArg!==!1?s.resolve(f[0]):s.resolve(f)},g=s=>s==1?"argument":"arguments",a=(s,l)=>function(_,...w){if(w.length<l.minArgs)throw new Error(`Expected at least ${l.minArgs} ${g(l.minArgs)} for ${s}(), got ${w.length}`);if(w.length>l.maxArgs)throw new Error(`Expected at most ${l.maxArgs} ${g(l.maxArgs)} for ${s}(), got ${w.length}`);return new Promise((S,T)=>{if(l.fallbackToNoCallback)try{_[s](...w,c({resolve:S,reject:T},l))}catch(d){console.warn(`${s} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,d),_[s](...w),l.fallbackToNoCallback=!1,l.noCallback=!0,S()}else l.noCallback?(_[s](...w),S()):_[s](...w,c({resolve:S,reject:T},l))})},A=(s,l,f)=>new Proxy(l,{apply(_,w,S){return f.call(w,s,...S)}}),x=Function.call.bind(Object.prototype.hasOwnProperty),q=(s,l={},f={})=>{let _=Object.create(null),w={has(T,d){return d in s||d in _},get(T,d,O){if(d in _)return _[d];if(!(d in s))return;let h=s[d];if(typeof h=="function")if(typeof l[d]=="function")h=A(s,s[d],l[d]);else if(x(f,d)){let P=a(d,f[d]);h=A(s,s[d],P)}else h=h.bind(s);else if(typeof h=="object"&&h!==null&&(x(l,d)||x(f,d)))h=q(h,l[d],f[d]);else if(x(f,"*"))h=q(h,l[d],f["*"]);else return Object.defineProperty(_,d,{configurable:!0,enumerable:!0,get(){return s[d]},set(P){s[d]=P}}),h;return _[d]=h,h},set(T,d,O,h){return d in _?_[d]=O:s[d]=O,!0},defineProperty(T,d,O){return Reflect.defineProperty(_,d,O)},deleteProperty(T,d){return Reflect.deleteProperty(_,d)}},S=Object.create(s);return new Proxy(S,w)},$=s=>({addListener(l,f,..._){l.addListener(s.get(f),..._)},hasListener(l,f){return l.hasListener(s.get(f))},removeListener(l,f){l.removeListener(s.get(f))}}),ve=new o(s=>typeof s!="function"?s:function(f){let _=q(f,{},{getContent:{minArgs:0,maxArgs:0}});s(_)}),ee=new o(s=>typeof s!="function"?s:function(f,_,w){let S=!1,T,d=new Promise(F=>{T=function(E){S=!0,F(E)}}),O;try{O=s(f,_,T)}catch(F){O=Promise.reject(F)}let h=O!==!0&&u(O);if(O!==!0&&!h&&!S)return!1;let P=F=>{F.then(E=>{w(E)},E=>{let W;E&&(E instanceof Error||typeof E.message=="string")?W=E.message:W="An unexpected error occurred",w({__mozWebExtensionPolyfillReject__:!0,message:W})}).catch(E=>{console.error("Failed to send onMessage rejected reply",E)})};return P(h?O:d),!0}),Se=({reject:s,resolve:l},f)=>{n.runtime.lastError?n.runtime.lastError.message===t?l():s(new Error(n.runtime.lastError.message)):f&&f.__mozWebExtensionPolyfillReject__?s(new Error(f.message)):l(f)},te=(s,l,f,..._)=>{if(_.length<l.minArgs)throw new Error(`Expected at least ${l.minArgs} ${g(l.minArgs)} for ${s}(), got ${_.length}`);if(_.length>l.maxArgs)throw new Error(`Expected at most ${l.maxArgs} ${g(l.maxArgs)} for ${s}(), got ${_.length}`);return new Promise((w,S)=>{let T=Se.bind(null,{resolve:w,reject:S});_.push(T),f.sendMessage(..._)})},Te={devtools:{network:{onRequestFinished:$(ve)}},runtime:{onMessage:$(ee),onMessageExternal:$(ee),sendMessage:te.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:te.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},Q={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return i.privacy={network:{"*":Q},services:{"*":Q},websites:{"*":Q}},q(n,Te,i)};e.exports=r(chrome)}else e.exports=globalThis.browser})});var L=B(H(),1);function I(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var Pe=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return b(t)},e.prototype.toString=function(){return"None"},e}(),m=new Pe;Object.freeze(m);var je=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return y(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return p(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(I(this.value),")")},e.EMPTY=new e(void 0),e}(),y=je,j;(function(e){function t(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];for(var u=[],c=0,g=i;c<g.length;c++){var a=g[c];if(a.isSome())u.push(a.value);else return a}return y(u)}e.all=t;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];for(var u=0,c=i;u<c.length;u++){var g=c[u];return g.isSome(),g}return m}e.any=r;function n(i){return i instanceof y||i===m}e.isOption=n})(j||(j={}));var Ne=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(I(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(I(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new b(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return m},e.prototype.toString=function(){return"Err(".concat(I(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new K(this)},e.EMPTY=new e(void 0),e}();var b=Ne,De=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(I(this.value)),{cause:this.value})},e.prototype.map=function(t){return new p(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return y(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(I(this.value),")")},e.prototype.toAsyncResult=function(){return new K(this)},e.EMPTY=new e(void 0),e}();var p=De,N;(function(e){function t(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];for(var g=[],a=0,A=u;a<A.length;a++){var x=A[a];if(x.isOk())g.push(x.value);else return x}return new p(g)}e.all=t;function r(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];for(var g=[],a=0,A=u;a<A.length;a++){var x=A[a];if(x.isOk())return x;g.push(x.error)}return new b(g)}e.any=r;function n(u){try{return new p(u())}catch(c){return new b(c)}}e.wrap=n;function i(u){try{return u().then(function(c){return new p(c)}).catch(function(c){return new b(c)})}catch(c){return Promise.resolve(new b(c))}}e.wrapAsync=i;function o(u){return u instanceof b||u instanceof p}e.isResult=o})(N||(N={}));var oe=function(e,t,r,n){function i(o){return o instanceof r?o:new r(function(u){u(o)})}return new(r||(r=Promise))(function(o,u){function c(A){try{a(n.next(A))}catch(x){u(x)}}function g(A){try{a(n.throw(A))}catch(x){u(x)}}function a(A){A.done?o(A.value):i(A.value).then(c,g)}a((n=n.apply(e,t||[])).next())})},ie=function(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,i,o,u;return u={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function c(a){return function(A){return g([a,A])}}function g(a){if(n)throw new TypeError("Generator is already executing.");for(;u&&(u=0,a[0]&&(r=0)),r;)try{if(n=1,i&&(o=a[0]&2?i.return:a[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,a[1])).done)return o;switch(i=0,o&&(a=[a[0]&2,o.value]),a[0]){case 0:case 1:o=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,i=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(a[0]===6||a[0]===2)){r=0;continue}if(a[0]===3&&(!o||a[1]>o[0]&&a[1]<o[3])){r.label=a[1];break}if(a[0]===6&&r.label<o[1]){r.label=o[1],o=a;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(a);break}o[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(A){a=[6,A],i=0}finally{n=o=0}if(a[0]&5)throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},K=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(n){return oe(r,void 0,void 0,function(){var i;return ie(this,function(o){return n.isErr()?[2,n]:(i=t(n.value),[2,i instanceof e?i.promise:i])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(n){return oe(r,void 0,void 0,function(){var i;return ie(this,function(o){switch(o.label){case 0:return n.isErr()?[2,n]:(i=p,[4,t(n.value)]);case 1:return[2,i.apply(void 0,[o.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();var ae=(e,t)=>typeof e[t]=="string";function M(e){try{if(ae(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return p(e.__serializer_value);if(e.__serializer_tag==="regex"){let n=new RegExp(e.__serializer_value);return p(n)}else if(e.__serializer_tag==="array"){let n=[];for(let i of e.__serializer_value){let o=M(i);if(o.isErr())return o;n.push(o.unwrap())}return p(n)}else if(e.__serializer_tag==="map"){let n=[];for(let i of e.__serializer_value){let o=M(i);if(o.isErr())return o;n.push(o.unwrap())}return p(new Map(n))}else if(e.__serializer_tag==="set"){let n=[];for(let i of e.__serializer_value){let o=M(i);if(o.isErr())return o;n.push(o.unwrap())}return p(new Set(n))}else if(e.__serializer_tag==="result_ok"){let n=e.__serializer_value,i=M(n);return i.isErr()?i:p(p(i.unwrap()))}else if(e.__serializer_tag==="result_err"){let n=e.__serializer_value,i=M(n);return i.isErr()?i:p(b(i.unwrap()))}else if(e.__serializer_tag==="option_some"){let n=e.__serializer_value,i=M(n);return i.isErr()?i:p(y(i.unwrap()))}else if(e.__serializer_tag==="option_none")return p(m)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return b("This object was not serialized with Serialize");let r={};for(let n of Object.keys(e))if(typeof n=="string"){let i=M(e[n]);if(i.isErr())return i;r[n]=i.unwrap()}return p(r)}catch{return b("Failed to inspect object. Not JSON?")}}function C(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return p({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return p({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(o=>C(o)),n=r.as_iter().find(o=>o.isErr());if(n.isSome())return n.unwrap();let i=r.as_iter().map(o=>o.unwrap()).toArray();return p({__serializer_tag:"array",__serializer_value:i})}else if(e instanceof Map){let r=[...e.entries()].map(o=>C(o)),n=r.as_iter().find(o=>o.isErr());if(n.isSome())return n.unwrap();let i=r.as_iter().map(o=>o.unwrap()).toArray();return p({__serializer_tag:"map",__serializer_value:i})}else if(e instanceof Set){let r=[...e.values()].map(o=>C(o)),n=r.as_iter().find(o=>o.isErr());if(n.isSome())return n.unwrap();let i=r.as_iter().map(o=>o.unwrap()).toArray();return p({__serializer_tag:"set",__serializer_value:i})}else if(N.isResult(e))if(e.isOk()){let r=e.unwrap(),n=C(r);return n.isErr()?n:p({__serializer_tag:"result_ok",__serializer_value:n.unwrap()})}else{let r=e.unwrapErr(),n=C(r);return n.isErr()?n:p({__serializer_tag:"result_err",__serializer_value:n.unwrap()})}else if(j.isOption(e))if(e.isSome()){let r=e.unwrap(),n=C(r);return n.isErr()?n:p({__serializer_tag:"option_some",__serializer_value:n.unwrap()})}else return p({__serializer_tag:"option_none"});else if(t==="object"){let r={},n=e;for(let i of Object.keys(e)){let o=n[i],u=C(o);if(u.isErr())continue;let c=u.unwrap();r[i]=c}return p(r)}else return b("Unsupported value")}function v(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return y(r);return m},count:function(t){return this.reduce((r,n)=>(t(n)&&r++,r),0)},reduce:function(t,r){let n=r;for(let i of this)n=t(n,i);return n},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>y(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?y(r):m)},enumerate:function(){let t=this;return v(function*(){let r=0;for(let n of t)yield[r,n],r++})()},filterMap:function(t){let r=this;return v(function*(){for(let n of r){let i=t(n);i.isSome()&&(yield i.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});var D=/.^/,se={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:D,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:D,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:D,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:D,defacto_container:"Mp4"}},le={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:D,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:D,defacto_container:"Mp4"}},ue=v(function*(){for(let e of Object.keys(se))yield se[e]}),ce=v(function*(){for(let e of Object.keys(le))yield le[e]});var me={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:ue().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:ce().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:y("MP3"),video:y("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:y("AAC"),video:m},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:y("FLAC"),video:m},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:y("MP3"),video:y("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:m,video:m},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:y("Wav"),video:m},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},Re=v(function*(){for(let e of Object.keys(me))yield e}),Ct=v(function*(){for(let e of Re())yield me[e]});var de={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};var Fe=v(function*(){for(let e of Object.keys(de))yield e}),jt=v(function*(){for(let e of Fe())yield de[e]});var Y=B(H(),1);function R(){return{template:"%title",max_length:64}}async function pe(e,t){let r=e.variants.values().next().value,n=e.page_title,i=new URL(e.page_url).hostname,o=i.replace(/\.com$|\.net$|\.org$/,""),u=new URL(r.manifest_url).pathname.split("/").pop()||"none",c="";if(n.length<4?n=o:n.length<8&&(n+="-"+o),u.includes(".")){let a=u.split(".");a.pop(),u=a.join(".")}if(!t){let a=await V(k);t=a.get(i)||a.get("*"),t||(console.error("Missing '*' rule"),t=R())}try{if(t.selector&&e.tab_id!="none"){let a={tabId:e.tab_id};c=(await Y.default.scripting.executeScript({target:a,world:Y.default.scripting.ExecutionWorld.MAIN,args:[t.selector],func:x=>document.querySelector(x)?.textContent}))[0]?.result}}catch{}let g=t.template.replaceAll("%title",n).replaceAll("%hostname",o).replaceAll("%pathname",u).replaceAll("%selector",c);return g.length<3&&(g=o),g.trim().normalize("NFD").replace(/\./gu," ").replace(/[^\p{L}\p{N}\-\s]/ug,"").replace(/-+/gu,"-").replace(/\s+/gu," ").substring(0,t.max_length)}async function Z(e,t){let r=t;e.hooks&&(r=e.hooks.setter(t)),await L.storage[e.where].set({[e.name]:r})}async function V(e){let t=await L.storage[e.where].get(e.name);if(e.name in t){let r=t[e.name];return e.hooks?e.hooks.getter(r,e):r}return e.default()}async function fe(e){await L.storage[e.where].remove(e.name)}var ge={name:"view_options",default:()=>({}),where:"session"};var k={name:"smartnaming",where:"local",default:()=>new Map([["*",R()]]),hooks:{setter:e=>C(e).unwrap(),getter:(e,t)=>M(e).unwrapOr(t.default())}},_e={name:"database",where:"session",default:()=>({yt_bulk:m,user_messages:new Set,coapp_status:"checking",license_status:{checking:!0},current_tab_id:0,current_window_id:0,downloadable:new Map,downloading:new Map,downloaded:new Map,download_errors:new Map}),hooks:{setter:e=>C(e).unwrap(),getter:(e,t)=>M(e).unwrapOr(t.default())}};var xe=B(H(),1);function ye(e){return e?e.replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll('"',"&quot;").replaceAll("'","&#039;"):""}function Ae(e,t,r){let n=()=>(console.error(`Requesting unknown i18n string ${e}`),e);t=t.map(i=>i.toString()).map(ye);try{if(e in r){let i=r[e],o=1;for(let u=0;u<t.length;u++)i=i.replace(`$${o}`,t[u]);return i}else{let i=xe.default.i18n.getMessage(e,t);return i||n()}}catch{return n()}}function he(e,t){for(let r of Array.from(e.querySelectorAll("[data-i18n]"))){let n=r.dataset.i18nArgs,i=r.dataset.i18nAttr,o;n?o=Ae(r.dataset.i18n,JSON.parse(n),t):o=Ae(r.dataset.i18n,[],t),i?r.setAttribute(i,o):r.textContent=o}}var ze=new URL(document.location.toString()).searchParams,Je=ze.get("id"),be=(await V(_e)).downloadable.get(Je),U=new URL(be.page_url).hostname;async function X(){let e=await V(k),t=e.get(U)||e.get("*")||R();for(let r of Array.from(document.querySelectorAll(".hostname")))r.textContent=U;document.querySelector("#template").value=t.template,document.querySelector("#selector").value=t.selector||"",document.querySelector("#max-length").value=t.max_length.toString(),document.querySelector("#result").value=""}function we(){let e=document.querySelector("#template").value,t=parseInt(document.querySelector("#max-length").value),r=document.querySelector("#selector").value,n=R();return n.template=e,t&&(n.max_length=t),r&&(n.selector=r),n}async function qe(e){let t=e.target;if(t){let r=t.closest("#button-reset"),n=t.closest("#button-reset-all"),i=t.closest("#button-save"),o=t.closest("#button-save-all"),u=t.closest("#button-test");if(n&&(await fe(k),X()),r){let c=await V(k);c.delete(U),await Z(k,c),X()}if(i||o){let c=await V(k),g=we(),a=U;o&&(delete g.selector,a="*"),c.set(a,g),Z(k,c)}if(u){let c=we(),g=await pe(be,c);document.querySelector("#result").value=g}}}X();var He=await V(ge);he(document,He);window.addEventListener("click",qe);
