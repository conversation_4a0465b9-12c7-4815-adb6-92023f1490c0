<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <style>

      body {
        margin: 0;
        font-family: sans-serif;
      }

      header {
        padding: 10px 0 10px 42px;
        background-repeat: no-repeat;
        background-position: 8px center;
        background-size: 23px;
        font-weight: bold;
        font-size: 1.2em;
        background-color: #ECECEC;
        background-image: url("/content2/icons/stable-color.png");
      }

      html[channel="beta"] header {
        background-image: url("/content2/icons/beta-color.png");
      }

      html[channel="dev"] header {
        background-image: url("/content2/icons/dev-color.png");
      }

      main {
        max-width: 800px;
        margin: 20px auto;
      }

      main > * {
        margin: 10px 10px 0 10px;
      }

      main > section > * {
        margin: 10px 0 0 0;
      }

      #saved-toaster {
        min-width: 70px;
        background-color: #99cc33;
        border: 0px solid #063902;
        border-radius: 3px;
        position: fixed;
        bottom: 10px;
        right: 10px;
        text-align: center;
        padding: 7px 10px;
        color: white;
        transition: transform 200ms;
        pointer-events: none;
      }

      #saved-toaster.hidden {
        transform: translateY(50px);
      }

      em {
        font-size: 0.8em;
        opacity: 0.8;
      }

      vbox {
        display: flex;
        flex-direction: row;
      }

    </style>
  </head>
  <body>
    <header data-i18n="mup_page_title"></header>
    <main>
      <section></section>
      <button id="reset_button" data-i18n="mup_reset"></button>
      <div id="saved-toaster" class="hidden" data-i18n="mup_saved"></div>
    </main>
    <script src="media-user-prefs-edit.js" type="module"></script>
  </body>
</html>
