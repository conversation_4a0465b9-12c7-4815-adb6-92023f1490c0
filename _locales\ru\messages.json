{"Bytes": {"message": "$1 Байт"}, "GB": {"message": "$1 ГБ"}, "KB": {"message": "$1 КБ"}, "MB": {"message": "$1 МБ"}, "about": {"message": "О программе"}, "about_alpha_extra7_fx": {"message": "Из-за внутренних технических изменений в Firefox дополнение должно быть полностью переработано. Пожалуйста, подождите пару недель, чтобы стали доступны все возможности из предыдущих версий."}, "about_alpha_intro": {"message": "Это альфа-версия дополнения."}, "about_beta_intro": {"message": "Это бета-версия дополнения."}, "about_chrome_licenses": {"message": "О лицензиях Chrome"}, "about_qr": {"message": "Файл сгенерирован"}, "about_vdh": {"message": "О дополнении"}, "action_abort_description": {"message": "Отменить текущее действие"}, "action_abort_title": {"message": "Отмена"}, "action_as_default": {"message": "Использовать это действие по умолчанию"}, "action_avplay_description": {"message": "Воспроизвести видео с помощью встроенного проигрывателя конвертора"}, "action_avplay_title": {"message": "Воспроизвести"}, "action_blacklist_description": {"message": "Мультимедиа файлы из выбранных доменов будут игнорироваться"}, "action_blacklist_title": {"message": "Добавить в чёрный список"}, "action_bulkdownload_description": {"message": "Скачать выбранные видео"}, "action_bulkdownload_title": {"message": "Скачать пачкой"}, "action_bulkdownloadconvert_description": {"message": "Скачать пачкой и переконв-ть выбранное"}, "action_bulkdownloadconvert_title": {"message": "Скачать пачкой и переконв-ть"}, "action_copyurl_description": {"message": "Скопировать URL видео файла в буфер обмена"}, "action_copyurl_title": {"message": "Копировать URL"}, "action_deletehit_description": {"message": "Удалить просмотренную страницу из текущего списка"}, "action_deletehit_title": {"message": "Удалить"}, "action_details_description": {"message": "Показать детали просмотра страницы"}, "action_details_title": {"message": "Детали"}, "action_download_description": {"message": "Загрузить файл на ваш жесткий диск"}, "action_download_title": {"message": "Загрузка"}, "action_downloadaudio_description": {"message": "Скачать только аудио"}, "action_downloadaudio_title": {"message": "Скачать звуковую дорожку"}, "action_downloadconvert_description": {"message": "Загрузить видео и сконвертировать в другой формат"}, "action_downloadconvert_title": {"message": "Загрузить и сконвертировать"}, "action_openlocalcontainer_description": {"message": "Открыть локальную папку файлов"}, "action_openlocalcontainer_title": {"message": "Открыть папку"}, "action_openlocalfile_description": {"message": "Открыть локальный видео файл"}, "action_openlocalfile_title": {"message": "Открыть видео"}, "action_pin_description": {"message": "Оставаться на странице"}, "action_pin_title": {"message": "Закрепить"}, "action_quickdownload_description": {"message": "Загрузить не спрашивая о том, куда сохранять"}, "action_quickdownload_title": {"message": "Быстрая загрузка"}, "action_quickdownloadaudio_description": {"message": "Скачать только аудио, не спрашивая о месте сохранения"}, "action_quickdownloadaudio_title": {"message": "Быстрая закачка аудио"}, "action_quicksidedownload_description": {"message": "Стороняя закачка без указания места сохранения"}, "action_quicksidedownload_title": {"message": "Быстрая сторонняя закачка"}, "action_sidedownload_description": {"message": "Экспериментальная закачка"}, "action_sidedownload_title": {"message": "Стороняя закачка"}, "action_sidedownloadconvert_description": {"message": "Сторонняя закачка медиа и конвертация в другой формат"}, "action_sidedownloadconvert_title": {"message": "Стороняя закачка и конвертация"}, "action_stop_description": {"message": "Остановить захват"}, "action_stop_title": {"message": "Остановка"}, "adaptative": {"message": "Адаптивный $1"}, "add_to_blacklist": {"message": "Добавить в чёрный список"}, "add_to_blacklist_help": {"message": "Мультимедиа файлы из выбранных доменов будут игнорироваться"}, "advanced": {"message": "Расширенные"}, "aggregating": {"message": "Сборка..."}, "analyze_page": {"message": "Анализировать страницу"}, "appDesc": {"message": "Скачать видео из интернета"}, "appName": {"message": "Video DownloadHelper"}, "appearance": {"message": "Вн<PERSON><PERSON>ний вид"}, "audio_only": {"message": "Только аудио дорожка"}, "behavior": {"message": "Поведение"}, "blacklist": {"message": "Чёрный список"}, "blacklist_add_domain": {"message": "Добавить домен в черный список"}, "blacklist_add_placeholder": {"message": "Домен для включения в черный список"}, "blacklist_edit_descr": {"message": "Черный список позволяет игнорировать контент, поступающий с некоторых доменов"}, "blacklist_empty": {"message": "Нет доменов в черном списке"}, "browser_info": {"message": "Браузер $1 $2 $3"}, "browser_locale": {"message": "Локаль броузера: $1"}, "build_options": {"message": "Опции компиляции: $1"}, "built_on": {"message": "Скомпилировано $1"}, "bulk_in_progress": {"message": "Выполняется пакетная операция Video DownloadHelper. Не закрывайте вкладку, это будет сделано автоматически"}, "bulk_n_videos": {"message": "$1 видео"}, "cancel": {"message": "Отмена"}, "change": {"message": "Изменить"}, "chrome_basic_mode": {"message": "Chrome Basic (рекомендовано обновление)"}, "chrome_inapp_descr_premium_lifetime": {"message": "Premium статус без ограничения по времени"}, "chrome_inapp_descr_premium_monthly": {"message": "Premium статус из месячной подписки"}, "chrome_inapp_descr_premium_yearly": {"message": "Premium статус из годовой подписки"}, "chrome_inapp_no_subs": {"message": "Примечание: после прекращения поддержки Google платежных сервисов Chrome подписки больше не доступны."}, "chrome_inapp_not_avail": {"message": "Недоступно"}, "chrome_inapp_premium_lifetime": {"message": "Пожизненный Premium"}, "chrome_inapp_premium_monthly": {"message": "Месячная Premium подписка"}, "chrome_inapp_premium_yearly": {"message": "Годовая Premium подписка"}, "chrome_install_firefox": {"message": "Установите Firefox"}, "chrome_install_fx_vdh": {"message": "Video DownloadHelper для Firefox"}, "chrome_license_webstore_accepted": {"message": "Активная лицензия из интернет-магазина Chrome"}, "chrome_licensing": {"message": "Лицензирование Chrome"}, "chrome_noyt_text": {"message": "К сожалению, Chrome Web Store не позволяет расширениям загружать YouTube-видео, поэтому нам пришлось убрать такую возможность"}, "chrome_noyt_text2": {"message": "Вы можете использовать Video DownloadHelper для скачивания YouTube-видео, используя Firefox версию"}, "chrome_noyt_text3": {"message": "К сожалению, интернет-магазин Chrome не допускает расширений для загрузки видео с YouTube, потому эта функция отсутствует в версии дополнения для Chrome."}, "chrome_premium_audio": {"message": "Создание только-аудио файлов доступно только в режиме Premium"}, "chrome_premium_check_error": {"message": "Ошибка проверки статуса Premium"}, "chrome_premium_hls": {"message": "Без статуса Premium загрузка HLS может быть произведена только через $1 минут после предыдущей"}, "chrome_premium_mode": {"message": "Chrome Premium"}, "chrome_premium_need_sign": {"message": "Вам нужно выполнить вход в Chrome, чтобы получить преимущества режима Premium"}, "chrome_premium_not_signed": {"message": "Не выполнен вход в Chrome"}, "chrome_premium_recheck": {"message": "Перепроверить статус Premium"}, "chrome_premium_required": {"message": "Требуется статус Premium"}, "chrome_premium_source": {"message": "Вы являетесь премиальным пользователем с помощью $1"}, "chrome_product_intro": {"message": "Вы можете повысить статус до Premium используя любую из опций:"}, "chrome_req_review": {"message": "Как вариант, не напишете ли милый отзыв в Chrome WebStore?"}, "chrome_signing_in": {"message": "Выполняется вход в Chrome"}, "chrome_verif_premium": {"message": "Проверка статуса Premium"}, "chrome_verif_premium_error": {"message": "API платежей через приложение не доступен"}, "chrome_warning_yt": {"message": "Предупреждать о вынужденных ограничениях функций расширений браузера Chrome при работе с YouTube"}, "clear": {"message": "Очистить"}, "clear_hits": {"message": "Очистить хиты"}, "clear_logs": {"message": "Очистить логи"}, "coapp": {"message": "Дополнение-спутник"}, "coapp_error": {"message": "Результат проверки дополнения-спутника:"}, "coapp_found": {"message": "Найдено приложение-компаньон:"}, "coapp_help": {"message": "Нажмите здесь, чтобы устранить неполадку"}, "coapp_install": {"message": "Установить приложение-компаньон"}, "coapp_installed": {"message": "Приложение-компаньон установлено"}, "coapp_latest_version": {"message": "Последняя доступная версия: $1"}, "coapp_not_installed": {"message": "Приложение-компаньон не установлено"}, "coapp_outdated": {"message": "Версия дополнения-спутника устарела, требуется обновление"}, "coapp_outofdate": {"message": "Версия дополнения-спутника критически устарела, требуется полная замена новейшей версией "}, "coapp_outofdate_text": {"message": "Установлена версия $1 дополнения-спутника, однако данная функция требует обновления до версии $2"}, "coapp_path": {"message": "Исполняемый файл компаньона:"}, "coapp_recheck": {"message": "Перепроверить"}, "coapp_required": {"message": "Требуется установить дополнение-спутник"}, "coapp_required_text": {"message": "Для завершения данной операции требуется наличие внешнего приложения"}, "coapp_shell": {"message": "Командная среда дополнения-спутника"}, "coapp_unchecked": {"message": "Идет проверка параметров дополнения-спутника"}, "coapp_update": {"message": "Обновить дополнение-спутник"}, "collecting": {"message": "Сбор..."}, "confirmation_required": {"message": "Требуется подтверждение"}, "congratulations": {"message": "Поздравляем!"}, "continue": {"message": "Далее"}, "convconf_2passes": {"message": "в 2 прохода"}, "convconf_ac": {"message": "Аудио-каналы"}, "convconf_acnone": {"message": "Пусто"}, "convconf_acodec": {"message": "Кодек аудио"}, "convconf_aspect": {"message": "Соотношение сторон"}, "convconf_audiobitrate": {"message": "Битрейт аудио"}, "convconf_audiofreq": {"message": "Частота аудио"}, "convconf_audioonly": {"message": "Только аудио"}, "convconf_bitrate": {"message": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "convconf_container": {"message": "Формат"}, "convconf_duplicate": {"message": "Дубли<PERSON>а<PERSON>"}, "convconf_ext": {"message": "Расширение выходного файла"}, "convconf_extra": {"message": "Доп. параметры"}, "convconf_level": {"message": "Уровень"}, "convconf_mono": {"message": "Моно"}, "convconf_new": {"message": "Новый"}, "convconf_preset": {"message": "Пресет"}, "convconf_profilev": {"message": "Профиль видео"}, "convconf_rate": {"message": "Частота кадров"}, "convconf_readonly": {"message": "Конфигурацию по умолчанию нельзя изменить. Сделайте ее копию для внесения изменений."}, "convconf_remove": {"message": "Удалить"}, "convconf_reset": {"message": "Сбросить всё"}, "convconf_reset_confirm": {"message": "Это удалит все Ваши настройки"}, "convconf_save": {"message": "Сохранить"}, "convconf_size": {"message": "Размер кадра"}, "convconf_stereo": {"message": "Стерео"}, "convconf_target": {"message": "Цель"}, "convconf_tune": {"message": "Настройка"}, "convconf_vcodec": {"message": "Кодек видео"}, "convconf_videobitrate": {"message": "Битрейт видео"}, "conversion_create_rule": {"message": "Создать правило"}, "conversion_outputs": {"message": "Параметры конвертирования видео"}, "conversion_rules": {"message": "Правила конвертации"}, "conversion_update_rule": {"message": "Обновить правило"}, "convert": {"message": "Конвертировать"}, "convert_local_files": {"message": "Конвертация локальных файлов"}, "converter_needed_aggregate": {"message": "Эта операция требует наличия конвертора, установленного в Вашей системе, для сведения видео и аудио потоков"}, "converter_needed_aggregate_why": {"message": "Почему мне необходим конвертор?"}, "converter_needs_reg": {"message": "Требуется регистрация"}, "converter_queued": {"message": "Ожидание очереди на конвертацию..."}, "converter_reg_audio": {"message": "Вы запросили создание \"только-аудио\" файла напрямую или через правило автоматической конвертации. Для этого требуется зарегистрировать конвертор."}, "converting": {"message": "Конвертирование..."}, "convrule_convert": {"message": "Конвертировать"}, "convrule_domain": {"message": "До<PERSON><PERSON>н"}, "convrule_extension": {"message": "Расширение"}, "convrule_format": {"message": "в формат $1"}, "convrule_from_domain": {"message": "из домена $1"}, "convrule_no_convert": {"message": "Не конвертировать"}, "convrule_output_format": {"message": "Выходной формат"}, "convrule_refresh_formats": {"message": "Обновить форматы выходного потока"}, "convrule_with_ext": {"message": "с расширением '$1'"}, "convrules_add_rule": {"message": "Создать новое правило конвертации"}, "convrules_edit_descr": {"message": "Правила конвертации разрешают автоматическое преобразование медиа сразу после загрузки"}, "convrules_empty": {"message": "Правила конвертации не заданы"}, "copy_of": {"message": "Копия $1"}, "copy_settings_info_to_clipboard": {"message": "Скопировать информацию в буфер обмена"}, "copy_settings_info_to_clipboard_success": {"message": "Информация скопирована в буфер обмена"}, "corrupted_media_file": {"message": "Невозможно извлечь данные медиа '$1' из файла '$2'. Вероятно, файл поврежден."}, "create": {"message": "Создать"}, "custom_output": {"message": "Ручной выбор формата вывода"}, "dash_streaming": {"message": "Потоковый DASH"}, "default": {"message": "По умолчанию"}, "details_parenthesis": {"message": "(подробнее)"}, "dev_build": {"message": "Инженерная версия программы"}, "dialog_audio_impossible": {"message": "Этот тип медиафайла не позволяет скачать только звук"}, "dialog_audio_impossible_title": {"message": "Невозможно скачать аудио"}, "directory_not_exist": {"message": "Несуществующая папка"}, "directory_not_exist_body": {"message": "Папка '$1' не существует. Создать ее?"}, "dlconv_download_and_convert": {"message": "Скачать и Конвертировать"}, "dlconv_output_details": {"message": "Детальная настройка выходных данных"}, "donate": {"message": "Пожертвования"}, "donate_vdh": {"message": "Справка по Video DownloadHelper"}, "download_error": {"message": "Ошибка загрузки"}, "download_method": {"message": "Метод загрузки"}, "download_method_not_again": {"message": "Использовать этот метод по умолчанию отныне"}, "download_modes1": {"message": "Загрузка может осуществляться браузером или приложением-компаньоном"}, "download_modes2": {"message": "По техническим причинам, при скачивании с помощью сервисов браузера загрузка может быть отменена сервером, на котором размещено видео, а также не возможно определить альтернативную папку скачивания по умолчанию"}, "download_with_browser": {"message": "Использовать браузер"}, "download_with_coapp": {"message": "Использовать приложение-компаньон"}, "downloading": {"message": "Скачивание..."}, "edge_req_review": {"message": "Или же, не хотите ли написать отличный отзыв в магазине расширений Microsoft Edge?"}, "error": {"message": "Ошибка"}, "error_not_directory": {"message": "'$1' существует, но не является директорией"}, "errors": {"message": "Ошибки"}, "exit_natmsgsh": {"message": "Выйти из дополнения-спутника"}, "explain_qr1": {"message": "Вы заметите, что итоговое видео содержит водяной знак в углу."}, "explain_qr2": {"message": "Это вызвано тем, что Вы выбрали вариант ADP и конвертор не зарегистрирован."}, "export": {"message": "Экспорт"}, "failed_aggregating": {"message": "Ошибка свода \"$1\""}, "failed_converting": {"message": "Ошибка конвертирования \"$1\""}, "failed_getting_info": {"message": "Ошибка в получении информации из \"$1\""}, "failed_opening_directory": {"message": "Ошибка открытия директории содержащей файл"}, "failed_playing_file": {"message": "Ошибка воспроизведения файла"}, "file_dialog_date": {"message": "Дата"}, "file_dialog_name": {"message": "Имя"}, "file_dialog_size": {"message": "Размер"}, "file_generated": {"message": "Файл \"$1\" сгенерирован."}, "file_ready": {"message": "\"$1\" готов"}, "finalizing": {"message": "Завершение..."}, "from_domain": {"message": "Источник $1"}, "gallery": {"message": "Галерея"}, "gallery_files_types": {"message": "$1 файлов"}, "gallery_from_domain": {"message": "Галерея с $1"}, "gallery_links_from_domain": {"message": "Ссылка с $1"}, "general": {"message": "Общие"}, "get_conversion_license": {"message": "Получить лицензию на конвертацию"}, "help_translating": {"message": "Помочь с переводом"}, "hit_details": {"message": "Детали"}, "hit_go_to_tab": {"message": "Перейти на вкладку"}, "hls_streaming": {"message": "Потоковый HLS"}, "homepage": {"message": "Домашняя страница"}, "import": {"message": "Импорт"}, "import_invalid_format": {"message": "Неверный формат"}, "in_current_tab": {"message": "В текущей вкладке"}, "in_other_tab": {"message": "В других вкладках"}, "lic_mismatch1": {"message": "Лицензия для $1, но сборка браузера не была указана"}, "lic_mismatch2": {"message": "Лицензия для $1, но сборка браузера для $2"}, "lic_not_needed_linux": {"message": "Наш вклад в Linux: лицензия не требуется"}, "lic_status_accepted": {"message": "Лицензия проверена"}, "lic_status_blocked": {"message": "Лицензия блокирована"}, "lic_status_error": {"message": "Ошибка обработки лицензии"}, "lic_status_locked": {"message": "Лицензия приостановлена (требуется перепроверка подлинности)"}, "lic_status_mismatch": {"message": "Лицензия/браузер не совпадают"}, "lic_status_nocoapp": {"message": "Отказ проверки подлинности лицензии"}, "lic_status_unneeded": {"message": "Лицензия не требуется"}, "lic_status_unset": {"message": "Лицензия не установлена"}, "lic_status_unverified": {"message": "Лицензия не проходила проверку подлинности"}, "lic_status_verifying": {"message": "Идет проверка подлинности лицензии..."}, "license": {"message": "Лицензия"}, "license_key": {"message": "Лицензионный ключ"}, "licensing": {"message": "Лицензирование"}, "live_stream": {"message": "прямая трансляция"}, "logs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "media": {"message": "Мультимедиа"}, "merge_error": {"message": "Ошибка сведения"}, "merge_local_files": {"message": "Сведение локальных аудио и видео файлов"}, "more": {"message": "Ещё..."}, "mup_best_video_quality": {"message": "Предпочитаемое качество видео"}, "mup_ignore_low_quality": {"message": "Игнорировать видео в низком качестве"}, "mup_ignore_low_quality_help": {"message": "Некоторые страницы содержат медиафайлы «низкого качества», которые используются только для создания «эффектов», например, файл WAV для звука щелчка или короткое видео для небольшой анимации на странице."}, "mup_ignored_containers": {"message": "Не показывать медиафайлы с контейнерами"}, "mup_ignored_video_codecs": {"message": "Не показывать медиафайлы с кодеками"}, "mup_lowest_video_quality": {"message": "Игнорировать следующее видео"}, "mup_max_variants": {"message": "Количество вариантов"}, "mup_max_variants_help": {"message": "Каждое видео имеет несколько форматов. Мы показываем сначала лучшую версию, а затем также несколько альтернативных форматов (вариантов)."}, "mup_page_title": {"message": "Медиа-предпочтения"}, "mup_prefer_60fps": {"message": "Предпочтительная частота кадров 60 и выше"}, "mup_prefered_container": {"message": "Предпочтительный формат контенера"}, "mup_prefered_video_codecs": {"message": "Предпочтительный видео-кодек"}, "mup_reset": {"message": "сбросить"}, "mup_saved": {"message": "сохранено!"}, "network_error_no_response": {"message": "Ошибка сети - нет ответа сервера"}, "network_error_status": {"message": "Ошибка сети - состояние $1"}, "new_sub_directory": {"message": "Создать подпапку"}, "next": {"message": "Далее"}, "no": {"message": "Нет"}, "no_audio_in_file": {"message": "В файле $1 аудиопоток не найден"}, "no_coapp_license_unverified": {"message": "Лицензия не могла быть проверена, так как дополнение-спутник не установлено."}, "no_license_registered": {"message": "Лицензий не зарегистрировано"}, "no_media_current_tab": {"message": "Текущая вкладка пуста"}, "no_media_to_process": {"message": "Отсутствует мультимедиа для обработки"}, "no_media_to_process_descr": {"message": "Запустите проигрывание видео для его обнаружения"}, "no_such_hit": {"message": "Нет такого потока"}, "no_validate_without_coapp": {"message": "Для проверки лицензии требуется установка дополнения-спутника"}, "no_video_in_file": {"message": "В файле $1 видеопоток не найден"}, "not_again_3months": {"message": "Не беспокойте меня об этом в течении 3 месяцев"}, "not_see_again": {"message": "Не показывать данное сообщение снова"}, "number_type": {"message": "$1 $2"}, "ok": {"message": "???"}, "orphan": {"message": "Без вкладки"}, "output_configuration": {"message": "Конфигурация выходного потока"}, "overwrite_file": {"message": "Подтверждаете перезапись файла '$1' ?"}, "per_month": {"message": "/ месяц"}, "per_year": {"message": "/ год"}, "pinned": {"message": "Закреплённый"}, "platform": {"message": "Платформа"}, "platform_info": {"message": "Платформа $1 $2"}, "powered_by_weh": {"message": "Создано благодаря технологиям Weh"}, "preferences": {"message": "Предпочтения"}, "prod_build": {"message": "Технический билд"}, "quality_medium": {"message": "Среднее качество"}, "quality_small": {"message": "Низкое качество"}, "queued": {"message": "В очереди..."}, "recheck_license": {"message": "Перепроверить лицензию"}, "register_converter": {"message": "Регистрация конвертора"}, "register_existing_license": {"message": "Зарегистрировать существующую лицензию"}, "registered_email": {"message": "Адрес электронной почты"}, "registered_key": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "reload_addon": {"message": "Перезапустить расширение"}, "reload_addon_confirm": {"message": "Вы уверены что хотите перезапустить расширение ?"}, "req_donate": {"message": "Не хотите ли поддержать разработку и сколько-нибудь пожертвовать?"}, "req_locale": {"message": "Или помочь с переводом интерфейса расширения '$1' (имеется $2 пропущенных строк) ?"}, "req_review": {"message": "Или же, не хотите ли написать отличный отзыв на сайте дополнений Mozilla ?"}, "req_review_link": {"message": "Написать отзыв о Video DownloadHelper"}, "reset_settings": {"message": "Сброс настроек"}, "running": {"message": "Выполняется"}, "save": {"message": "Сохранить"}, "save_as": {"message": "Сохранить как..."}, "save_file_as": {"message": "Сохранить файл как..."}, "select_audio_file_to_merge": {"message": "Выбрать файл содержащий аудиоряд"}, "select_files_to_convert": {"message": "Конвертация локальных файлов"}, "select_output_config": {"message": "Выбрать конфигурацию выходного потока"}, "select_output_directory": {"message": "Директория сохранения выходного потока"}, "select_video_file_to_merge": {"message": "Выбрать файл содержащий видеоряд"}, "selected_media": {"message": "Выбор источников для массовой загрузки"}, "settings": {"message": "Настройки"}, "smartname_add_domain": {"message": "Добавить интеллектуальный шаблон формирования названия"}, "smartname_create_rule": {"message": "Создать правило"}, "smartname_define": {"message": "Задать интеллектуальный шаблон формирования названия"}, "smartname_edit_descr": {"message": "Добавление интеллектуального шаблона позволяет формировать название выходного файла в соответствии с частью названия хоста загрузки"}, "smartname_empty": {"message": "Не задавать интеллектуальный шаблон формирования названия"}, "smartname_update_rule": {"message": "Обновить правило"}, "smartnamer_delay": {"message": "Задержка именования захвата (мс)"}, "smartnamer_domain": {"message": "До<PERSON><PERSON>н"}, "smartnamer_get_name_from_header_url": {"message": "Получить имя из заголовка документа/URL"}, "smartnamer_get_name_from_page_content": {"message": "Получить имя из содержимого страницы"}, "smartnamer_get_name_from_page_title": {"message": "Получить имя из заголовка страницы"}, "smartnamer_get_obfuscated_name": {"message": "Использовать случайные имена"}, "smartnamer_regexp": {"message": "Регулярное выражение"}, "smartnamer_selected_text": {"message": "Выделенный текст"}, "smartnamer_xpath_expr": {"message": "Выражение XPath"}, "smartnaming_rule": {"message": "Интеллектуальный шаблон формирования названия"}, "smartnaming_rules": {"message": "Интеллектуальные шаблоны формирования названия"}, "sub_directory_name": {"message": "Название подпапки"}, "support_forum": {"message": "Форум техподдержки"}, "supported_sites": {"message": "Поддерживаемые сайты"}, "tbsn_quality_hd": {"message": "Среднее качество"}, "tbsn_quality_sd": {"message": "Низкое качество"}, "tell_me_more": {"message": "Рассказать больше об этом"}, "title": {"message": "Video DownloadHelper"}, "translation": {"message": "Перевод"}, "up": {"message": "Ввер<PERSON>"}, "v9_about_qr": {"message": "Файл сгенерирован"}, "v9_chrome_noyt_text2": {"message": "Вы можете использовать Video DownloadHelper для скачивания YouTube-видео, используя Firefox версию"}, "v9_chrome_noyt_text3": {"message": "К сожалению, интернет-магазин Chrome не допускает расширений для загрузки видео с YouTube, потому эта функция отсутствует в версии дополнения для Chrome."}, "v9_chrome_premium_hls": {"message": "Без статуса Premium загрузка HLS может быть произведена только через $1 минут после предыдущей"}, "v9_chrome_premium_required": {"message": "Требуется статус Premium"}, "v9_chrome_warning_yt": {"message": "Предупреждать о вынужденных ограничениях функций расширений браузера Chrome при работе с YouTube"}, "v9_coapp_help": {"message": "Нажмите здесь, чтобы устранить неполадку"}, "v9_coapp_install": {"message": "Установить приложение-компаньон"}, "v9_coapp_installed": {"message": "Приложение-компаньон установлено"}, "v9_coapp_not_installed": {"message": "Приложение-компаньон не установлено"}, "v9_coapp_outdated": {"message": "Версия дополнения-спутника устарела, требуется обновление"}, "v9_coapp_recheck": {"message": "Перепроверить"}, "v9_coapp_required": {"message": "Требуется установить дополнение-спутник"}, "v9_coapp_required_text": {"message": "Для завершения данной операции требуется наличие внешнего приложения"}, "v9_coapp_unchecked": {"message": "Идет проверка параметров дополнения-спутника"}, "v9_coapp_update": {"message": "Обновить дополнение-спутник"}, "v9_converter_needs_reg": {"message": "Требуется регистрация"}, "v9_converter_reg_audio": {"message": "Вы запросили создание \"только-аудио\" файла напрямую или через правило автоматической конвертации. Для этого требуется зарегистрировать конвертор."}, "v9_copy_settings_info_to_clipboard": {"message": "Скопировать информацию в буфер обмена"}, "v9_dialog_audio_impossible": {"message": "Этот тип медиафайла не позволяет скачать только звук"}, "v9_dialog_audio_impossible_title": {"message": "Невозможно скачать аудио"}, "v9_error": {"message": "Ошибка"}, "v9_explain_qr1": {"message": "Вы заметите, что итоговое видео содержит водяной знак в углу."}, "v9_file_ready": {"message": "\"$1\" готов"}, "v9_get_conversion_license": {"message": "Получить лицензию на конвертацию"}, "v9_lic_mismatch2": {"message": "Лицензия для $1, но сборка браузера для $2"}, "v9_lic_status_accepted": {"message": "Лицензия проверена"}, "v9_lic_status_blocked": {"message": "Лицензия блокирована"}, "v9_lic_status_locked": {"message": "Лицензия приостановлена (требуется перепроверка подлинности)"}, "v9_lic_status_unset": {"message": "Лицензия не установлена"}, "v9_lic_status_verifying": {"message": "Идет проверка подлинности лицензии..."}, "v9_mup_max_variants": {"message": "Количество вариантов"}, "v9_no": {"message": "Нет"}, "v9_no_license_registered": {"message": "Лицензий не зарегистрировано"}, "v9_no_media_current_tab": {"message": "Текущая вкладка пуста"}, "v9_no_media_to_process_descr": {"message": "Запустите проигрывание видео для его обнаружения"}, "v9_no_validate_without_coapp": {"message": "Для проверки лицензии требуется установка дополнения-спутника"}, "v9_not_see_again": {"message": "Не показывать данное сообщение снова"}, "v9_settings": {"message": "Настройки"}, "v9_tell_me_more": {"message": "Рассказать больше об этом"}, "v9_vdh_notification": {"message": "Video DownloadHelper"}, "v9_weh_prefs_description_contextMenuEnabled": {"message": "Доступ к меню команд по правому клику мышью на целевой странице"}, "v9_weh_prefs_label_downloadControlledMax": {"message": "Максимальное количество параллельных загрузок"}, "v9_yes": {"message": "Да"}, "validate_license": {"message": "Зарегистрировать лицензию"}, "variants_list_adp": {"message": "Адаптивные варианты"}, "variants_list_full": {"message": "Варианты"}, "vdh_notification": {"message": "Video DownloadHelper"}, "version": {"message": "Версия $1"}, "video_only": {"message": "Только видеоряд"}, "video_qualities": {"message": "Качество"}, "weh_prefs_alertDialogType_option_panel": {"message": "Окно"}, "weh_prefs_alertDialogType_option_tab": {"message": "Вкладка"}, "weh_prefs_coappDownloads_option_ask": {"message": "Спросить перед загрузкой о предпочитаемом способе"}, "weh_prefs_coappDownloads_option_browser": {"message": "Загрузить с помощью браузера"}, "weh_prefs_coappDownloads_option_coapp": {"message": "Загрузить с помощью дополнения-спутника"}, "weh_prefs_dashOnAdp_option_audio": {"message": "Скачать аудио"}, "weh_prefs_dashOnAdp_option_audio_video": {"message": "Объединить аудио и видео потоки"}, "weh_prefs_dashOnAdp_option_video": {"message": "Скачать видео"}, "weh_prefs_description_adpHide": {"message": "Не показывать варианты ADP в листе загрузок"}, "weh_prefs_description_alertDialogType": {"message": "Способ отображения диалоговых окон при выводе информации о сбоях"}, "weh_prefs_description_autoPin": {"message": "Закрепить просмотренную страницу после закачки"}, "weh_prefs_description_avplayEnabled": {"message": "Разрешить воспроизведение непосредственно дополнением-спутником"}, "weh_prefs_description_blacklistEnabled": {"message": "Не искать видео на определенных сайтах"}, "weh_prefs_description_bulkEnabled": {"message": "Разрешить блочные операции загрузки"}, "weh_prefs_description_checkCoappOnStartup": {"message": "При активации дополнения, проверять доступность дополнения-спутника с целью улучшения распознавания медиапотоков"}, "weh_prefs_description_chunkedCoappDataRequests": {"message": "Запрашивать фрагментированные данные при помощи дополнения-спутника"}, "weh_prefs_description_chunkedCoappManifestsRequests": {"message": "Запрашивать фрагментированные манифесты при помощи дополнения-спутника"}, "weh_prefs_description_chunksConcurrentDownloads": {"message": "Максимальное количество параллельно загружаемых сегментов"}, "weh_prefs_description_chunksEnabled": {"message": "Поточная передача данных по кускам включена"}, "weh_prefs_description_chunksPrefetchCount": {"message": "Количество сегментов потока подлежащих предзагрузке"}, "weh_prefs_description_coappDownloads": {"message": "Приложение, непосредственно осуществляющее загрузку"}, "weh_prefs_description_coappIdleExit": {"message": "Автоматически закрывать после числа миллисекунд, 0 - отключено"}, "weh_prefs_description_coappRestartDelay": {"message": "Задержка в миллисекундах перед перезапуском дополнения-спутника"}, "weh_prefs_description_coappUseProxy": {"message": "Дополнение-спутник использует те же параметры прокси, что и при первем запросе источника"}, "weh_prefs_description_contentRedirectEnabled": {"message": "Некоторые сайты могут возвращать новый URL вместо медиа содержимого"}, "weh_prefs_description_contextMenuEnabled": {"message": "Доступ к меню команд по правому клику мышью на целевой странице"}, "weh_prefs_description_convertControlledMax": {"message": "Максимум допустимых параллельных процессов слияния или конвертации медиаданных"}, "weh_prefs_description_converterAggregTuneH264": {"message": "??? Bad translation"}, "weh_prefs_description_converterKeepTmpFiles": {"message": "Не удалять временные файлы после обработки"}, "weh_prefs_description_converterThreads": {"message": "Количество потоков при операции конвертации"}, "weh_prefs_description_dashEnabled": {"message": "Разрешить обработку потоковой передачи DASH"}, "weh_prefs_description_dashHideM4s": {"message": "Не показывать имеющиеся .m4s в листе загрузок"}, "weh_prefs_description_dashOnAdp": {"message": "Если поток в DASH содержит и аудио и видео компоненту"}, "weh_prefs_description_dialogAutoClose": {"message": "Автоматическое закрытие диалоговых окон, при снятии с них выделения"}, "weh_prefs_description_downloadControlledMax": {"message": "Контролирует количество одновременных загрузок, запущенных приложением, чтобы обеспечить некоторую пропускную способность."}, "weh_prefs_description_downloadRetries": {"message": "Количество попыток скачивания"}, "weh_prefs_description_downloadRetryDelay": {"message": "Задержка между повторными попытками скачивания (в миллисекундах)"}, "weh_prefs_description_downloadStreamControlledMax": {"message": "Максимальное количество потоков загрузки одного элемента"}, "weh_prefs_description_fileDialogType": {"message": "Как отображать файловые диалоги"}, "weh_prefs_description_galleryNaming": {"message": "Способ назначения имен файлов при сохранении из галереи"}, "weh_prefs_description_hitsGotoTab": {"message": "Отображать в общем списке доступных загрузок прямую ссылку на закладку-источник видео для быстрого переключения"}, "weh_prefs_description_hlsDownloadAsM2ts": {"message": "Загрузить поток HLS как M2TS"}, "weh_prefs_description_hlsEnabled": {"message": "Разрешить обработку потоков HLS"}, "weh_prefs_description_hlsEndTimeout": {"message": "Перерыв в ожидании новых фрагментов HLS, в секундах"}, "weh_prefs_description_hlsRememberPrevLiveChunks": {"message": "Запомнить предыдущий HLS фрагмент"}, "weh_prefs_description_iconActivation": {"message": "Условия активации иконки дополнения в панели инструментов браузера"}, "weh_prefs_description_iconBadge": {"message": "Отображение иконки дополнения в панели инструментов браузера"}, "weh_prefs_description_ignoreProtectedVariants": {"message": "Не показывать защищенные варианты"}, "weh_prefs_description_lastDownloadDirectory": {"message": "Используется только с процессором загрузки Companion App"}, "weh_prefs_description_mediaExtensions": {"message": "Расширения, которые "}, "weh_prefs_description_medialinkAutoDetect": {"message": "Выполнять при каждом обновлении или открытии страницы (может ухудшить скорость работы)"}, "weh_prefs_description_medialinkExtensions": {"message": "Типы расширений файлов, которые следует захватывать как изображения галереи"}, "weh_prefs_description_medialinkMaxHits": {"message": "Ограничение максимального количества элементов, которые следует определять как отдельную галерею"}, "weh_prefs_description_medialinkMinFilesPerGroup": {"message": "Указание минимального количества элементов, которые следует определять как отдельную галерею"}, "weh_prefs_description_medialinkMinImgSize": {"message": "Минимальный размер изображения для галереи"}, "weh_prefs_description_medialinkScanImages": {"message": "Распознавать и захватывать изображения на страницах"}, "weh_prefs_description_medialinkScanLinks": {"message": "Распознавать только те медиаданные, источником которых является только непосредственно открытая страница"}, "weh_prefs_description_mediaweightMinSize": {"message": "Игнорировать определения меньше этого размера"}, "weh_prefs_description_mediaweightThreshold": {"message": "Принудительно определять максимально возможный размер видеопотока"}, "weh_prefs_description_monitorNetworkRequests": {"message": "Загружать те же заголовки медиа<PERSON><PERSON><PERSON><PERSON><PERSON>, что отображались при первом обращении к ним"}, "weh_prefs_description_mpegtsHideTs": {"message": "Не показывать имеющиеся .ts в листе загрузок"}, "weh_prefs_description_networkFilterOut": {"message": "Регулярное выражение для игнорирования некоторых адресов видео"}, "weh_prefs_description_networkProbe": {"message": "Сканировать сетевой трафик для обнаружения кросс-ссылок"}, "weh_prefs_description_noPrivateNotification": {"message": "Не отображать персональные подсказки"}, "weh_prefs_description_notifyReady": {"message": "Уведомить после завершения"}, "weh_prefs_description_orphanExpiration": {"message": "Задержка в секундах перед удалением кросс-ссылок не имеющих явного продолжения потока"}, "weh_prefs_description_qualitiesMaxVariants": {"message": "Максимальное количество показываемых вариантов одного видео"}, "weh_prefs_description_rememberLastDir": {"message": "Использовать последнюю директорию как место по умолчанию"}, "weh_prefs_description_smartnamerFnameMaxlen": {"message": "Обеспечивает длину сгенерированного имени файла, не превышающую данной величины"}, "weh_prefs_description_smartnamerFnameSpaces": {"message": "Как поступать с пробелами в названии видео"}, "weh_prefs_description_tbsnEnabled": {"message": "Разрешить распознавание и загрузку Facebook видео"}, "weh_prefs_description_titleMode": {"message": "Как отображать длинные названия видео на главной панели"}, "weh_prefs_description_toolsMenuEnabled": {"message": "Доступ к командам из меню Инструменты"}, "weh_prefs_description_use_native_filepicker": {"message": "Использовать системное средство выбора файлов"}, "weh_prefs_fileDialogType_option_panel": {"message": "Окно"}, "weh_prefs_fileDialogType_option_tab": {"message": "Вкладка"}, "weh_prefs_galleryNaming_option_index_url": {"message": "Индекс - URL"}, "weh_prefs_galleryNaming_option_type_index": {"message": "Тип - Ин<PERSON>е<PERSON>с"}, "weh_prefs_galleryNaming_option_url": {"message": "URL"}, "weh_prefs_iconActivation_option_anytab": {"message": "Определение изо всех вкладок"}, "weh_prefs_iconActivation_option_currenttab": {"message": "Определение из текущей вкладки"}, "weh_prefs_iconBadge_option_activetab": {"message": "Видео с текущей вкладки"}, "weh_prefs_iconBadge_option_anytab": {"message": "Видео с любой вкладки"}, "weh_prefs_iconBadge_option_mixed": {"message": "Смешанные"}, "weh_prefs_iconBadge_option_none": {"message": "Пусто"}, "weh_prefs_iconBadge_option_pinned": {"message": "Закрепленные просмотренные страницы"}, "weh_prefs_iconBadge_option_tasks": {"message": "Запущенные задачи"}, "weh_prefs_label_adpHide": {"message": "Скрывать предложение загрузки файлов ADP"}, "weh_prefs_label_alertDialogType": {"message": "Диалоговое окно при сообщениях о сбое"}, "weh_prefs_label_autoPin": {"message": "Закрепить автоматически"}, "weh_prefs_label_avplayEnabled": {"message": "Воспроизведение разрешено"}, "weh_prefs_label_blacklistEnabled": {"message": "Включить черный список"}, "weh_prefs_label_bulkEnabled": {"message": "Разрешить скачивание массивов медиа"}, "weh_prefs_label_checkCoappOnStartup": {"message": "Проверить дополнение-спутник сразу при запуске"}, "weh_prefs_label_chunkedCoappDataRequests": {"message": "Разрешить запрос на скачивание отрывков медиа дополнением-спутником"}, "weh_prefs_label_chunkedCoappManifestsRequests": {"message": "Разрешить запрос на обработку манифестов отрывков медиа дополнением-спутником"}, "weh_prefs_label_chunksConcurrentDownloads": {"message": "Параллельное скачивания по кускам"}, "weh_prefs_label_chunksEnabled": {"message": "Поточная передача данных по кускам"}, "weh_prefs_label_chunksPrefetchCount": {"message": "Предварительный подсчёт параметров фрагментов"}, "weh_prefs_label_coappDownloads": {"message": "Обработчик загрузки"}, "weh_prefs_label_coappIdleExit": {"message": "Время до автоотключения дополнения-спутника при простое"}, "weh_prefs_label_coappRestartDelay": {"message": "Время до автоматического перезапуска дополнения-спутника при простое"}, "weh_prefs_label_coappUseProxy": {"message": "Параметры прокси для дополнения-спутника"}, "weh_prefs_label_contentRedirectEnabled": {"message": "Включить перенаправление контента"}, "weh_prefs_label_contextMenuEnabled": {"message": "Контекстное меню"}, "weh_prefs_label_convertControlledMax": {"message": "Количество одновременных операций преобразования"}, "weh_prefs_label_converterAggregTuneH264": {"message": "Настройка H264"}, "weh_prefs_label_converterKeepTmpFiles": {"message": "Сохранять временные файлы"}, "weh_prefs_label_converterThreads": {"message": "Количество проходов при конвертировании видео"}, "weh_prefs_label_dashEnabled": {"message": "Разрешить DASH"}, "weh_prefs_label_dashHideM4s": {"message": "Скрыть контент с расширением .m4s"}, "weh_prefs_label_dashOnAdp": {"message": "Потоки DASH"}, "weh_prefs_label_dialogAutoClose": {"message": "Автоматически закрывать диалоги"}, "weh_prefs_label_downloadControlledMax": {"message": "Максимальное количество параллельных загрузок"}, "weh_prefs_label_downloadRetries": {"message": "Попыток загрузки"}, "weh_prefs_label_downloadRetryDelay": {"message": "Задержка перед следующей повторной попыткой загрузки"}, "weh_prefs_label_downloadStreamControlledMax": {"message": "Максимальное количество одновременных закачек из нефайловых источников - медиапотока"}, "weh_prefs_label_fileDialogType": {"message": "Диалоговое окно файловых операций"}, "weh_prefs_label_galleryNaming": {"message": "Название файла галереи"}, "weh_prefs_label_hitsGotoTab": {"message": "Показывать управление вкладки \"Go-to-Tab\""}, "weh_prefs_label_hlsDownloadAsM2ts": {"message": "HLS как M2TS"}, "weh_prefs_label_hlsEnabled": {"message": "Разрешить HLS"}, "weh_prefs_label_hlsEndTimeout": {"message": "Окончание перерыва в работе HLS"}, "weh_prefs_label_hlsRememberPrevLiveChunks": {"message": "История HLS"}, "weh_prefs_label_iconActivation": {"message": "Активация иконки"}, "weh_prefs_label_iconBadge": {"message": "Метка иконки"}, "weh_prefs_label_ignoreProtectedVariants": {"message": "Игнорировать защищенные варианты"}, "weh_prefs_label_lastDownloadDirectory": {"message": "Директория загрузки по умолчанию"}, "weh_prefs_label_mediaExtensions": {"message": "Определить расширение"}, "weh_prefs_label_medialinkAutoDetect": {"message": "Автоопределение галерей"}, "weh_prefs_label_medialinkExtensions": {"message": "Расширения ссылок на медиаданные"}, "weh_prefs_label_medialinkMaxHits": {"message": "Максимально допустимое количество распознаваемых объектов"}, "weh_prefs_label_medialinkMinFilesPerGroup": {"message": "Минимально требующееся количество распознанных объектов для последующего отображения"}, "weh_prefs_label_medialinkMinImgSize": {"message": "Минимальный размер изображения"}, "weh_prefs_label_medialinkScanImages": {"message": "Определять встроенные изображения"}, "weh_prefs_label_medialinkScanLinks": {"message": "Определить ссылки на видео"}, "weh_prefs_label_mediaweightMinSize": {"message": "Минимальный размер"}, "weh_prefs_label_mediaweightThreshold": {"message": "Предельное значение объема"}, "weh_prefs_label_monitorNetworkRequests": {"message": "Заголовки запросов"}, "weh_prefs_label_mpegtsHideTs": {"message": "Скрыть .ts"}, "weh_prefs_label_networkFilterOut": {"message": "Отключение сетевого фильтра"}, "weh_prefs_label_networkProbe": {"message": "Сетевое сканирование"}, "weh_prefs_label_noPrivateNotification": {"message": "Частные уведомления"}, "weh_prefs_label_notifyReady": {"message": "Предупреждение"}, "weh_prefs_label_orphanExpiration": {"message": "Прерывание в связи с истечением времени ожидания продолжения потока"}, "weh_prefs_label_qualitiesMaxVariants": {"message": "Максимальное количество вариантов"}, "weh_prefs_label_rememberLastDir": {"message": "Запоминать последнюю директорию"}, "weh_prefs_label_smartnamerFnameMaxlen": {"message": "Максимальная длина имени файла"}, "weh_prefs_label_smartnamerFnameSpaces": {"message": "Длиные заголовки в основной панели"}, "weh_prefs_label_tbsnEnabled": {"message": "Поддержка Facebook"}, "weh_prefs_label_tbvwsExtractionMethod": {"message": "Метод извлечения"}, "weh_prefs_label_titleMode": {"message": "Длиные заголовки в основной панели"}, "weh_prefs_label_toolsMenuEnabled": {"message": "Меню инструментов"}, "weh_prefs_label_use_native_filepicker": {"message": "Использовать собственное средство выбора файлов"}, "weh_prefs_smartnamerFnameSpaces_option_hyphen": {"message": "Заменить дефисами"}, "weh_prefs_smartnamerFnameSpaces_option_keep": {"message": "Оставить"}, "weh_prefs_smartnamerFnameSpaces_option_remove": {"message": "Убрать"}, "weh_prefs_smartnamerFnameSpaces_option_underscore": {"message": "Заменить символами подчеркивания"}, "weh_prefs_titleMode_option_left": {"message": "Троеточие слева"}, "weh_prefs_titleMode_option_multiline": {"message": "В несколько строк"}, "weh_prefs_titleMode_option_right": {"message": "Троеточие справа"}, "yes": {"message": "Да"}, "you_downloaded_n_videos": {"message": "Вы только что успешно загрузили Ваш $1th файл с помощью Video DownloadHelper."}}