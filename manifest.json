{"action": {"default_icon": {"128": "/content2/icons/stable-color.png"}, "default_popup": "/content2/popup.html", "default_title": "Video DownloadHelper"}, "author": "<PERSON>", "background": {"service_worker": "background/main.js"}, "commands": {"_execute_action": {"suggested_key": {"default": "Alt+Up", "mac": "Ctrl+Shift+Up"}}, "default-action": {"description": "Execute default action", "suggested_key": {"default": "Alt+Down", "mac": "Ctrl+Shift+Down"}}}, "content_scripts": [{"js": ["injected/downloadhelper.net.js"], "matches": ["*://*.downloadhelper.net/*"], "run_at": "document_start"}, {"js": ["injected/changelog.js"], "matches": ["*://*.downloadhelper.net/changelog/*"], "run_at": "document_end"}, {"js": ["injected/debugger.js"], "matches": ["*://*.downloadhelper.net/debugger"], "run_at": "document_end"}], "default_locale": "en_US", "description": "__MSG_appDesc__", "host_permissions": ["<all_urls>"], "icons": {"128": "/content2/icons/stable-color.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqm/Cdcx4ikjVJbadUGpwrgJXdkc6Is8smuMiuAQbzgOg0cETVxuAY55UxgCxsh3lu4Os0RFK11bMBhu79Jbwv8tyml6mFGLLncLU2dFPZwnZWnIt5U+KC+uOvjryOacIPLxS/PeF2sXScNd0QJg/wTyl5WIizFtbcM3ZmFDFsj39FuUoH/OhO1Ux1/4Xn6dbSRb5F6qgkS2CU9ptTPQz0NJNaEmbL7vnWQkl2C8N4SjlDpRf/lC5r72Ow16hmbNmCGRhbxjPF0Dm+EO2uyF6dBw4n9V8BjBXQ7AdL5J3k4dXJk/oIOD2GQJsGyHK6TobNPPjy6W7bpPBtSfesIXS+wIDAQAB", "manifest_version": 3, "name": "Video DownloadHelper", "optional_permissions": ["browsingData", "downloads.open"], "permissions": ["tabs", "webRequest", "downloads", "webNavigation", "notifications", "scripting", "storage", "contextMenus", "nativeMessaging", "declarativeNetRequest", "sidePanel"], "side_panel": {"default_icon": {"128": "/content2/icons/stable-color.png"}, "default_path": "/content2/sidebar.html", "default_title": "Video DownloadHelper"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "*******"}