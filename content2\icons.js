function u(D,S){let{size:t,spread:p,radius:a,greyed:P,channel:I}=D,x=JSON.stringify({instructions:D}),E=S.get(x);if(E)return E;let r=new OffscreenCanvas(t,t).getContext("2d");r.lineCap="round";let n=t/2-5,d=t/2,l=p*Math.sin(Math.PI/6),i=p*Math.cos(Math.PI/6),o=(C,B,G,_,A)=>{r.save(),r.globalAlpha=C,r.beginPath(),r.arc(B,G,A,0,2*Math.PI,!1),r.fillStyle=_,r.fill(),r.closePath(),r.restore()},v=r.createLinearGradient(t,t*.5,t*.5,t*.5);v.addColorStop(.1652,"#A6DEEF"),v.addColorStop(.3949,"#6CC5F0"),v.addColorStop(.8805,"#355BAA");let f=r.createLinearGradient(t*.3,t*.25,t*.6,t*.6);f.addColorStop(0,"#FFF200"),f.addColorStop(1,"#FFCE07");let g=r.createLinearGradient(t*.5,t*.5,t*.5,t);g.addColorStop(0,"#EC223B"),g.addColorStop(.2577,"#E42339"),g.addColorStop(.492,"#D42634"),g.addColorStop(.7172,"#BD292C"),g.addColorStop(.9354,"#9E2B22"),g.addColorStop(1,"#942B1F");let m=.2,F="#666";if(I=="beta"&&(F="green"),I=="dev"&&(F="#F06"),o(1,n+p,d,F,a*(1+m)),o(1,n-l,d-i,F,a*(1+m)),o(1,n-l,d+i,F,a*(1+m)),r.globalCompositeOperation="destination-out",o(1,n+p,d,"#FFF",a*(1-m)),o(1,n-l,d-i,"#FFF",a*(1-m)),o(1,n-l,d+i,"#FFF",a*(1-m)),r.globalCompositeOperation="source-over",!P)o(1,n+p,d,"white",a),o(1,n-l,d-i,"white",a),o(1,n-l,d+i,"white",a),o(1,n+p,d,v,a),o(.9,n-l,d-i,f,a),o(.85,n-l,d+i,g,a);else{let C=r.createLinearGradient(0,0,t,t);C.addColorStop(0,"#333"),C.addColorStop(1,"#CCC"),o(1,n+p,d,"white",a),o(1,n-l,d-i,"white",a),o(1,n-l,d+i,"white",a),o(.2,n+p,d,C,a),o(.2,n-l,d-i,C,a),o(.2,n-l,d+i,C,a)}let M=r.getImageData(0,0,t,t);return S.set(x,M),M}var b=new Map,c=128,y=35,w=25,s=u({size:c,radius:y,spread:w,greyed:!1,channel:"stable"},b),e=document.createElement("canvas");e.width=e.height=c;document.body.appendChild(e);var h=e.getContext("2d");h.putImageData(s,0,0);s=u({size:c,radius:y,spread:w,greyed:!1,channel:"beta"},b);e=document.createElement("canvas");e.width=e.height=c;document.body.appendChild(e);h=e.getContext("2d");h.putImageData(s,0,0);s=u({size:c,radius:y,spread:w,greyed:!1,channel:"dev"},b);e=document.createElement("canvas");e.width=e.height=c;document.body.appendChild(e);h=e.getContext("2d");h.putImageData(s,0,0);s=u({size:c,radius:y,spread:w,greyed:!0,channel:"stable"},b);e=document.createElement("canvas");e.width=e.height=c;document.body.appendChild(e);h=e.getContext("2d");h.putImageData(s,0,0);s=u({size:c,radius:y,spread:w,greyed:!0,channel:"beta"},b);e=document.createElement("canvas");e.width=e.height=c;document.body.appendChild(e);h=e.getContext("2d");h.putImageData(s,0,0);s=u({size:c,radius:y,spread:w,greyed:!0,channel:"dev"},b);e=document.createElement("canvas");e.width=e.height=c;document.body.appendChild(e);h=e.getContext("2d");h.putImageData(s,0,0);
