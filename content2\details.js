var pe=Object.create;var G=Object.defineProperty;var fe=Object.getOwnPropertyDescriptor;var ge=Object.getOwnPropertyNames;var _e=Object.getPrototypeOf,ye=Object.prototype.hasOwnProperty;var Ae=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var xe=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of ge(t))!ye.call(e,n)&&n!==r&&G(e,n,{get:()=>t[n],enumerable:!(o=fe(t,n))||o.enumerable});return e};var K=(e,t,r)=>(r=e!=null?pe(_e(e)):{},xe(t||!e||!e.__esModule?G(r,"default",{value:e,enumerable:!0}):r,e));var Q=Ae((q,X)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof q<"u")t(X);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:q,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=o=>{let n={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(n).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class i extends WeakMap{constructor(s,f=void 0){super(f),this.createItem=s}get(s){return this.has(s)||this.set(s,this.createItem(s)),super.get(s)}}let u=a=>a&&typeof a=="object"&&typeof a.then=="function",m=(a,s)=>(...f)=>{o.runtime.lastError?a.reject(new Error(o.runtime.lastError.message)):s.singleCallbackArg||f.length<=1&&s.singleCallbackArg!==!1?a.resolve(f[0]):a.resolve(f)},y=a=>a==1?"argument":"arguments",l=(a,s)=>function(g,...h){if(h.length<s.minArgs)throw new Error(`Expected at least ${s.minArgs} ${y(s.minArgs)} for ${a}(), got ${h.length}`);if(h.length>s.maxArgs)throw new Error(`Expected at most ${s.maxArgs} ${y(s.maxArgs)} for ${a}(), got ${h.length}`);return new Promise((S,T)=>{if(s.fallbackToNoCallback)try{g[a](...h,m({resolve:S,reject:T},s))}catch(d){console.warn(`${a} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,d),g[a](...h),s.fallbackToNoCallback=!1,s.noCallback=!0,S()}else s.noCallback?(g[a](...h),S()):g[a](...h,m({resolve:S,reject:T},s))})},A=(a,s,f)=>new Proxy(s,{apply(g,h,S){return f.call(h,a,...S)}}),w=Function.call.bind(Object.prototype.hasOwnProperty),z=(a,s={},f={})=>{let g=Object.create(null),h={has(T,d){return d in a||d in g},get(T,d,O){if(d in g)return g[d];if(!(d in a))return;let x=a[d];if(typeof x=="function")if(typeof s[d]=="function")x=A(a,a[d],s[d]);else if(w(f,d)){let P=l(d,f[d]);x=A(a,a[d],P)}else x=x.bind(a);else if(typeof x=="object"&&x!==null&&(w(s,d)||w(f,d)))x=z(x,s[d],f[d]);else if(w(f,"*"))x=z(x,s[d],f["*"]);else return Object.defineProperty(g,d,{configurable:!0,enumerable:!0,get(){return a[d]},set(P){a[d]=P}}),x;return g[d]=x,x},set(T,d,O,x){return d in g?g[d]=O:a[d]=O,!0},defineProperty(T,d,O){return Reflect.defineProperty(g,d,O)},deleteProperty(T,d){return Reflect.deleteProperty(g,d)}},S=Object.create(a);return new Proxy(S,h)},$=a=>({addListener(s,f,...g){s.addListener(a.get(f),...g)},hasListener(s,f){return s.hasListener(a.get(f))},removeListener(s,f){s.removeListener(a.get(f))}}),ce=new i(a=>typeof a!="function"?a:function(f){let g=z(f,{},{getContent:{minArgs:0,maxArgs:0}});a(g)}),W=new i(a=>typeof a!="function"?a:function(f,g,h){let S=!1,T,d=new Promise(D=>{T=function(M){S=!0,D(M)}}),O;try{O=a(f,g,T)}catch(D){O=Promise.reject(D)}let x=O!==!0&&u(O);if(O!==!0&&!x&&!S)return!1;let P=D=>{D.then(M=>{h(M)},M=>{let L;M&&(M instanceof Error||typeof M.message=="string")?L=M.message:L="An unexpected error occurred",h({__mozWebExtensionPolyfillReject__:!0,message:L})}).catch(M=>{console.error("Failed to send onMessage rejected reply",M)})};return P(x?O:d),!0}),me=({reject:a,resolve:s},f)=>{o.runtime.lastError?o.runtime.lastError.message===t?s():a(new Error(o.runtime.lastError.message)):f&&f.__mozWebExtensionPolyfillReject__?a(new Error(f.message)):s(f)},B=(a,s,f,...g)=>{if(g.length<s.minArgs)throw new Error(`Expected at least ${s.minArgs} ${y(s.minArgs)} for ${a}(), got ${g.length}`);if(g.length>s.maxArgs)throw new Error(`Expected at most ${s.maxArgs} ${y(s.maxArgs)} for ${a}(), got ${g.length}`);return new Promise((h,S)=>{let T=me.bind(null,{resolve:h,reject:S});g.push(T),f.sendMessage(...g)})},de={devtools:{network:{onRequestFinished:$(ce)}},runtime:{onMessage:$(W),onMessageExternal:$(W),sendMessage:B.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:B.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},J={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return n.privacy={network:{"*":J},services:{"*":J},websites:{"*":J}},z(o,de,n)};e.exports=r(chrome)}else e.exports=globalThis.browser})});function V(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var he=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return b(t)},e.prototype.toString=function(){return"None"},e}(),c=new he;Object.freeze(c);var we=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return _(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return p(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(V(this.value),")")},e.EMPTY=new e(void 0),e}(),_=we,I;(function(e){function t(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];for(var u=[],m=0,y=n;m<y.length;m++){var l=y[m];if(l.isSome())u.push(l.value);else return l}return _(u)}e.all=t;function r(){for(var n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];for(var u=0,m=n;u<m.length;u++){var y=m[u];return y.isSome(),y}return c}e.any=r;function o(n){return n instanceof _||n===c}e.isOption=o})(I||(I={}));var be=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(V(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(V(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new b(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return c},e.prototype.toString=function(){return"Err(".concat(V(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new H(this)},e.EMPTY=new e(void 0),e}();var b=be,ve=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(V(this.value)),{cause:this.value})},e.prototype.map=function(t){return new p(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return _(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(V(this.value),")")},e.prototype.toAsyncResult=function(){return new H(this)},e.EMPTY=new e(void 0),e}();var p=ve,j;(function(e){function t(){for(var u=[],m=0;m<arguments.length;m++)u[m]=arguments[m];for(var y=[],l=0,A=u;l<A.length;l++){var w=A[l];if(w.isOk())y.push(w.value);else return w}return new p(y)}e.all=t;function r(){for(var u=[],m=0;m<arguments.length;m++)u[m]=arguments[m];for(var y=[],l=0,A=u;l<A.length;l++){var w=A[l];if(w.isOk())return w;y.push(w.error)}return new b(y)}e.any=r;function o(u){try{return new p(u())}catch(m){return new b(m)}}e.wrap=o;function n(u){try{return u().then(function(m){return new p(m)}).catch(function(m){return new b(m)})}catch(m){return Promise.resolve(new b(m))}}e.wrapAsync=n;function i(u){return u instanceof b||u instanceof p}e.isResult=i})(j||(j={}));var Y=function(e,t,r,o){function n(i){return i instanceof r?i:new r(function(u){u(i)})}return new(r||(r=Promise))(function(i,u){function m(A){try{l(o.next(A))}catch(w){u(w)}}function y(A){try{l(o.throw(A))}catch(w){u(w)}}function l(A){A.done?i(A.value):n(A.value).then(m,y)}l((o=o.apply(e,t||[])).next())})},Z=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},o,n,i,u;return u={next:m(0),throw:m(1),return:m(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function m(l){return function(A){return y([l,A])}}function y(l){if(o)throw new TypeError("Generator is already executing.");for(;u&&(u=0,l[0]&&(r=0)),r;)try{if(o=1,n&&(i=l[0]&2?n.return:l[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,l[1])).done)return i;switch(n=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,n=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){r.label=l[1];break}if(l[0]===6&&r.label<i[1]){r.label=i[1],i=l;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(l);break}i[2]&&r.ops.pop(),r.trys.pop();continue}l=t.call(e,r)}catch(A){l=[6,A],n=0}finally{o=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}},H=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(o){return Y(r,void 0,void 0,function(){var n;return Z(this,function(i){return o.isErr()?[2,o]:(n=t(o.value),[2,n instanceof e?n.promise:n])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(o){return Y(r,void 0,void 0,function(){var n;return Z(this,function(i){switch(i.label){case 0:return o.isErr()?[2,o]:(n=p,[4,t(o.value)]);case 1:return[2,n.apply(void 0,[i.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();function v(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return _(r);return c},count:function(t){return this.reduce((r,o)=>(t(o)&&r++,r),0)},reduce:function(t,r){let o=r;for(let n of this)o=t(o,n);return o},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>_(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?_(r):c)},enumerate:function(){let t=this;return v(function*(){let r=0;for(let o of t)yield[r,o],r++})()},filterMap:function(t){let r=this;return v(function*(){for(let o of r){let n=t(o);n.isSome()&&(yield n.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});var se=K(Q(),1);var ee=(e,t)=>typeof e[t]=="string";function E(e){try{if(ee(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return p(e.__serializer_value);if(e.__serializer_tag==="regex"){let o=new RegExp(e.__serializer_value);return p(o)}else if(e.__serializer_tag==="array"){let o=[];for(let n of e.__serializer_value){let i=E(n);if(i.isErr())return i;o.push(i.unwrap())}return p(o)}else if(e.__serializer_tag==="map"){let o=[];for(let n of e.__serializer_value){let i=E(n);if(i.isErr())return i;o.push(i.unwrap())}return p(new Map(o))}else if(e.__serializer_tag==="set"){let o=[];for(let n of e.__serializer_value){let i=E(n);if(i.isErr())return i;o.push(i.unwrap())}return p(new Set(o))}else if(e.__serializer_tag==="result_ok"){let o=e.__serializer_value,n=E(o);return n.isErr()?n:p(p(n.unwrap()))}else if(e.__serializer_tag==="result_err"){let o=e.__serializer_value,n=E(o);return n.isErr()?n:p(b(n.unwrap()))}else if(e.__serializer_tag==="option_some"){let o=e.__serializer_value,n=E(o);return n.isErr()?n:p(_(n.unwrap()))}else if(e.__serializer_tag==="option_none")return p(c)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return b("This object was not serialized with Serialize");let r={};for(let o of Object.keys(e))if(typeof o=="string"){let n=E(e[o]);if(n.isErr())return n;r[o]=n.unwrap()}return p(r)}catch{return b("Failed to inspect object. Not JSON?")}}function k(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return p({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return p({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(i=>k(i)),o=r.as_iter().find(i=>i.isErr());if(o.isSome())return o.unwrap();let n=r.as_iter().map(i=>i.unwrap()).toArray();return p({__serializer_tag:"array",__serializer_value:n})}else if(e instanceof Map){let r=[...e.entries()].map(i=>k(i)),o=r.as_iter().find(i=>i.isErr());if(o.isSome())return o.unwrap();let n=r.as_iter().map(i=>i.unwrap()).toArray();return p({__serializer_tag:"map",__serializer_value:n})}else if(e instanceof Set){let r=[...e.values()].map(i=>k(i)),o=r.as_iter().find(i=>i.isErr());if(o.isSome())return o.unwrap();let n=r.as_iter().map(i=>i.unwrap()).toArray();return p({__serializer_tag:"set",__serializer_value:n})}else if(j.isResult(e))if(e.isOk()){let r=e.unwrap(),o=k(r);return o.isErr()?o:p({__serializer_tag:"result_ok",__serializer_value:o.unwrap()})}else{let r=e.unwrapErr(),o=k(r);return o.isErr()?o:p({__serializer_tag:"result_err",__serializer_value:o.unwrap()})}else if(I.isOption(e))if(e.isSome()){let r=e.unwrap(),o=k(r);return o.isErr()?o:p({__serializer_tag:"option_some",__serializer_value:o.unwrap()})}else return p({__serializer_tag:"option_none"});else if(t==="object"){let r={},o=e;for(let n of Object.keys(e)){let i=o[n],u=k(i);if(u.isErr())continue;let m=u.unwrap();r[n]=m}return p(r)}else return b("Unsupported value")}var N=/.^/,te={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:N,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:N,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:N,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:N,defacto_container:"Mp4"}},re={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:N,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:N,defacto_container:"Mp4"}},oe=v(function*(){for(let e of Object.keys(te))yield te[e]}),ne=v(function*(){for(let e of Object.keys(re))yield re[e]});var ie={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:oe().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:ne().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:c,video:c},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:_("MP3"),video:_("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:_("AAC"),video:c},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:_("FLAC"),video:c},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:_("MP3"),video:_("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:c,video:c},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:_("Wav"),video:c},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:c,video:c},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},Se=v(function*(){for(let e of Object.keys(ie))yield e}),ft=v(function*(){for(let e of Se())yield ie[e]});var ae={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};var Te=v(function*(){for(let e of Object.keys(ae))yield e}),ht=v(function*(){for(let e of Te())yield ae[e]});var Oe=K(Q(),1);async function U(e){let t=await se.storage[e.where].get(e.name);if(e.name in t){let r=t[e.name];return e.hooks?e.hooks.getter(r,e):r}return e.default()}var le={name:"database",where:"session",default:()=>({yt_bulk:c,user_messages:new Set,coapp_status:"checking",license_status:{checking:!0},current_tab_id:0,current_window_id:0,downloadable:new Map,downloading:new Map,downloaded:new Map,download_errors:new Map}),hooks:{setter:e=>k(e).unwrap(),getter:(e,t)=>E(e).unwrapOr(t.default())}};var Ce=await U(le),Me=new URL(document.location.toString()).searchParams,ue=Me.get("id");function C(e,t,r){let o=document.querySelector("tbody"),n=document.createElement("tr"),i=document.createElement("th"),u=document.createElement("td");n.appendChild(i),n.appendChild(u),i.textContent=e,u.textContent=t,r&&n.classList.add("separator"),o.appendChild(n)}if(ue){let e=Ce.downloadable.get(ue);if(e){C("Tab",`${e.tab_id} (incognito: ${e.incognito})`),C("Title",e.title),C("Page URL",`${e.page_url} with ${e.headers.length} headers`),C("Thumbnail",e.thumbnail_url),C("Low Quality",e.is_low_quality?"true":"false");let t=0;for(let r of e.variants.values()){C(`#${t} main url`,r.manifest_url,!0),C(`#${t} video component`,r.sources.video||"none"),C(`#${t} audio component`,r.sources.audio||"none");let o=r.core_media.container.name;if(o+=" b:"+r.core_media.builder,o+=" p:"+r.core_media.protocol,o+=" "+r.core_media.duration+"s",C(`#${t} core media`,o),r.core_media.av.audio){let n=r.core_media.av.audio.codec.name+" at "+r.core_media.av.audio.bitrate.unwrapOr("unknown");C(`#${t} audio`,n)}if(r.core_media.av.video){let n=r.core_media.av.video,i=`${n.codec.name} at ${n.bitrate.unwrapOr(-1)}, fps: ${n.fps.unwrapOr("?")}, q:${n.quality.unwrapOr("?")}, ${n.dimensions.map(u=>JSON.stringify(u)).unwrapOr("no dim")}`;C(`#${t} video`,i)}t++}}}
