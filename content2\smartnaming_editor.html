<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">

    <link rel="stylesheet" href="xul.css"/>
    <link rel="stylesheet" href="base.css"/>
    <title>VDH: Smartnaming</title>
    <link rel="shortcut icon" href="/content2/icons/stable-color.png"/>
    <style>
      body {
        max-width: 800px;
        margin: auto;
        padding: 0 20px;
      }

      hbox, vbox {
        gap: var(--sl-spacing-small);
      }

    </style>

  </head>
  <body>
    <div id="header">
      <h1 data-i18n="v9_smartnaming_title"></h1>
      <hbox>
        <sl-button id="button-reset"><span data-i18n="v9_smartnaming_reset_for"></span>: <span class="hostname"></span></sl-button>
        <sl-button id="button-save" variant="success"><span data-i18n="v9_smartnaming_save_for"></span>: <span class="hostname"></span></sl-button>
      </hbox>
      <sl-divider></sl-divider>
      <hbox>
        <sl-button id="button-reset-all" data-i18n="v9_smartnaming_reset_for_all"></sl-button>
        <sl-button id="button-save-all" data-i18n="v9_smartnaming_save_for_all"></sl-button>
      </hbox>
    </div>
    <vbox id="core">
      <hbox align="end">
        <sl-input data-i18n="v9_smartnaming_template" data-i18n-attr="label" id="template" clearable> </sl-input>
        <sl-input data-i18n="v9_smartnaming_max_length" data-i18n-attr="label" id="max-length" type="number" min="1" clearable> </sl-input>
      </hbox>
      <sl-input data-i18n="v9_smartnaming_selector" data-i18n-attr="label" id="selector" clearable> </sl-input>
      <hbox align="end">
        <sl-input data-i18n="v9_smartnaming_result" data-i18n-attr="label" flex="1" id="result" readonly></sl-input>
        <sl-button data-i18n="v9_smartnaming_test" id="button-test"></sl-button>
      </hbox>
    </vbox>
    <script type="module" src="shoelace.js"></script>
    <script type="module" src="smartnaming_editor.js"></script>
  </body>
</html>
