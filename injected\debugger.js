"use strict";(()=>{var o=chrome?.runtime||browser?.runtime;function l(){for(let e of Array.from(document.querySelectorAll("button")))e.setAttribute("disabled",!0);document.querySelector("#logs").textContent="wait\u2026"}function s(){for(let e of Array.from(document.querySelectorAll("button")))e.removeAttribute("disabled")}function a(){let e,t=document.querySelector("#core");t.innerHTML="",e=document.createElement("button"),e.textContent="Toggle Debugger",e.onclick=()=>{l(),o.sendMessage("debugger_toggle"),setTimeout(()=>o.sendMessage("debugger_request_logs"),1e3)},t.appendChild(e),e=document.createElement("button"),e.textContent="Restart Addon",e.onclick=()=>{l(),o.sendMessage("debugger_restart_addon"),setTimeout(()=>window.location.reload(),1e3)},t.appendChild(e),e=document.createElement("button"),e.textContent="Update",e.onclick=()=>{l(),o.sendMessage("debugger_request_logs")},t.appendChild(e),e=document.createElement("button"),e.textContent="Save Logs",e.onclick=async()=>{let u=document.querySelector("#logs").textContent,c=new Blob([u]),d=URL.createObjectURL(c),n=document.createElement("a");n.href=d,n.target="_blank",n.download="vdh-logs.txt",n.click(),setTimeout(()=>URL.revokeObjectURL(d),1e3)},t.appendChild(e);let r=document.createElement("pre");r.textContent="wait\u2026",r.setAttribute("id","logs"),t.appendChild(r)}a();l();o.onMessage.addListener(e=>{e.all_logs&&(s(),document.querySelector("#logs").textContent=e.all_logs)});setTimeout(()=>o.sendMessage("debugger_request_logs"),1e3);})();
