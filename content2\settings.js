var it=Object.create;var ve=Object.defineProperty;var st=Object.getOwnPropertyDescriptor;var at=Object.getOwnPropertyNames;var lt=Object.getPrototypeOf,ut=Object.prototype.hasOwnProperty;var ct=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),dt=(e,t)=>{for(var r in t)ve(e,r,{get:t[r],enumerable:!0})},mt=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of at(t))!ut.call(e,o)&&o!==r&&ve(e,o,{get:()=>t[o],enumerable:!(n=st(t,o))||n.enumerable});return e};var _e=(e,t,r)=>(r=e!=null?it(lt(e)):{},mt(t||!e||!e.__esModule?ve(r,"default",{value:e,enumerable:!0}):r,e));var ne=ct((Te,Fe)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof Te<"u")t(Fe);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:Te,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=n=>{let o={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(o).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class i extends WeakMap{constructor(c,_=void 0){super(_),this.createItem=c}get(c){return this.has(c)||this.set(c,this.createItem(c)),super.get(c)}}let a=u=>u&&typeof u=="object"&&typeof u.then=="function",s=(u,c)=>(..._)=>{n.runtime.lastError?u.reject(new Error(n.runtime.lastError.message)):c.singleCallbackArg||_.length<=1&&c.singleCallbackArg!==!1?u.resolve(_[0]):u.resolve(_)},g=u=>u==1?"argument":"arguments",l=(u,c)=>function(m,...x){if(x.length<c.minArgs)throw new Error(`Expected at least ${c.minArgs} ${g(c.minArgs)} for ${u}(), got ${x.length}`);if(x.length>c.maxArgs)throw new Error(`Expected at most ${c.maxArgs} ${g(c.maxArgs)} for ${u}(), got ${x.length}`);return new Promise((T,C)=>{if(c.fallbackToNoCallback)try{m[u](...x,s({resolve:T,reject:C},c))}catch(f){console.warn(`${u} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,f),m[u](...x),c.fallbackToNoCallback=!1,c.noCallback=!0,T()}else c.noCallback?(m[u](...x),T()):m[u](...x,s({resolve:T,reject:C},c))})},A=(u,c,_)=>new Proxy(c,{apply(m,x,T){return _.call(x,u,...T)}}),w=Function.call.bind(Object.prototype.hasOwnProperty),N=(u,c={},_={})=>{let m=Object.create(null),x={has(C,f){return f in u||f in m},get(C,f,V){if(f in m)return m[f];if(!(f in u))return;let S=u[f];if(typeof S=="function")if(typeof c[f]=="function")S=A(u,u[f],c[f]);else if(w(_,f)){let Q=l(f,_[f]);S=A(u,u[f],Q)}else S=S.bind(u);else if(typeof S=="object"&&S!==null&&(w(c,f)||w(_,f)))S=N(S,c[f],_[f]);else if(w(_,"*"))S=N(S,c[f],_["*"]);else return Object.defineProperty(m,f,{configurable:!0,enumerable:!0,get(){return u[f]},set(Q){u[f]=Q}}),S;return m[f]=S,S},set(C,f,V,S){return f in m?m[f]=V:u[f]=V,!0},defineProperty(C,f,V){return Reflect.defineProperty(m,f,V)},deleteProperty(C,f){return Reflect.deleteProperty(m,f)}},T=Object.create(u);return new Proxy(T,x)},H=u=>({addListener(c,_,...m){c.addListener(u.get(_),...m)},hasListener(c,_){return c.hasListener(u.get(_))},removeListener(c,_){c.removeListener(u.get(_))}}),k=new i(u=>typeof u!="function"?u:function(_){let m=N(_,{},{getContent:{minArgs:0,maxArgs:0}});u(m)}),d=new i(u=>typeof u!="function"?u:function(_,m,x){let T=!1,C,f=new Promise(M=>{C=function(I){T=!0,M(I)}}),V;try{V=u(_,m,C)}catch(M){V=Promise.reject(M)}let S=V!==!0&&a(V);if(V!==!0&&!S&&!T)return!1;let Q=M=>{M.then(I=>{x(I)},I=>{let be;I&&(I instanceof Error||typeof I.message=="string")?be=I.message:be="An unexpected error occurred",x({__mozWebExtensionPolyfillReject__:!0,message:be})}).catch(I=>{console.error("Failed to send onMessage rejected reply",I)})};return Q(S?V:f),!0}),F=({reject:u,resolve:c},_)=>{n.runtime.lastError?n.runtime.lastError.message===t?c():u(new Error(n.runtime.lastError.message)):_&&_.__mozWebExtensionPolyfillReject__?u(new Error(_.message)):c(_)},z=(u,c,_,...m)=>{if(m.length<c.minArgs)throw new Error(`Expected at least ${c.minArgs} ${g(c.minArgs)} for ${u}(), got ${m.length}`);if(m.length>c.maxArgs)throw new Error(`Expected at most ${c.maxArgs} ${g(c.maxArgs)} for ${u}(), got ${m.length}`);return new Promise((x,T)=>{let C=F.bind(null,{resolve:x,reject:T});m.push(C),_.sendMessage(...m)})},$={devtools:{network:{onRequestFinished:H(k)}},runtime:{onMessage:H(d),onMessageExternal:H(d),sendMessage:z.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:z.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},U={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return o.privacy={network:{"*":U},services:{"*":U},websites:{"*":U}},N(n,$,o)};e.exports=r(chrome)}else e.exports=globalThis.browser})});var ye={};dt(ye,{bg:()=>pt,ca:()=>ft,co:()=>gt,cs:()=>_t,da:()=>yt,de:()=>ht,default:()=>Jt,dsb:()=>At,el:()=>xt,en_US:()=>Se,es:()=>wt,fr:()=>bt,hsb:()=>vt,hu:()=>St,id:()=>kt,is:()=>Tt,it:()=>Ct,ja:()=>Mt,ko:()=>Et,nb:()=>Ot,nl:()=>It,pl:()=>Pt,pt_BR:()=>Nt,ro:()=>Vt,ru:()=>jt,sk:()=>Lt,sl:()=>qt,sv:()=>Rt,tr:()=>Dt,uk:()=>Ht,zh_CN:()=>Ft,zh_TW:()=>zt});var pt=224,ft=538,gt=694,_t=652,yt=379,ht=694,At=519,xt=636,Se=694,wt=578,bt=694,vt=519,St=540,kt=538,Tt=251,Ct=696,Mt=624,Et=303,Ot=255,It=651,Pt=636,Nt=685,Vt=223,jt=578,Lt=535,qt=540,Rt=540,Dt=617,Ht=669,Ft=679,zt=641,Jt={bg:pt,ca:ft,co:gt,cs:_t,da:yt,de:ht,dsb:At,el:xt,en_US:Se,es:wt,fr:bt,hsb:vt,hu:St,id:kt,is:Tt,it:Ct,ja:Mt,ko:Et,nb:Ot,nl:It,pl:Pt,pt_BR:Nt,ro:Vt,ru:jt,sk:Lt,sl:qt,sv:Rt,tr:Dt,uk:Ht,zh_CN:Ft,zh_TW:zt};function G(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var $t=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return P(t)},e.prototype.toString=function(){return"None"},e}(),p=new $t;Object.freeze(p);var Ut=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return b(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return y(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(G(this.value),")")},e.EMPTY=new e(void 0),e}(),b=Ut,K;(function(e){function t(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];for(var a=[],s=0,g=o;s<g.length;s++){var l=g[s];if(l.isSome())a.push(l.value);else return l}return b(a)}e.all=t;function r(){for(var o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];for(var a=0,s=o;a<s.length;a++){var g=s[a];return g.isSome(),g}return p}e.any=r;function n(o){return o instanceof b||o===p}e.isOption=n})(K||(K={}));var Qt=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(G(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(G(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new P(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return p},e.prototype.toString=function(){return"Err(".concat(G(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new ke(this)},e.EMPTY=new e(void 0),e}();var P=Qt,Gt=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(G(this.value)),{cause:this.value})},e.prototype.map=function(t){return new y(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return b(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(G(this.value),")")},e.prototype.toAsyncResult=function(){return new ke(this)},e.EMPTY=new e(void 0),e}();var y=Gt,Y;(function(e){function t(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];for(var g=[],l=0,A=a;l<A.length;l++){var w=A[l];if(w.isOk())g.push(w.value);else return w}return new y(g)}e.all=t;function r(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];for(var g=[],l=0,A=a;l<A.length;l++){var w=A[l];if(w.isOk())return w;g.push(w.error)}return new P(g)}e.any=r;function n(a){try{return new y(a())}catch(s){return new P(s)}}e.wrap=n;function o(a){try{return a().then(function(s){return new y(s)}).catch(function(s){return new P(s)})}catch(s){return Promise.resolve(new P(s))}}e.wrapAsync=o;function i(a){return a instanceof P||a instanceof y}e.isResult=i})(Y||(Y={}));var De=function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(a){a(i)})}return new(r||(r=Promise))(function(i,a){function s(A){try{l(n.next(A))}catch(w){a(w)}}function g(A){try{l(n.throw(A))}catch(w){a(w)}}function l(A){A.done?i(A.value):o(A.value).then(s,g)}l((n=n.apply(e,t||[])).next())})},He=function(e,t){var r={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},n,o,i,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(l){return function(A){return g([l,A])}}function g(l){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(r=0)),r;)try{if(n=1,o&&(i=l[0]&2?o.return:l[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,l[1])).done)return i;switch(o=0,i&&(l=[l[0]&2,i.value]),l[0]){case 0:case 1:i=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,o=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(i=r.trys,!(i=i.length>0&&i[i.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!i||l[1]>i[0]&&l[1]<i[3])){r.label=l[1];break}if(l[0]===6&&r.label<i[1]){r.label=i[1],i=l;break}if(i&&r.label<i[2]){r.label=i[2],r.ops.push(l);break}i[2]&&r.ops.pop(),r.trys.pop();continue}l=t.call(e,r)}catch(A){l=[6,A],o=0}finally{n=i=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}},ke=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(n){return De(r,void 0,void 0,function(){var o;return He(this,function(i){return n.isErr()?[2,n]:(o=t(n.value),[2,o instanceof e?o.promise:o])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(n){return De(r,void 0,void 0,function(){var o;return He(this,function(i){switch(i.label){case 0:return n.isErr()?[2,n]:(o=y,[4,t(n.value)]);case 1:return[2,o.apply(void 0,[i.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();function j(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return b(r);return p},count:function(t){return this.reduce((r,n)=>(t(n)&&r++,r),0)},reduce:function(t,r){let n=r;for(let o of this)n=t(n,o);return n},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>b(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?b(r):p)},enumerate:function(){let t=this;return j(function*(){let r=0;for(let n of t)yield[r,n],r++})()},filterMap:function(t){let r=this;return j(function*(){for(let n of r){let o=t(n);o.isSome()&&(yield o.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return j(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return j(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return j(function*(){for(let t of e)yield t})()});var O=_e(ne(),1);var Je=_e(ne(),1);function ze(e){return e?e.replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll('"',"&quot;").replaceAll("'","&#039;"):""}function J(e,t,r){let n=()=>(console.error(`Requesting unknown i18n string ${e}`),e);t=t.map(o=>o.toString()).map(ze);try{if(e in r){let o=r[e],i=1;for(let a=0;a<t.length;a++)o=o.replace(`$${i}`,t[a]);return o}else{let o=Je.default.i18n.getMessage(e,t);return o||n()}}catch{return n()}}function Ce(e,t){for(let r of Array.from(e.querySelectorAll("[data-i18n]"))){let n=r.dataset.i18nArgs,o=r.dataset.i18nAttr,i;n?i=J(r.dataset.i18n,JSON.parse(n),t):i=J(r.dataset.i18n,[],t),o?r.setAttribute(o,i):r.textContent=i}}var Me="stable";var he="google";var W=_e(ne(),1);var Ue=(e,t)=>typeof e[t]=="string";function L(e){try{if(Ue(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return y(e.__serializer_value);if(e.__serializer_tag==="regex"){let n=new RegExp(e.__serializer_value);return y(n)}else if(e.__serializer_tag==="array"){let n=[];for(let o of e.__serializer_value){let i=L(o);if(i.isErr())return i;n.push(i.unwrap())}return y(n)}else if(e.__serializer_tag==="map"){let n=[];for(let o of e.__serializer_value){let i=L(o);if(i.isErr())return i;n.push(i.unwrap())}return y(new Map(n))}else if(e.__serializer_tag==="set"){let n=[];for(let o of e.__serializer_value){let i=L(o);if(i.isErr())return i;n.push(i.unwrap())}return y(new Set(n))}else if(e.__serializer_tag==="result_ok"){let n=e.__serializer_value,o=L(n);return o.isErr()?o:y(y(o.unwrap()))}else if(e.__serializer_tag==="result_err"){let n=e.__serializer_value,o=L(n);return o.isErr()?o:y(P(o.unwrap()))}else if(e.__serializer_tag==="option_some"){let n=e.__serializer_value,o=L(n);return o.isErr()?o:y(b(o.unwrap()))}else if(e.__serializer_tag==="option_none")return y(p)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return P("This object was not serialized with Serialize");let r={};for(let n of Object.keys(e))if(typeof n=="string"){let o=L(e[n]);if(o.isErr())return o;r[n]=o.unwrap()}return y(r)}catch{return P("Failed to inspect object. Not JSON?")}}function q(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return y({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return y({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(i=>q(i)),n=r.as_iter().find(i=>i.isErr());if(n.isSome())return n.unwrap();let o=r.as_iter().map(i=>i.unwrap()).toArray();return y({__serializer_tag:"array",__serializer_value:o})}else if(e instanceof Map){let r=[...e.entries()].map(i=>q(i)),n=r.as_iter().find(i=>i.isErr());if(n.isSome())return n.unwrap();let o=r.as_iter().map(i=>i.unwrap()).toArray();return y({__serializer_tag:"map",__serializer_value:o})}else if(e instanceof Set){let r=[...e.values()].map(i=>q(i)),n=r.as_iter().find(i=>i.isErr());if(n.isSome())return n.unwrap();let o=r.as_iter().map(i=>i.unwrap()).toArray();return y({__serializer_tag:"set",__serializer_value:o})}else if(Y.isResult(e))if(e.isOk()){let r=e.unwrap(),n=q(r);return n.isErr()?n:y({__serializer_tag:"result_ok",__serializer_value:n.unwrap()})}else{let r=e.unwrapErr(),n=q(r);return n.isErr()?n:y({__serializer_tag:"result_err",__serializer_value:n.unwrap()})}else if(K.isOption(e))if(e.isSome()){let r=e.unwrap(),n=q(r);return n.isErr()?n:y({__serializer_tag:"option_some",__serializer_value:n.unwrap()})}else return y({__serializer_tag:"option_none"});else if(t==="object"){let r={},n=e;for(let o of Object.keys(e)){let i=n[o],a=q(i);if(a.isErr())continue;let s=a.unwrap();r[o]=s}return y(r)}else return P("Unsupported value")}var Z=/.^/,Ee={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:Z,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:Z,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:Z,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:Z,defacto_container:"Mp4"}},Qe={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:Z,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:Z,defacto_container:"Mp4"}},Ge=j(function*(){for(let e of Object.keys(Ee))yield Ee[e]}),We=j(function*(){for(let e of Object.keys(Qe))yield Qe[e]});function Oe(e){return typeof e=="string"&&e in Ee?b(e):p}var Ie={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:Ge().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:We().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:p,video:p},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:b("MP3"),video:b("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:b("AAC"),video:p},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:b("FLAC"),video:p},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:b("MP3"),video:b("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:p,video:p},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:b("Wav"),video:p},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:p,video:p},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},Wt=j(function*(){for(let e of Object.keys(Ie))yield e}),on=j(function*(){for(let e of Wt())yield Ie[e]});function Pe(e){return typeof e=="string"&&e in Ie?b(e):p}var Be={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};var Ke=j(function*(){for(let e of Object.keys(Be))yield e}),dn=j(function*(){for(let e of Ke())yield Be[e]});function oe(e){if(typeof e=="string")return Ke().find(t=>t==e);if(typeof e=="number"){let t=e.toString();return oe(t)}return p}function Ne(){return{prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"4320",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:[]}}function Ye(e){return q(e).unwrap()}function Ze(e){let t=L(e).unwrapOr({}),r=Ne(),n=Pe(t.container).unwrapOr(r.container),o=Oe(t.video_codec).unwrapOr(r.video_codec),i=oe(t.best_video_quality).unwrapOr(r.best_video_quality),a=oe(t.lowest_video_quality).unwrapOr(r.lowest_video_quality),s;if("prefered_video_quality"in t){let k=oe(t.prefered_video_quality);k.isSome()&&(s=k.unwrap())}let g=r.max_variants;if(typeof t.max_variants=="number"){let k=t.max_variants;Number.isInteger(k)&&k<=11&&k>0&&(g=k)}let l=r.prefer_60fps;typeof t.prefer_60fps=="boolean"&&(l=t.prefer_60fps);let A=r.ignore_low_quality_hits;typeof t.ignore_low_quality_hits=="boolean"&&(A=t.ignore_low_quality_hits);let w=[];if(Array.isArray(t.ignored_containers))for(let k of t.ignored_containers){let d=Pe(k);d.isSome()&&w.push(d.unwrap())}let N=[];if(Array.isArray(t.ignored_video_codecs))for(let k of t.ignored_video_codecs){let d=Oe(k);d.isSome()&&N.push(d.unwrap())}let H={prefer_60fps:l,ignore_low_quality_hits:A,container:n,max_variants:g,video_codec:o,lowest_video_quality:a,best_video_quality:i,ignored_containers:w,ignored_video_codecs:N};return typeof s<"u"&&(H.prefered_video_quality=s),H}var Xe={all_tabs:!1,low_quality:!1,sort_by_status:!0,sort_reverse:!1,show_button_clean:!0,show_button_clean_all:!1,show_button_convert_local:!1,hide_downloaded:!1};var Bt=_e(ne(),1);function et(){return{template:"%title",max_length:64}}function Ve(e,t){if(e==null||t===null||t===void 0)return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Function||e instanceof RegExp)return e===t;if(e===t||e.valueOf()===t.valueOf())return!0;if(Array.isArray(e)&&e.length!==t.length||e instanceof Date||!(e instanceof Object)||!(t instanceof Object))return!1;let r=Object.keys(e),n=Object.keys(t).every(i=>r.indexOf(i)!==-1),o=r.every(i=>Ve(e[i],t[i]));return n&&o}async function R(e,t){let r=t;e.hooks&&(r=e.hooks.setter(t)),await W.storage[e.where].set({[e.name]:r})}async function v(e){let t=await W.storage[e.where].get(e.name);if(e.name in t){let r=t[e.name];return e.hooks?e.hooks.getter(r,e):r}return e.default()}async function je(e){await W.storage[e.where].remove(e.name)}function E(e,t){W.storage[e.where].onChanged.addListener(r=>{let n=r[e.name];if(n){if(Ve(n.oldValue,n.newValue))return;typeof n.newValue>"u"?t(e.default()):e.hooks?t(e.hooks.getter(n.newValue,e)):t(n.newValue)}})}var Kt={name:"privacy_accept",default:()=>"unknown",where:"local"},ie={name:"http_media_download_strategy",default:()=>"coapp",where:"local"},Yt={name:"debugger_enabled",default:()=>!1,where:"local"},Zt={name:"debugger_logs",default:()=>[],where:"session"},Xt={name:"used_history_button",default:()=>!1,where:"local"},er={name:"use_sidebar",default:()=>!1,where:"local"},tr={name:"flush_youtube",default:()=>0,where:"local"};var Ae={name:"download_directory",default:()=>"dwhelper",where:"local"},se={name:"concurrent_downloads_max",default:()=>6,where:"local"},ae={name:"show_thumbnail_in_notification",default:()=>!0,where:"local"},le={name:"show_success_notification",default:()=>!0,where:"local"},ue={name:"show_success_notification_for_icognito",default:()=>!1,where:"local"},ce={name:"theme",default:()=>"system",where:"local"},X={name:"view_options",default:()=>structuredClone(Xe),where:"local"},de={name:"show_context_menu",default:()=>!0,where:"local"},me={name:"forget_media_on_tab_close",default:()=>!0,where:"local"},rr={name:"default_action",default:()=>"download",where:"local"},nr={name:"yt_warning",default:()=>!0,where:"local"},pe={name:"use_wide_ui",default:()=>!1,where:"local"},xe={name:"use_legacy_ui",default:()=>!1,where:"local"},or={name:"never_show_no_incognito_msg_again",default:()=>!1,where:"local"},ir={name:"auto_hide_downloaded_message_has_been_displayed",default:()=>!0,where:"local"},sr={name:"valid_license_message_has_been_displayed",default:()=>!1,where:"local"},ar={name:"open_count_store",default:()=>0,where:"session"},lr={name:"successfull_dl",default:()=>0,where:"local"},ur={name:"never_show_successfull_dl_message",default:()=>!1,where:"local"},cr={name:"record_download_history",default:()=>!1,where:"local"},fe={name:"history_limit_in_days",default:()=>30,where:"local"},ee={name:"view_options",default:()=>({}),where:"session"};var dr={name:"blacklist",default:()=>[],where:"local",hooks:{setter:e=>e.filter(t=>t.length>0),getter:e=>e}},mr={name:"last_download_directory",default:()=>p,where:"local",hooks:{setter:e=>q(e).unwrap(),getter:(e,t)=>L(e).unwrapOr(t.default())}},B={name:"media_user_pref",where:"local",default:()=>Ne(),hooks:{setter:e=>Ye(e),getter:e=>Ze(e)}},pr={name:"download_history",where:"local",default:()=>new Map,hooks:{setter:e=>q(e).unwrap(),getter:(e,t)=>L(e).unwrapOr(t.default())}},tt={name:"smartnaming",where:"local",default:()=>new Map([["*",et()]]),hooks:{setter:e=>q(e).unwrap(),getter:(e,t)=>L(e).unwrapOr(t.default())}},ge={name:"database",where:"session",default:()=>({yt_bulk:p,user_messages:new Set,coapp_status:"checking",license_status:{checking:!0},current_tab_id:0,current_window_id:0,downloadable:new Map,downloading:new Map,downloaded:new Map,download_errors:new Map}),hooks:{setter:e=>q(e).unwrap(),getter:(e,t)=>L(e).unwrapOr(t.default())}},Le=[Yt,Zt,ir,sr,Kt,ie,Xt,Ae,mr,se,B,ae,le,ue,or,ce,X,ee,de,me,dr,tt,rr,nr,pe,xe,er,tr,ar,lr,ur,pr,cr,fe];async function rt(e){for(let t in e)for(let r of Le)r.name==t&&await W.storage[r.where].set({[t]:e[t]})}async function qe(){let e={};for(let t of Le){let r=await W.storage[t.where].get(t.name);t.name in r&&(e={...e,...r})}return e}async function nt(){for(let e of Le)await je(e)}function we(e){O.default.runtime.sendMessage(e)}async function D(){let e=await v(Ae),t=await v(ie),r=await v(se),n=await v(de),o=await v(le),i=await v(ae),a=await v(ue),s=await v(X),g=await v(B),l=await v(ce),A=await v(me),w=await v(pe),N=await v(xe),H=await v(fe);document.querySelector("#settings-download-directory > i").textContent=e;let k=document.querySelector("#radio-theme");k.value=l;let d=document.querySelector("#settings-browser-download > sl-checkbox");d.indeterminate=!1,t=="inbrowser"?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-show-notification > sl-checkbox"),d.indeterminate=!1,o?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-show-tb-in-notification > sl-checkbox"),d.indeterminate=!1,i?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-context-menu > sl-checkbox"),d.indeterminate=!1,n?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-forget-on-close > sl-checkbox"),d.indeterminate=!1,A?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-show-notification-for-incognito > sl-checkbox"),d.indeterminate=!1,a?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-view-convert-local > sl-checkbox"),d.indeterminate=!1,s.show_button_convert_local?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-use-wide-ui > sl-checkbox"),d.indeterminate=!1,w?d.checked=!0:d.checked=!1,d=document.querySelector("#settings-use-legacy-ui > sl-checkbox"),d.indeterminate=!1,N?d.checked=!0:d.checked=!1;let F=document.querySelector("#settings-concurrent-downloads > sl-input");F.value=r.toString(),F=document.querySelector("#settings-history-limit > sl-input"),F.value=H.toString();let z=document.querySelector("#settings-variants .variant-quality");g.prefered_video_quality?(z.removeAttribute("hidden"),z.textContent=g.prefered_video_quality+"p",z.className=`variant-quality _${g.prefered_video_quality}p`):z.setAttribute("hidden","true");let $=document.querySelector("#settings-variants .variant-container");$.textContent=g.container,F=document.querySelector("#settings-max-variants > sl-input"),F.value=g.max_variants.toString()}function Re(e,t){let r=e.coapp_status,n=document.querySelector("#drawer-settings-coapp");if(r=="checking")n.setAttribute("coapp-status","checking");else if(r.found){let i=Array.from(n.querySelectorAll(".coapp-path"));for(let s of i)s.textContent=r.path;let a=Array.from(n.querySelectorAll(".coapp-version"));for(let s of a)s.textContent=r.version;r.new_version?(n.setAttribute("coapp-status","outdated"),n.querySelector("#coapp-new-version").textContent=r.new_version):n.setAttribute("coapp-status","found")}else n.setAttribute("coapp-status","not-found"),n.querySelector("#coapp-error").textContent=r.error;let o=e.license_status;if(n=document.querySelector("#drawer-settings-license"),"unneeded"in o)n.setAttribute("license-status","unneeded");else if("nocoapp"in o)n.setAttribute("license-status","nocoapp");else if("checking"in o)n.setAttribute("license-status","checking");else{n.setAttribute("license-status","checked");let i=document.querySelector("#input-license-number");i.classList.contains("had_focus")||("key"in o?i.value=o.key:i.value="");let a=document.querySelector("#settings-license-checked-msg");a.classList.toggle("settings-success",!1),a.classList.toggle("settings-warning",!1),"unset"in o?a.textContent=J("v9_lic_status_unset",[],t):"blocked"in o?(a.classList.toggle("settings-warning",!0),a.textContent=J("v9_lic_status_blocked",[],t)):"locked"in o?(a.classList.toggle("settings-warning",!0),a.textContent=J("v9_lic_status_locked2",[],t)):"accepted"in o?(a.classList.toggle("settings-success",!0),a.textContent=`${J("v9_lic_status_accepted",[],t)} (${o.email})`):"mismatch"in o?(a.classList.toggle("settings-warning",!0),a.textContent=J("v9_lic_mismatch2",[o.other_browser,o.this_browser],t)):"invalid"in o&&(a.classList.toggle("settings-warning",!0),a.textContent=J("v9_no_license_registered",[],t))}}async function fr(){let e=await v(ee);Ce(document,e),document.querySelector("#settings-version").textContent=O.default.runtime.getManifest().version,document.querySelector("#settings-channel").textContent=Me,document.querySelector("#settings-target").textContent=he,document.querySelector("#settings-locale").textContent=O.default.i18n.getUILanguage();{let t=O.default.i18n.getUILanguage().replace("-","_");if(t in ye){document.querySelector("#settings-locales").removeAttribute("hidden");let r=ye[t],n=Se;document.querySelector("#settings-locales-missing-strings-count").textContent=(n-r).toString(),document.querySelector("#settings-locales-locale-name").textContent=t}else document.querySelector("#settings-locales").setAttribute("hidden","true")}{let t=document.querySelector("#settings-incognito-mode");await O.default.extension.isAllowedIncognitoAccess()&&t.setAttribute("hidden","true")}E(Ae,D),E(ie,D),E(se,D),E(B,D),E(de,D),E(le,D),E(ae,D),E(ue,D),E(X,D),E(ce,D),E(me,D),E(pe,D),E(fe,D),D();{E(ge,async a=>{let s=await v(ee);Re(a,s)}),E(ee,async a=>{let s=await v(ge);Re(s,a),Ce(document,a)});let t=await v(ee),r=await v(ge);Re(r,t);async function n(a){let s=a.target;if(s){let g=s.closest("#settings-concurrent-downloads > sl-input"),l=s.closest("#settings-max-variants > sl-input"),A=s.closest("#settings-history-limit > sl-input"),w=s.closest("#radio-theme"),N=s.closest("#settings-browser-download > sl-checkbox"),H=s.closest("#settings-context-menu > sl-checkbox"),k=s.closest("#settings-forget-on-close > sl-checkbox"),d=s.closest("#settings-show-notification > sl-checkbox"),F=s.closest("#settings-show-tb-in-notification > sl-checkbox"),z=s.closest("#settings-show-notification-for-incognito > sl-checkbox"),$=s.closest("#settings-view-convert-local > sl-checkbox"),U=s.closest("#settings-use-wide-ui > sl-checkbox"),u=s.closest("#settings-use-legacy-ui > sl-checkbox");if(w&&R(ce,w.value),g&&R(se,parseInt(g.value)),l){let c=await v(B);c.max_variants=Math.max(1,Math.min(10,parseInt(l.value))),R(B,c)}if(A&&R(fe,Math.min(99,parseInt(A.value))),N&&R(ie,N.checked?"inbrowser":"coapp"),H&&R(de,H.checked),k&&R(me,k.checked),d&&R(le,d.checked),F&&R(ae,F.checked),z&&R(ue,z.checked),U&&R(pe,U.checked),u&&R(xe,u.checked),$){let c=await v(X);c.show_button_convert_local=$.checked,R(X,c)}}}async function o(a){let s=a.target;if(s){let g=s.closest(".button-coapp-check"),l=s.closest(".button-license-check"),A=s.closest(".button-license-help"),w=s.closest(".button-license-get"),N=s.closest(".button-coapp-install"),H=s.closest(".button-coapp-help"),k=s.closest("#button-reset-settings"),d=s.closest("#button-reset-privacy"),F=s.closest("#button-export-settings"),z=s.closest("#button-import-settings"),$=s.closest("#button-reload-addon"),U=s.closest("#button-download-directory-change"),u=s.closest("#button-variants-clear"),c=s.closest("#button-translate");if(s.closest("#button-copy-settings")){let{coapp_status:m,license_status:x}=await v(ge);"key"in x&&(x.key=x.key.substring(0,16)),"email"in x&&(x.email="<removed>");let T=JSON.stringify(m),C=JSON.stringify(x),f=O.default.runtime.getManifest(),V=f.version_name??f.version,S=await O.default.runtime.getPlatformInfo(),Q=O.default.i18n.getUILanguage(),M="";M+=`version: ${V}
`,M+=`target: ${he}
`,M+=`channel: ${Me}
`,M+=`lang: ${Q}
`,M+=`coapp: ${T}
`,M+=`license: ${C}
`,M+=`platform: ${S.arch} ${S.os}
`,M+=`UA: ${navigator.userAgent}
`;let I=await qe();delete I.blacklist,delete I.media_user_pref,delete I.smartnaming,M+=JSON.stringify(I,null,4),navigator.clipboard.writeText(M)}if(l){let m=document.querySelector("#input-license-number");we({license_check:m.value||null})}if(c&&O.default.tabs.create({url:"/content2/locales.html"}),w&&O.default.tabs.create({url:"https://www.downloadhelper.net/convert"}),A&&O.default.tabs.create({url:"https://www.downloadhelper.net/help"}),g&&we({coapp_check:!0}),N&&O.default.tabs.create({url:"https://downloadhelper.net/install-coapp-v2"}),H&&O.default.tabs.create({url:"https://github.com/aclap-dev/video-downloadhelper/wiki/CoApp-not-recognized"}),u&&je(B),k&&nt(),d&&he=="mozilla"&&we({privacy_accept:"unknown"}),U&&we("select_download_directory"),F){let m=await qe(),x=JSON.stringify(m,null,4),T=new Blob([x],{type:"text/json;charset=utf-8"}),C=URL.createObjectURL(T),f=document.documentElement.id=="sidebar";O.default.downloads.download({url:C,filename:"vdh-settings.json",saveAs:f,conflictAction:"uniquify"})}if(z){let m=document.querySelector("#button-import-settings-input"),x=async()=>{if(m.removeEventListener("change",x),m.files&&m.files.length>0){let T=await m.files[0].text(),C=JSON.parse(T);await rt(C)}};m.addEventListener("change",x),m.click()}$&&O.default.runtime.reload()}}let i=document.querySelector("#input-license-number");i.addEventListener("sl-focus",()=>{i.classList.add("had_focus")}),window.addEventListener("click",o),window.addEventListener("sl-change",n)}}var ot=document.querySelector("#include-settings");ot.addEventListener("sl-load",e=>{e.eventPhase===Event.AT_TARGET&&fr()});ot.addEventListener("sl-error",e=>{e.eventPhase===Event.AT_TARGET&&console.error("sl-include error",e.detail?.status)});
