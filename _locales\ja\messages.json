{"Bytes": {"message": "$1 バイト"}, "GB": {"message": "$1 GB"}, "KB": {"message": "$1 KB"}, "MB": {"message": "$1 MB"}, "__MSG_appDesc_": {"message": "Video DownloadHelper"}, "about": {"message": "バージョン情報"}, "about_alpha_extra7_fx": {"message": "Firefox の内部的な技術的変更のために、アドオンは完全に書き直されなければなりませんでした。以前のバージョンのすべての機能を元に戻すには、数週間かかることがあります。"}, "about_alpha_intro": {"message": "これは開発初期版です。"}, "about_beta_intro": {"message": "これは評価版です。"}, "about_chrome_licenses": {"message": "Chrome ライセンスについて"}, "about_qr": {"message": "生成されたファイル"}, "about_vdh": {"message": "Video DownloadHelper について"}, "action_abort_description": {"message": "進行中の動作を中断します。"}, "action_abort_title": {"message": "中止"}, "action_as_default": {"message": "既定の動作として使用する"}, "action_avplay_description": {"message": "コンバーターの本来のビューアーでビデオを再生します。"}, "action_avplay_title": {"message": "再生"}, "action_blacklist_description": {"message": "選択したドメインから発信、または配信された動画は無視されます。"}, "action_blacklist_title": {"message": "ブラックリストに追加"}, "action_bulkdownload_description": {"message": "選択した動画をダウンロードします。"}, "action_bulkdownload_title": {"message": "一括ダウンロード"}, "action_bulkdownloadconvert_description": {"message": "一括して選択したビデオをダウンロードして変換します。"}, "action_bulkdownloadconvert_title": {"message": "一括ダウンロードおよび変換"}, "action_copyurl_description": {"message": "メディアの URL をクリップボードにコピーします。"}, "action_copyurl_title": {"message": "URL のコピー"}, "action_deletehit_description": {"message": "現在の一覧から検出したものを削除します。"}, "action_deletehit_title": {"message": "検出の削除"}, "action_details_description": {"message": "検出したものの詳細を表示します。"}, "action_details_title": {"message": "詳細"}, "action_download_description": {"message": "ファイルをハードドライブにダウンロードします。"}, "action_download_title": {"message": "ダウンロード"}, "action_downloadconvert_description": {"message": "メディアをダウンロードして別の形式に変換します。"}, "action_downloadconvert_title": {"message": "ダウンロードおよび変換"}, "action_openlocalcontainer_description": {"message": "ローカルファイルのフォルダーを開きます。"}, "action_openlocalcontainer_title": {"message": "フォルダーを開く"}, "action_openlocalfile_description": {"message": "ローカルメディアファイルを開きます。"}, "action_openlocalfile_title": {"message": "メディアを開く"}, "action_pin_description": {"message": "検出したものをピン留めします。"}, "action_pin_title": {"message": "ピン留め"}, "action_quickdownload_description": {"message": "宛先を尋ねずにダウンロードします。"}, "action_quickdownload_title": {"message": "クイックダウンロード"}, "action_stop_description": {"message": "キャプチャーを停止します。"}, "action_stop_title": {"message": "停止"}, "adaptative": {"message": "$1 適応"}, "add_to_blacklist": {"message": "ブラックリストに追加"}, "add_to_blacklist_help": {"message": "選択したドメインから発信、または配信された動画は無視されます。"}, "advanced": {"message": "詳細設定"}, "aggregating": {"message": "集約中..."}, "analyze_page": {"message": "分析ページ"}, "appDesc": {"message": "Web からビデオをダウンロードする"}, "appName": {"message": "Video DownloadHelper"}, "appearance": {"message": "外観"}, "audio_only": {"message": "オーディオのみ"}, "behavior": {"message": "動作"}, "blacklist": {"message": "ブラックリスト"}, "blacklist_add_domain": {"message": "ブラックリストに載ったドメインを追加する"}, "blacklist_add_placeholder": {"message": "登録するドメインを入力"}, "blacklist_edit_descr": {"message": "ブラックリストでは、一部のドメインからのメディアを無視できます。"}, "blacklist_empty": {"message": "ブラックリストに載っていないドメイン"}, "browser_info": {"message": "ブラウザー $1 $2 $3"}, "browser_locale": {"message": "ブラウザーの言語: $1"}, "build_options": {"message": "構築オプション: $1"}, "built_on": {"message": "$1 で構築"}, "bulk_in_progress": {"message": "一括ビデオダウンロードの操作が進行中です。このタブを閉じないでください。これは自動的に行われます。"}, "bulk_n_videos": {"message": "$1 個のビデオ"}, "cancel": {"message": "キャンセル"}, "change": {"message": "変更"}, "chrome_basic_mode": {"message": "Chrome Basic (アップグレードのアドバイス)"}, "chrome_inapp_descr_premium_lifetime": {"message": "時間制限のないプレミアムステータス"}, "chrome_inapp_descr_premium_monthly": {"message": "毎月の定期購読によるプレミアムステータス"}, "chrome_inapp_descr_premium_yearly": {"message": "年間購読によるプレミアムステータス"}, "chrome_inapp_no_subs": {"message": "注：Google による Chrome 決済サービスの廃止後、サブスクリプションは利用できなくなります。"}, "chrome_inapp_not_avail": {"message": "利用不可"}, "chrome_inapp_premium_lifetime": {"message": "ライフタイムプレミアム"}, "chrome_inapp_premium_monthly": {"message": "月額プレミアム定期購入"}, "chrome_inapp_premium_yearly": {"message": "年間プレミアム定期購読"}, "chrome_install_firefox": {"message": "Firefox をインストールする"}, "chrome_install_fx_vdh": {"message": "Firefox 用 Video DownloadHelper"}, "chrome_license_webstore_accepted": {"message": "Chrome ウェブストアからの有効なライセンス"}, "chrome_licensing": {"message": "Chrome のライセンス"}, "chrome_noyt_text": {"message": "申し訳ありませんが、Chrome ウェブストアでは YouTube の動画をダウンロードするための拡張機能を使用できないため、この機能を削除する必要がありました。"}, "chrome_noyt_text2": {"message": "Video DownloadHelper を使用すると、Firefox 版で YouTube の動画をダウンロードできます。"}, "chrome_noyt_text3": {"message": "申し訳ありませんが、Chrome ウェブストアでは YouTube 動画のダウンロード用の拡張機能が許可されないため、この機能を Chrome バージョンの拡張機能に含めることができませんでした。"}, "chrome_premium_audio": {"message": "オーディオ専用ファイルの生成は、プレミアムモードでのみ利用可能です。"}, "chrome_premium_check_error": {"message": "プレミアムステータスの確認エラー"}, "chrome_premium_hls": {"message": "プレミアムステータスなしでは、HLS ダウンロードは前回の $1 分後にのみ実行できます。"}, "chrome_premium_mode": {"message": "Chrome プレミアム"}, "chrome_premium_need_sign": {"message": "Chrome for Premium にログインする必要があります。"}, "chrome_premium_not_signed": {"message": "Chrome にログインしていません。"}, "chrome_premium_recheck": {"message": "プレミアムステータスの再確認"}, "chrome_premium_required": {"message": "プレミアムステータスが必要"}, "chrome_premium_source": {"message": "あなたは $1 でプレミアムユーザーです。"}, "chrome_product_intro": {"message": "以下のいずれかのオプションを使用して Premium にアップグレードできます:"}, "chrome_req_review": {"message": "また、Chrome WebStore について素晴らしいレビューを書いてもいいですか?"}, "chrome_signing_in": {"message": "Chrome にログインする"}, "chrome_verif_premium": {"message": "プレミアムステータスの確認中..."}, "chrome_verif_premium_error": {"message": "アプリ内支払い API は利用できません。"}, "chrome_warning_yt": {"message": "Chrome 拡張機能と YouTube に関する警告"}, "clear": {"message": "消去"}, "clear_hits": {"message": "検出したものを消去"}, "clear_logs": {"message": "ログの消去"}, "coapp": {"message": "コンパニオンアプリ"}, "coapp_error": {"message": "返されたコンパニオンアプリの確認:"}, "coapp_found": {"message": "インストール済みコンパニオンアプリ:"}, "coapp_install": {"message": "コンパニオンアプリをインストールする"}, "coapp_installed": {"message": "コンパニオンアプリがインストール済み"}, "coapp_latest_version": {"message": "最新バージョンは $1 です。"}, "coapp_not_installed": {"message": "コンパニオンアプリが未インストール"}, "coapp_outdated": {"message": "コンパニオンアプリは最新ではありません - 更新してください"}, "coapp_outofdate": {"message": "コンパニオンアプリのアップグレードが必要です。"}, "coapp_outofdate_text": {"message": "コンパニオンアプリのバージョン $1 を実行していますが、この機能にはバージョン $2 が必要です。"}, "coapp_path": {"message": "コンパニオンアプリの場所:"}, "coapp_recheck": {"message": "再チェック"}, "coapp_required": {"message": "コンパニオンアプリが必要です。"}, "coapp_required_text": {"message": "この操作では、外部アプリケーションを完了する必要があります。"}, "coapp_shell": {"message": "コンパニオンアプリ シェル"}, "coapp_unchecked": {"message": "コンパニオンアプリの確認中..."}, "coapp_update": {"message": "コンパニオンアプリの更新"}, "collecting": {"message": "収集中..."}, "confirmation_required": {"message": "確認"}, "congratulations": {"message": "おめでとうございます!"}, "continue": {"message": "続行"}, "convconf_2passes": {"message": "2 pass (事前に 1 回解析してから、その解析結果を元にエンコードします)"}, "convconf_ac": {"message": "オーディオチャンネル"}, "convconf_acnone": {"message": "なし"}, "convconf_acodec": {"message": "オーディオのコーデック"}, "convconf_aspect": {"message": "縦横比"}, "convconf_audiobitrate": {"message": "オーディオのビットレート (1 秒あたりのビット数)"}, "convconf_audiofreq": {"message": "オーディオの周波数"}, "convconf_audioonly": {"message": "オーディオのみ"}, "convconf_bitrate": {"message": "ビットレート (1 秒あたりのビット数)"}, "convconf_container": {"message": "ファイルの種類"}, "convconf_duplicate": {"message": "複製"}, "convconf_ext": {"message": "出力ファイルの拡張子"}, "convconf_extra": {"message": "その他のパラメーター"}, "convconf_level": {"message": "レベル"}, "convconf_mono": {"message": "モノラル"}, "convconf_new": {"message": "新規"}, "convconf_preset": {"message": "プリセット"}, "convconf_profilev": {"message": "ビデオプロファイル"}, "convconf_rate": {"message": "フレームレート (1 秒あたりのコマ数)"}, "convconf_readonly": {"message": "この既定の設定は読み取り専用です。それを複製して変更します。"}, "convconf_remove": {"message": "削除"}, "convconf_reset": {"message": "すべてリセット"}, "convconf_reset_confirm": {"message": "これにより、すべてのカスタム設定が削除されます。"}, "convconf_save": {"message": "保存"}, "convconf_size": {"message": "フレームサイズ (横×縦のピクセル数)"}, "convconf_stereo": {"message": "ステレオ"}, "convconf_target": {"message": "対象"}, "convconf_tune": {"message": "曲"}, "convconf_vcodec": {"message": "ビデオのコーデック"}, "convconf_videobitrate": {"message": "ビデオのビットレート (1 秒あたりのビット数)"}, "conversion_create_rule": {"message": "ルールを作成"}, "conversion_outputs": {"message": "変換出力"}, "conversion_rules": {"message": "変換ルール"}, "conversion_update_rule": {"message": "ルールの更新"}, "convert": {"message": "変換"}, "convert_local_files": {"message": "ローカルファイルを変換する"}, "converter_needed_aggregate": {"message": "この操作では、ビデオストリームとオーディオストリームを集約するために、システムにコンバーターがインストールされている必要があります。"}, "converter_needed_aggregate_why": {"message": "なぜコンバーターが必要なのですか?"}, "converter_needs_reg": {"message": "登録が必要"}, "converter_queued": {"message": "コンバーターが待機中..."}, "converter_reg_audio": {"message": "明確に、または自動変換ルールから、オーディオ専用メディアファイルの生成を要求しました。これには、登録されたコンバーターが必要です。"}, "converting": {"message": "変換中..."}, "convrule_convert": {"message": "変換する"}, "convrule_domain": {"message": "ドメイン"}, "convrule_extension": {"message": "拡張子"}, "convrule_format": {"message": "$1 をフォーマットする"}, "convrule_from_domain": {"message": "ドメイン $1 から"}, "convrule_no_convert": {"message": "変換しない"}, "convrule_output_format": {"message": "出力形式"}, "convrule_refresh_formats": {"message": "出力形式を最新の情報に更新"}, "convrule_with_ext": {"message": "拡張子 '$1'"}, "convrules_add_rule": {"message": "新しい変換ルールを作成する"}, "convrules_edit_descr": {"message": "変換ルールに従いダウンロード直後にメディア変換を自動的に実行できます。"}, "convrules_empty": {"message": "変換ルールはありません。"}, "copy_of": {"message": "$1 のコピー"}, "corrupted_media_file": {"message": "ファイル '$2' からメディア '$1' の情報を取得できませんでした。ファイルが壊れている可能性があります。"}, "create": {"message": "作成"}, "custom_output": {"message": "カスタム出力形式"}, "dash_streaming": {"message": "DASH ストリーミング"}, "default": {"message": "既定値"}, "details_parenthesis": {"message": "(詳細)"}, "dev_build": {"message": "開発構築"}, "directory_not_exist": {"message": "存在しないフォルダー"}, "directory_not_exist_body": {"message": "フォルダー '$1' は存在しません。作成してもよろしいですか?"}, "dlconv_download_and_convert": {"message": "ダウンロードおよび変換"}, "dlconv_output_details": {"message": "出力の詳細を設定する"}, "donate": {"message": "寄付する"}, "donate_vdh": {"message": "Video DownloadHelper のヘルプ"}, "download_error": {"message": "ダウンロードエラー"}, "download_method": {"message": "ダウンロード方式"}, "download_method_not_again": {"message": "次回から既定でこの方式を使用する"}, "download_modes1": {"message": "実際のダウンロードは、ブラウザーまたはコンパニオンアプリで実行できます。"}, "download_modes2": {"message": "技術的な理由から、ブラウザーサービスからダウンロードすると、ビデオを主催 (ホスト) するサーバーによってダウンロードが拒否される可能性があり、代わりの既定のダウンロードフォルダーを定義することはできません。"}, "download_with_browser": {"message": "ブラウザーを使用する"}, "download_with_coapp": {"message": "コンパニオンアプリを使用する"}, "downloading": {"message": "ダウンロード中..."}, "edge_req_review": {"message": "または、Microsoft Edge 拡張機能ストアで素敵なレビューを書いてもらえますか?"}, "error": {"message": "エラー"}, "error_not_directory": {"message": "'$1' は存在しますが、フォルダーではありません。"}, "errors": {"message": "エラー"}, "exit_natmsgsh": {"message": "コンパニオンアプリを終了する"}, "explain_qr1": {"message": "出力されたビデオの角に透かしが含まれていることがわかります。"}, "explain_qr2": {"message": "これは、ADP の変種を選択し、変換機能が登録されていないためです。"}, "export": {"message": "設定の書き出し"}, "failed_aggregating": {"message": "\"$1\" の集約に失敗しました。"}, "failed_converting": {"message": "\"$1\" の変換に失敗しました。"}, "failed_getting_info": {"message": "\"$1\" から情報を取得できませんでした。"}, "failed_opening_directory": {"message": "ファイル フォルダーを開くことができませんでした。"}, "failed_playing_file": {"message": "ファイルを再生することができませんでした。"}, "file_dialog_date": {"message": "日付"}, "file_dialog_name": {"message": "名前"}, "file_dialog_size": {"message": "サイズ"}, "file_generated": {"message": "ファイル \"$1\" が生成されました。"}, "file_ready": {"message": "\"$1\" はダウンロード完了です。"}, "finalizing": {"message": "最終処理中..."}, "from_domain": {"message": "$1 から"}, "gallery": {"message": "ギャラリー"}, "gallery_files_types": {"message": "$1 ファイル"}, "gallery_from_domain": {"message": "$1 のギャラリー"}, "gallery_links_from_domain": {"message": "$1 からのリンク"}, "general": {"message": "全般"}, "get_conversion_license": {"message": "変換のライセンスを取得する"}, "help_translating": {"message": "翻訳のヘルプ"}, "hit_details": {"message": "検出したものの詳細"}, "hit_go_to_tab": {"message": "タブへ移動"}, "hls_streaming": {"message": "HLS ストリーミング"}, "homepage": {"message": "ホームページ"}, "import": {"message": "設定の取り込み"}, "import_invalid_format": {"message": "無効な形式"}, "in_current_tab": {"message": "現在のタブの中"}, "in_other_tab": {"message": "他のタブの中"}, "lic_mismatch1": {"message": "ライセンスは $1 用ですが、拡張ブラウザーの構築が指定されていません。"}, "lic_mismatch2": {"message": "ライセンスは $1 用ですが、拡張構築は $2 用です。"}, "lic_not_needed_linux": {"message": "Linux への貢献: ライセンスは不要です。"}, "lic_status_accepted": {"message": "ライセンス確認済み"}, "lic_status_blocked": {"message": "ライセンスブロック済み"}, "lic_status_error": {"message": "ライセンスエラー"}, "lic_status_locked": {"message": "ライセンスがロックされている (再検証中)"}, "lic_status_mismatch": {"message": "ライセンス / ブラウザーの不一致"}, "lic_status_nocoapp": {"message": "ライセンスを確認できませんでした。"}, "lic_status_unneeded": {"message": "ライセンス不要"}, "lic_status_unset": {"message": "ライセンスが未設定"}, "lic_status_unverified": {"message": "ライセンスが確認されていません。"}, "lic_status_verifying": {"message": "ライセンスの確認中..."}, "license": {"message": "ライセンス"}, "license_key": {"message": "ライセンスキー"}, "licensing": {"message": "ライセンス"}, "logs": {"message": "ログ"}, "media": {"message": "メディア"}, "merge_error": {"message": "集計エラー"}, "merge_local_files": {"message": "ローカルオーディオとビデオファイルを集約する"}, "more": {"message": "詳細..."}, "network_error_no_response": {"message": "ネットワークエラー - 応答なし"}, "network_error_status": {"message": "ネットワークエラー - 状態 $1"}, "new_sub_directory": {"message": "サブフォルダーを作成する"}, "next": {"message": "次へ"}, "no": {"message": "いいえ"}, "no_audio_in_file": {"message": "ファイル $1 にオーディオストリームがありません。"}, "no_coapp_license_unverified": {"message": "コンパニオンアプリがインストールされていないため、ライセンスを確認できませんでした。"}, "no_license_registered": {"message": "ライセンスは登録されていません。"}, "no_media_current_tab": {"message": "現在のタブで処理するメディアがありません。"}, "no_media_to_process": {"message": "処理するメディアがありません。"}, "no_media_to_process_descr": {"message": "ビデオの再生をクリックすると、ファイルを検出するのに役立ちます..."}, "no_such_hit": {"message": "検出するものはありません。"}, "no_validate_without_coapp": {"message": "ライセンスを確認するには、コンパニオンアプリをインストールする必要があります。"}, "no_video_in_file": {"message": "ファイル $1 にビデオストリームがありません。"}, "not_again_3months": {"message": "3 ヵ月間は再びこれを表示しない"}, "not_see_again": {"message": "このメッセージを再度表示しない"}, "number_type": {"message": "$1 $2"}, "ok": {"message": "OK"}, "orphan": {"message": "孤立したデータ"}, "output_configuration": {"message": "出力構成"}, "overwrite_file": {"message": "ファイル '$1' を上書きしますか?"}, "per_month": {"message": "/ 月"}, "per_year": {"message": "/ 年"}, "pinned": {"message": "ピン留めされた"}, "platform": {"message": "基盤となるコンピューター"}, "platform_info": {"message": "基盤となるコンピューター $1 $2"}, "powered_by_weh": {"message": "Weh を活用しています。"}, "preferences": {"message": "設定"}, "prod_build": {"message": "実稼働構築"}, "quality_medium": {"message": "中"}, "quality_small": {"message": "低"}, "queued": {"message": "待機中..."}, "recheck_license": {"message": "ライセンスの再確認"}, "register_converter": {"message": "変換の登録"}, "register_existing_license": {"message": "既存のライセンスを登録する"}, "registered_email": {"message": "E メール"}, "registered_key": {"message": "キー"}, "reload_addon": {"message": "拡張機能の再読み込み"}, "reload_addon_confirm": {"message": "拡張機能を再読み込みしてもよろしいですか?"}, "req_donate": {"message": "開発を支援し、少し寄付することを検討してください。"}, "req_locale": {"message": "または、アドオンを '$1' に翻訳するのに役立つかもしれません ($2 文字列がありません)?"}, "req_review": {"message": "あるいは、Mozilla アドオンのサイトで素敵なレビューを書いてもらえますか?"}, "req_review_link": {"message": "Video DownloadHelper に関するレビューを書く"}, "reset_settings": {"message": "設定をリセットする"}, "running": {"message": "稼働中"}, "save": {"message": "保存"}, "save_as": {"message": "名前を付けて保存..."}, "save_file_as": {"message": "ファイルを別名で保存..."}, "select_audio_file_to_merge": {"message": "オーディオストリームを含むファイルを選択します。"}, "select_files_to_convert": {"message": "ローカル ファイルの変換"}, "select_output_config": {"message": "出力設定を選択..."}, "select_output_directory": {"message": "出力フォルダー"}, "select_video_file_to_merge": {"message": "ビデオストリームを含むファイルを選択します。"}, "selected_media": {"message": "一括選択"}, "settings": {"message": "設定..."}, "smartname_add_domain": {"message": "スマートネーミングのルールを追加する"}, "smartname_create_rule": {"message": "ルールを作成"}, "smartname_define": {"message": "スマートネーミングのルールを定義する"}, "smartname_edit_descr": {"message": "スマートネーミングのルールでは、ホスト名に基づいてビデオ名をカスタマイズできます。"}, "smartname_empty": {"message": "スマートネーミングのルールはありません。"}, "smartname_update_rule": {"message": "ルールの更新"}, "smartnamer_delay": {"message": "名前を取得するために遅延させる時間 (ミリ秒)"}, "smartnamer_domain": {"message": "ドメイン"}, "smartnamer_get_name_from_header_url": {"message": "文書のヘッダーまたは URL から名前を取得する"}, "smartnamer_get_name_from_page_content": {"message": "ページの内容から名前を取得する"}, "smartnamer_get_name_from_page_title": {"message": "ページタイトルから名前を取得する"}, "smartnamer_get_obfuscated_name": {"message": "難読化された名前を使用する"}, "smartnamer_regexp": {"message": "正規表現"}, "smartnamer_selected_text": {"message": "選択したテキスト"}, "smartnamer_xpath_expr": {"message": "XPath 式"}, "smartnaming_rule": {"message": "スマートネーミングのルール"}, "smartnaming_rules": {"message": "スマートネーミングのルール"}, "sub_directory_name": {"message": "サブフォルダー名"}, "support_forum": {"message": "サポートフォーラム"}, "supported_sites": {"message": "対応しているサイト"}, "tbsn_quality_hd": {"message": "中品質"}, "tbsn_quality_sd": {"message": "低品質"}, "tell_me_more": {"message": "詳細情報を Web で確認する"}, "title": {"message": "Video DownloadHelper"}, "translation": {"message": "翻訳を編集する"}, "up": {"message": "↑"}, "v9_about_qr": {"message": "生成されたファイル"}, "v9_blacklist_glob": {"message": "より広範囲の一致には「*」を使用します。"}, "v9_checkbox_remember_action": {"message": "デフォルトのアクションとして記憶する"}, "v9_chrome_noyt_text2": {"message": "Firefox 版なら、Video DownloadHelper を使用して YouTube の動画をダウンロードできます。"}, "v9_chrome_noyt_text3": {"message": "申し訳ありませんが、Chrome ウェブストアでは YouTube 動画のダウンロード用の拡張機能が許可されないため、この機能を Chrome バージョンの拡張機能に含めることができませんでした。"}, "v9_chrome_premium_hls": {"message": "プレミアムステータスなしでは、HLS ダウンロードは前回の $1 分後にのみ実行できます。"}, "v9_chrome_premium_required": {"message": "プレミアムステータスが必要"}, "v9_chrome_warning_yt": {"message": "Chrome 拡張機能と YouTube に関する警告"}, "v9_coapp_help": {"message": "トラブルシュートはここをクリック"}, "v9_coapp_install": {"message": "コンパニオンアプリをインストールする"}, "v9_coapp_installed": {"message": "コンパニオンアプリがインストール済み"}, "v9_coapp_not_installed": {"message": "コンパニオンアプリが未インストール"}, "v9_coapp_outdated": {"message": "コンパニオンアプリは最新ではありません - 更新してください"}, "v9_coapp_recheck": {"message": "再チェック"}, "v9_coapp_required": {"message": "コンパニオンアプリが必要です。"}, "v9_coapp_required_text": {"message": "この操作では、外部アプリケーションを完了する必要があります。"}, "v9_coapp_unchecked": {"message": "コンパニオンアプリの確認中..."}, "v9_coapp_update": {"message": "コンパニオンアプリの更新"}, "v9_converter_needs_reg": {"message": "登録が必要"}, "v9_converter_reg_audio": {"message": "明確に、または自動変換ルールから、オーディオ専用メディアファイルの生成を要求しました。これには、登録されたコンバーターが必要です。"}, "v9_copy_settings_info_to_clipboard": {"message": "設定内容をクリップボードへ"}, "v9_dialog_audio_impossible": {"message": "この種類のメディアでは音声だけダウンロードすることはできない"}, "v9_dialog_audio_impossible_title": {"message": "オーディオをダウンロードできません"}, "v9_error": {"message": "エラー"}, "v9_explain_qr1": {"message": "出力されたビデオの角に透かしが含まれていることがわかります。"}, "v9_file_ready": {"message": "\"$1\" はダウンロード完了です。"}, "v9_filepicker_select_download_dir": {"message": "ダウンロードディレクトリの選択"}, "v9_filepicker_select_file": {"message": "ファイル選択"}, "v9_get_conversion_license": {"message": "変換のライセンスを取得する"}, "v9_lic_mismatch2": {"message": "ライセンスは $1 用ですが、拡張構築は $2 用です。"}, "v9_lic_status_accepted": {"message": "ライセンス確認済み"}, "v9_lic_status_blocked": {"message": "ライセンスブロック済み"}, "v9_lic_status_locked": {"message": "ライセンスがロックされている (再検証中)"}, "v9_lic_status_locked2": {"message": "ライセンスはロックされた。（emailを確認してください）"}, "v9_lic_status_unset": {"message": "ライセンスが未設定"}, "v9_lic_status_verifying": {"message": "ライセンスの確認中..."}, "v9_menu_item_blacklist": {"message": "ブラックリスト"}, "v9_menu_item_blacklist_domain": {"message": "ドメイン"}, "v9_menu_item_blacklist_media": {"message": "メディア"}, "v9_menu_item_blacklist_page": {"message": "ページ"}, "v9_menu_item_details": {"message": "詳細"}, "v9_menu_item_download_and_convert": {"message": "ダウンロードして変換する"}, "v9_menu_item_smartnaming": {"message": "スマートネーミング"}, "v9_mup_max_variants": {"message": "表示する解像度数"}, "v9_no": {"message": "いいえ"}, "v9_no_license_registered": {"message": "ライセンスは登録されていません。"}, "v9_no_media_current_tab": {"message": "現在のタブで処理するメディアがありません。"}, "v9_no_media_to_process_descr": {"message": "ビデオの再生をクリックすると、ファイルを検出するのに役立ちます..."}, "v9_no_validate_without_coapp": {"message": "ライセンスを確認するには、コンパニオンアプリをインストールする必要があります。"}, "v9_not_see_again": {"message": "このメッセージを再度表示しない"}, "v9_panel_copy_url_button_label": {"message": "URLをコピー"}, "v9_panel_download_as_button_label": {"message": "名前を付けてダウンロード"}, "v9_panel_download_audio_button_label": {"message": "音声をダウンロード"}, "v9_panel_download_button_label": {"message": "ダウンロード"}, "v9_panel_downloadable_variant_no_details": {"message": "詳細なし"}, "v9_panel_downloaded_delete_file_tooltip": {"message": "ファイルを削除する"}, "v9_panel_downloaded_retry_tooltip": {"message": "再ダウンロード"}, "v9_panel_downloaded_show_dir_tooltip": {"message": "ダウンロードフォルダーを表示"}, "v9_panel_downloading_stop": {"message": "停止"}, "v9_panel_error_coapp_failure_copy_report_button": {"message": "詳細をコピー"}, "v9_panel_error_coapp_failure_report_button": {"message": "チケットを開く"}, "v9_panel_error_coapp_failure_title": {"message": "ダウンロードに失敗しました"}, "v9_panel_error_coapp_too_old_button_udpate": {"message": "最新版にアップデート"}, "v9_panel_error_nocoapp_button_install": {"message": "ダウンロードとインストール"}, "v9_panel_footer_clean_all_tooltip": {"message": "検出されダウンロードされたメディアを削除します (ファイルは削除されません)"}, "v9_panel_footer_clean_tooltip": {"message": "検出されたメディアを削除する"}, "v9_panel_footer_convert_local_tooltip": {"message": "ローカルファイルを変換する"}, "v9_panel_footer_show_in_popup_tooltip": {"message": "ポップアップで表示"}, "v9_panel_footer_show_in_sidebar_tooltip": {"message": "サイドバーに表示"}, "v9_panel_variant_menu_prefer_format": {"message": "常にこの形式を好みます"}, "v9_panel_variant_menu_prefer_quality": {"message": "常にこの品質に設定する"}, "v9_panel_view_clean": {"message": "「クリーン」ボタンを表示"}, "v9_panel_view_clean_all": {"message": "「すべてクリーン」ボタンを表示"}, "v9_panel_view_hide_downloaded": {"message": "ダウンロードしたメディアを自動的に非表示にする"}, "v9_panel_view_open_settings": {"message": "その他の設定"}, "v9_panel_view_show_all_tabs": {"message": "すべてのタブを表示"}, "v9_panel_view_show_low_quality": {"message": "低品質のメディアを表示する"}, "v9_panel_view_sort_reverse": {"message": "逆ソート"}, "v9_panel_view_sort_status": {"message": "ステータスで並べ替える"}, "v9_reset": {"message": "リセット"}, "v9_save": {"message": "保存"}, "v9_settings": {"message": "設定..."}, "v9_settings_button_export": {"message": "設定のエクスポート"}, "v9_settings_button_import": {"message": "設定のインポート"}, "v9_settings_button_reload": {"message": "DownloadHelperを再起動する"}, "v9_settings_button_reset": {"message": "設定を初期値に戻す"}, "v9_settings_checkbox_force_inbrowser": {"message": "可能な限り CoApp を使用しないでください"}, "v9_settings_checkbox_forget_on_close": {"message": "タブを閉じたときに検出されたメディアを消去する"}, "v9_settings_checkbox_notification": {"message": "ダウンロードが完了したら通知を表示する"}, "v9_settings_checkbox_notification_incognito": {"message": "プライベートブラウジングの通知を表示する"}, "v9_settings_checkbox_use_legacy_ui": {"message": "従来の UI を使用する"}, "v9_settings_checkbox_use_wide_ui": {"message": "アドオンのポップアップを大きくする"}, "v9_settings_checkbox_view_convert_local": {"message": "「ローカルに変換」ボタンを表示"}, "v9_settings_download_directory": {"message": "ダウンロードディレクトリ"}, "v9_settings_download_directory_change": {"message": "変更"}, "v9_settings_license_check": {"message": "ライセンスキーを確認"}, "v9_settings_license_get": {"message": "ライセンスを取得する"}, "v9_settings_license_placeholder": {"message": "ライセンスキーをここに入力"}, "v9_settings_theme_dark": {"message": "暗い"}, "v9_settings_theme_light": {"message": "明るい"}, "v9_settings_theme_system": {"message": "システム"}, "v9_settings_theme_title": {"message": "テーマ"}, "v9_settings_variants_clear": {"message": "クリア"}, "v9_settings_variants_title": {"message": "バリエーション設定"}, "v9_short_help": {"message": "ヘルプ"}, "v9_smartnaming_max_length": {"message": "最大文字数"}, "v9_smartnaming_reset_for": {"message": "リセット:"}, "v9_smartnaming_result": {"message": "結果"}, "v9_smartnaming_save_for": {"message": "保存:"}, "v9_smartnaming_test": {"message": "テスト"}, "v9_smartnaming_title": {"message": "スマートネーミング"}, "v9_tell_me_more": {"message": "詳細情報を Web で確認する"}, "v9_user_message_auto_hide_downloaded": {"message": "ダウンロードしたメディアを自動的に非表示にしますか?"}, "v9_user_message_no_incognito_body": {"message": "Video DownloadHelper はプライベート/シークレット ウィンドウでは有効になりません。そのオプションを手動でオンにする必要があります (これは必須ではありません)。"}, "v9_user_message_no_incognito_open_settings": {"message": "ブラウザ設定で有効にする"}, "v9_user_message_no_incognito_title": {"message": "シークレットモードなし"}, "v9_user_message_one_hundred_downloads_body": {"message": "Video DownloadHelper をお楽しみいただければ幸いです :) アドオン Web サイトに素敵なレビューを書いていただけませんか?"}, "v9_vdh_notification": {"message": "Video DownloadHelper"}, "v9_weh_prefs_description_contextMenuEnabled": {"message": "ページ内を右クリックしてコマンドにアクセスします。"}, "v9_weh_prefs_label_downloadControlledMax": {"message": "最大同時ダウンロード数"}, "v9_yes": {"message": "はい"}, "v9_yt_bulk_detected_trigger": {"message": "一括ダウンロードを開始"}, "validate_license": {"message": "ライセンス登録"}, "variants_list_adp": {"message": "適応した変種"}, "variants_list_full": {"message": "変種"}, "vdh_notification": {"message": "Video DownloadHelper"}, "version": {"message": "バージョン $1"}, "video_only": {"message": "ビデオのみ"}, "video_qualities": {"message": "ビデオの品質"}, "weh_prefs_alertDialogType_option_panel": {"message": "ウィンドウ"}, "weh_prefs_alertDialogType_option_tab": {"message": "タブ"}, "weh_prefs_coappDownloads_option_ask": {"message": "その都度確認する"}, "weh_prefs_coappDownloads_option_browser": {"message": "ブラウザー"}, "weh_prefs_coappDownloads_option_coapp": {"message": "コンパニオンアプリ"}, "weh_prefs_dashOnAdp_option_audio": {"message": "オーディオをダウンロードする"}, "weh_prefs_dashOnAdp_option_audio_video": {"message": "オーディオおよびビデオを集約する"}, "weh_prefs_dashOnAdp_option_video": {"message": "ビデオをダウンロードする"}, "weh_prefs_description_adpHide": {"message": "ダウンロードの一覧に ADP の変種を表示しません。"}, "weh_prefs_description_alertDialogType": {"message": "警告ダイアログの表示方法を指定します。"}, "weh_prefs_description_autoPin": {"message": "ビデオがダウンロードされると自動的に固定します。"}, "weh_prefs_description_avplayEnabled": {"message": "コンパニオンアプリでビデオを再生できるようにします。"}, "weh_prefs_description_blacklistEnabled": {"message": "一部のサイトがメディア認識を有効にしないようにします。"}, "weh_prefs_description_bulkEnabled": {"message": "一括ダウンロード操作を有効にします。"}, "weh_prefs_description_checkCoappOnStartup": {"message": "アドオンの起動時にコンパニオンアプリが、より良いメディア検出のために利用可能かどうかを確認します。"}, "weh_prefs_description_chunkedCoappDataRequests": {"message": "コンパニオンアプリを使用して、まとまったデータを要求します。"}, "weh_prefs_description_chunkedCoappManifestsRequests": {"message": "コンパニオンアプリを使用して、明らかにまとまったデータを要求します。"}, "weh_prefs_description_chunksConcurrentDownloads": {"message": "並行してダウンロードされる最大数を指定します。"}, "weh_prefs_description_chunksEnabled": {"message": "まとまったデータのストリーミングを有効にします。"}, "weh_prefs_description_chunksPrefetchCount": {"message": "事前にダウンロードする、まとまったデータの数を指定します。"}, "weh_prefs_description_coappDownloads": {"message": "実際のダウンロードを実行するアプリケーションを指定します。"}, "weh_prefs_description_coappIdleExit": {"message": "指定したミリ秒後に自動的に閉じます。0 の場合は閉じません。"}, "weh_prefs_description_coappRestartDelay": {"message": "コンパニオンアプリの再起動時の遅延時間 (ミリ秒単位) を指定します。"}, "weh_prefs_description_coappUseProxy": {"message": "コンパニオンアプリは元の要求と同じプロキシを使用します。"}, "weh_prefs_description_contentRedirectEnabled": {"message": "サイトによっては、メディアの内容の代わりに新しい URL を返す場合があります。"}, "weh_prefs_description_contextMenuEnabled": {"message": "ページ内を右クリックしてコマンドにアクセスします。"}, "weh_prefs_description_convertControlledMax": {"message": "複数の集約タスクまたは変換タスクの最大数を指定します。"}, "weh_prefs_description_converterAggregTuneH264": {"message": "集約時に H264 同調を強制します。"}, "weh_prefs_description_converterKeepTmpFiles": {"message": "処理後に一時ファイルを削除しません。"}, "weh_prefs_description_converterThreads": {"message": "変換中に使用されるスレッド数を指定します。"}, "weh_prefs_description_dashEnabled": {"message": "DASH のまとまったデータのストリーミングを有効にします。"}, "weh_prefs_description_dashHideM4s": {"message": "ダウンロードの一覧に .m4s 項目を表示しません。"}, "weh_prefs_description_dashOnAdp": {"message": "DASH にオーディオとビデオの両方が含まれている場合の処理を指定します。"}, "weh_prefs_description_dialogAutoClose": {"message": "フォーカスを失うとダイアログが閉じられます。"}, "weh_prefs_description_downloadControlledMax": {"message": "同時に実行されているアドオンで生成されたダウンロードの数を制御して、帯域幅を一定に保ちます。"}, "weh_prefs_description_downloadRetries": {"message": "ダウンロードの再試行回数を指定します。"}, "weh_prefs_description_downloadRetryDelay": {"message": "ダウンロードの再試行の遅延間隔 (ミリ秒) を指定します。"}, "weh_prefs_description_downloadStreamControlledMax": {"message": "1 つの項目にダウンロードされているストリームの数を制御します。"}, "weh_prefs_description_fileDialogType": {"message": "ファイルダイアログの表示方法を指定します。"}, "weh_prefs_description_galleryNaming": {"message": "ギャラリーからファイルに名前を付ける方法を指定します。"}, "weh_prefs_description_hitsGotoTab": {"message": "項目の説明にリンクを表示してビデオタブに切り替えます。"}, "weh_prefs_description_hlsDownloadAsM2ts": {"message": "M2TS として HLS ストリームをダウンロードします。"}, "weh_prefs_description_hlsEnabled": {"message": "HLS のまとまったデータのストリーミングを有効にします。"}, "weh_prefs_description_hlsEndTimeout": {"message": "新しいHLSチャンクの待機を停止するタイムアウト秒数"}, "weh_prefs_description_hlsRememberPrevLiveChunks": {"message": "以前のまとまったデータの HLS ライブを記憶します。"}, "weh_prefs_description_iconActivation": {"message": "ツールバーのアイコンでの検出を通知するタイミングを指定します。"}, "weh_prefs_description_iconBadge": {"message": "ツールバーアイコンのバッジ情報を表示します。"}, "weh_prefs_description_ignoreProtectedVariants": {"message": "保護されている変種は表示しません。"}, "weh_prefs_description_lastDownloadDirectory": {"message": "コンパニオンアプリはダウンロード処理でのみ使用します。"}, "weh_prefs_description_mediaExtensions": {"message": "メディアと見なされるファイル拡張子を指定します。"}, "weh_prefs_description_medialinkAutoDetect": {"message": "各ページの読み込み時に実行します (性能に影響を与える可能性があります)。"}, "weh_prefs_description_medialinkExtensions": {"message": "ギャラリーキャプチャーと見なされるファイル拡張子を指定します。"}, "weh_prefs_description_medialinkMaxHits": {"message": "ギャラリーとして検出された項目の数を制限します。"}, "weh_prefs_description_medialinkMinFilesPerGroup": {"message": "ギャラリーとして検出される最小の項目を指定します。"}, "weh_prefs_description_medialinkMinImgSize": {"message": "画像ギャラリーと見なされる最小画像サイズを指定します。"}, "weh_prefs_description_medialinkScanImages": {"message": "ページ内の画像を検出します。"}, "weh_prefs_description_medialinkScanLinks": {"message": "ページから直接リンクされたメディアを検出します。"}, "weh_prefs_description_mediaweightMinSize": {"message": "指定のサイズを下回る検出は無視します。"}, "weh_prefs_description_mediaweightThreshold": {"message": "指定のサイズを超えている場合はメディアを強制的に検出します。"}, "weh_prefs_description_monitorNetworkRequests": {"message": "元の要求と同じヘッダーを使用してダウンロードします。"}, "weh_prefs_description_mpegtsHideTs": {"message": ".ts 項目をダウンロードの一覧に表示しません。"}, "weh_prefs_description_networkFilterOut": {"message": "一部のメディア URL を無視する正規表現を指定します。"}, "weh_prefs_description_networkProbe": {"message": "ネットワークの通信量をスキャンして検出します。"}, "weh_prefs_description_noPrivateNotification": {"message": "非公開の検出の通知はしません。"}, "weh_prefs_description_notifyReady": {"message": "処理時に通知します。"}, "weh_prefs_description_orphanExpiration": {"message": "孤立した検出データを削除するまでの秒単位の時間を指定します。"}, "weh_prefs_description_qualitiesMaxVariants": {"message": "同じビデオの表示される変種の最大数を指定します。"}, "weh_prefs_description_rememberLastDir": {"message": "最後にダウンロードされたフォルダーを既定の場所として使用します。"}, "weh_prefs_description_smartnamerFnameMaxlen": {"message": "生成されたファイル名が指定サイズを超えないようにします。"}, "weh_prefs_description_smartnamerFnameSpaces": {"message": "ビデオ名の空白を処理する方法を指定します。"}, "weh_prefs_description_tbsnEnabled": {"message": "Facebook の動画の検出とダウンロードを許可します。"}, "weh_prefs_description_titleMode": {"message": "長いビデオタイトルをメインパネルにどのように表示するかを指定します。"}, "weh_prefs_description_toolsMenuEnabled": {"message": "[ツール] メニューからコマンドにアクセスします。"}, "weh_prefs_fileDialogType_option_panel": {"message": "ウィンドウ"}, "weh_prefs_fileDialogType_option_tab": {"message": "タブ"}, "weh_prefs_galleryNaming_option_index_url": {"message": "インデックス - URL"}, "weh_prefs_galleryNaming_option_type_index": {"message": "種類 - インデックス"}, "weh_prefs_galleryNaming_option_url": {"message": "URL"}, "weh_prefs_iconActivation_option_anytab": {"message": "任意のタブから検出"}, "weh_prefs_iconActivation_option_currenttab": {"message": "現在のタブから検出"}, "weh_prefs_iconBadge_option_activetab": {"message": "有効なタブのメディア"}, "weh_prefs_iconBadge_option_anytab": {"message": "任意のタブのメディア"}, "weh_prefs_iconBadge_option_mixed": {"message": "状況に応じて色分けして表示"}, "weh_prefs_iconBadge_option_none": {"message": "なし"}, "weh_prefs_iconBadge_option_pinned": {"message": "ピン留めされた検出"}, "weh_prefs_iconBadge_option_tasks": {"message": "実行中のタスク"}, "weh_prefs_label_adpHide": {"message": "ADP 変種を表示しない"}, "weh_prefs_label_alertDialogType": {"message": "警告ダイアログ"}, "weh_prefs_label_autoPin": {"message": "自動ピン留め"}, "weh_prefs_label_avplayEnabled": {"message": "使用可能なプレーヤー"}, "weh_prefs_label_blacklistEnabled": {"message": "ブラックリストを有効にする"}, "weh_prefs_label_bulkEnabled": {"message": "一括ダウンロードを有効にする"}, "weh_prefs_label_checkCoappOnStartup": {"message": "起動時にコンパニオンアプリを確認する"}, "weh_prefs_label_chunkedCoappDataRequests": {"message": "まとまったデータをコンパニオンアプリに要求する"}, "weh_prefs_label_chunkedCoappManifestsRequests": {"message": "明らかにまとまったデータをコンパニオンアプリに要求する"}, "weh_prefs_label_chunksConcurrentDownloads": {"message": "並行して、まとまったデータをダウンロード"}, "weh_prefs_label_chunksEnabled": {"message": "まとまったデータのストリーミング"}, "weh_prefs_label_chunksPrefetchCount": {"message": "事前に読み取る、まとまったデータの数"}, "weh_prefs_label_coappDownloads": {"message": "ダウンロード処理"}, "weh_prefs_label_coappIdleExit": {"message": "コンパニオンアプリの無操作の終了時間"}, "weh_prefs_label_coappRestartDelay": {"message": "コンパニオンアプリの再起動の遅延時間"}, "weh_prefs_label_coappUseProxy": {"message": "コンパニオンアプリのプロキシ"}, "weh_prefs_label_contentRedirectEnabled": {"message": "内容の宛先の書き替えを有効にする"}, "weh_prefs_label_contextMenuEnabled": {"message": "右クリックメニュー"}, "weh_prefs_label_convertControlledMax": {"message": "複数の変換動作"}, "weh_prefs_label_converterAggregTuneH264": {"message": "H264 同調"}, "weh_prefs_label_converterKeepTmpFiles": {"message": "一時ファイルを保存する"}, "weh_prefs_label_converterThreads": {"message": "変換のスレッド数"}, "weh_prefs_label_dashEnabled": {"message": "DASH を有効にする"}, "weh_prefs_label_dashHideM4s": {"message": ".m4s を表示しない"}, "weh_prefs_label_dashOnAdp": {"message": "DASH ストリーム"}, "weh_prefs_label_dialogAutoClose": {"message": "ダイアログを自動的に閉じる"}, "weh_prefs_label_downloadControlledMax": {"message": "最大同時ダウンロード数"}, "weh_prefs_label_downloadRetries": {"message": "ダウンロードの再試行"}, "weh_prefs_label_downloadRetryDelay": {"message": "再試行の遅延"}, "weh_prefs_label_downloadStreamControlledMax": {"message": "最大同時ストリームダウンロード数"}, "weh_prefs_label_fileDialogType": {"message": "ファイルダイアログ"}, "weh_prefs_label_galleryNaming": {"message": "ギャラリーファイルの名前付け"}, "weh_prefs_label_hitsGotoTab": {"message": "[タブへ移動] 操作を表示する"}, "weh_prefs_label_hlsDownloadAsM2ts": {"message": "HLS を M2TS でダウンロードする"}, "weh_prefs_label_hlsEnabled": {"message": "HLS を有効にする"}, "weh_prefs_label_hlsEndTimeout": {"message": "HLSの終了タイムアウト"}, "weh_prefs_label_hlsRememberPrevLiveChunks": {"message": "HLS ライブ履歴"}, "weh_prefs_label_iconActivation": {"message": "アイコンでの通知"}, "weh_prefs_label_iconBadge": {"message": "アイコンのバッジ情報"}, "weh_prefs_label_ignoreProtectedVariants": {"message": "保護された変種を無視する"}, "weh_prefs_label_lastDownloadDirectory": {"message": "既定のダウンロードフォルダー"}, "weh_prefs_label_mediaExtensions": {"message": "拡張子を検出する"}, "weh_prefs_label_medialinkAutoDetect": {"message": "ギャラリーの自動検出"}, "weh_prefs_label_medialinkExtensions": {"message": "メディアのリンクの拡張子"}, "weh_prefs_label_medialinkMaxHits": {"message": "最大項目数"}, "weh_prefs_label_medialinkMinFilesPerGroup": {"message": "最小項目数"}, "weh_prefs_label_medialinkMinImgSize": {"message": "最小画像サイズ"}, "weh_prefs_label_medialinkScanImages": {"message": "埋め込み画像を検出する"}, "weh_prefs_label_medialinkScanLinks": {"message": "メディアへのリンクを検出する"}, "weh_prefs_label_mediaweightMinSize": {"message": "最小サイズ"}, "weh_prefs_label_mediaweightThreshold": {"message": "サイズ閾値"}, "weh_prefs_label_monitorNetworkRequests": {"message": "ヘッダーを要求する"}, "weh_prefs_label_mpegtsHideTs": {"message": ".ts を表示しない"}, "weh_prefs_label_networkFilterOut": {"message": "ネットワークの除外するフィルター"}, "weh_prefs_label_networkProbe": {"message": "ネットワークの厳密な調査"}, "weh_prefs_label_noPrivateNotification": {"message": "非公開通知"}, "weh_prefs_label_notifyReady": {"message": "通知"}, "weh_prefs_label_orphanExpiration": {"message": "孤立したデータの有効期限の時間"}, "weh_prefs_label_qualitiesMaxVariants": {"message": "最大の変種数"}, "weh_prefs_label_rememberLastDir": {"message": "最後のフォルダーを記憶する"}, "weh_prefs_label_smartnamerFnameMaxlen": {"message": "ファイル名の最大の長さ"}, "weh_prefs_label_smartnamerFnameSpaces": {"message": "メインパネルの長いタイトル"}, "weh_prefs_label_tbsnEnabled": {"message": "Facebook の対応"}, "weh_prefs_label_titleMode": {"message": "メインパネルの長いタイトル"}, "weh_prefs_label_toolsMenuEnabled": {"message": "[ツール] メニュー"}, "weh_prefs_smartnamerFnameSpaces_option_hyphen": {"message": "ハイフン (-) に置換"}, "weh_prefs_smartnamerFnameSpaces_option_keep": {"message": "保持する"}, "weh_prefs_smartnamerFnameSpaces_option_remove": {"message": "削除する"}, "weh_prefs_smartnamerFnameSpaces_option_underscore": {"message": "アンダースコア (_) に置換"}, "weh_prefs_titleMode_option_left": {"message": "左側を省略して表示"}, "weh_prefs_titleMode_option_multiline": {"message": "数行に渡って表示"}, "weh_prefs_titleMode_option_right": {"message": "右側を省略して表示"}, "yes": {"message": "はい"}, "you_downloaded_n_videos": {"message": "Video DownloadHelper で $1 個目のファイルをダウンロードしました。"}}