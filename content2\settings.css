.inline-button { text-decoration: underline; color: var(--sl-color-primary-600); cursor: pointer; text-decoration-color: var(--sl-color-primary-400); }
.inline-button:not(:first-child) { margin-left: var(--sl-spacing-small); }
.inline-button:not(:last-child) { margin-right: var(--sl-spacing-small); }

#drawer-settings::part(header) {
  border-bottom: 1px solid var(--sl-color-neutral-200);
}

#drawer-settings hbox {
  flex-wrap: wrap;
}

#drawer-settings h2 {
  font-weight: bold;
}

#drawer-settings code {
  overflow: hidden;
  text-overflow: ellipsis;
  font-style: italic;
  max-width: 100%;
  overflow-wrap: break-word;
}

html:not(.sidebar) #drawer-settings {
  --size: 100vh;
}

html.sidebar #drawer-settings {
  --size: 70vh;
}

.settings-success {
  color: var(--sl-color-success-700);
}

.settings-warning {
  color: var(--sl-color-warning-700);
}

.coapp-error {
  max-width: 100%;
  text-wrap: wrap;
}

#drawer-settings-coapp:not([coapp-status="checking"]) > #settings-coapp-checking,
#drawer-settings-coapp:not([coapp-status="not-found"]) > #settings-coapp-not-found,
#drawer-settings-coapp:not([coapp-status="found"]):not([coapp-status="outdated"]) > #settings-coapp-found {
  display: none;
}

#drawer-settings-coapp[coapp-status="found"] #coapp-outdated,
#drawer-settings-coapp[coapp-status="outdated"] #coapp-up-to-date {
  display: none;
}

#drawer-settings-license[license-status="unneeded"],
#drawer-settings-license:not([license-status="nocoapp"]) > #settings-license-nocoapp,
#drawer-settings-license:not([license-status="checking"]) > #settings-license-checking,
#drawer-settings-license:not([license-status="checked"]) > #settings-license-checked {
  display: none;
}

#settings-bottom-buttons {
  gap: var(--sl-spacing-small);
}
