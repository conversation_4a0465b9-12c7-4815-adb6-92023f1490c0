"use strict";(()=>{var q=Object.create;var N=Object.defineProperty;var W=Object.getOwnPropertyDescriptor;var I=Object.getOwnPropertyNames;var U=Object.getPrototypeOf,D=Object.prototype.hasOwnProperty;var Z=(m,i)=>()=>(i||m((i={exports:{}}).exports,i),i.exports);var z=(m,i,c,u)=>{if(i&&typeof i=="object"||typeof i=="function")for(let d of I(i))!D.call(m,d)&&d!==c&&N(m,d,{get:()=>i[d],enumerable:!(u=W(i,d))||u.enumerable});return m};var G=(m,i,c)=>(c=m!=null?q(U(m)):{},z(i||!m||!m.__esModule?N(c,"default",{value:m,enumerable:!0}):c,m));var _=Z((T,R)=>{(function(m,i){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],i);else if(typeof T<"u")i(R);else{var c={exports:{}};i(c),m.browser=c.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:T,function(m){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let i="The message port closed before a response was received.",c=u=>{let d={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(d).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class E extends WeakMap{constructor(s,n=void 0){super(n),this.createItem=s}get(s){return this.has(s)||this.set(s,this.createItem(s)),super.get(s)}}let L=e=>e&&typeof e=="object"&&typeof e.then=="function",M=(e,s)=>(...n)=>{u.runtime.lastError?e.reject(new Error(u.runtime.lastError.message)):s.singleCallbackArg||n.length<=1&&s.singleCallbackArg!==!1?e.resolve(n[0]):e.resolve(n)},h=e=>e==1?"argument":"arguments",$=(e,s)=>function(g,...a){if(a.length<s.minArgs)throw new Error(`Expected at least ${s.minArgs} ${h(s.minArgs)} for ${e}(), got ${a.length}`);if(a.length>s.maxArgs)throw new Error(`Expected at most ${s.maxArgs} ${h(s.maxArgs)} for ${e}(), got ${a.length}`);return new Promise((A,o)=>{if(s.fallbackToNoCallback)try{g[e](...a,M({resolve:A,reject:o},s))}catch(r){console.warn(`${e} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,r),g[e](...a),s.fallbackToNoCallback=!1,s.noCallback=!0,A()}else s.noCallback?(g[e](...a),A()):g[e](...a,M({resolve:A,reject:o},s))})},P=(e,s,n)=>new Proxy(s,{apply(g,a,A){return n.call(a,e,...A)}}),w=Function.call.bind(Object.prototype.hasOwnProperty),p=(e,s={},n={})=>{let g=Object.create(null),a={has(o,r){return r in e||r in g},get(o,r,l){if(r in g)return g[r];if(!(r in e))return;let t=e[r];if(typeof t=="function")if(typeof s[r]=="function")t=P(e,e[r],s[r]);else if(w(n,r)){let f=$(r,n[r]);t=P(e,e[r],f)}else t=t.bind(e);else if(typeof t=="object"&&t!==null&&(w(s,r)||w(n,r)))t=p(t,s[r],n[r]);else if(w(n,"*"))t=p(t,s[r],n["*"]);else return Object.defineProperty(g,r,{configurable:!0,enumerable:!0,get(){return e[r]},set(f){e[r]=f}}),t;return g[r]=t,t},set(o,r,l,t){return r in g?g[r]=l:e[r]=l,!0},defineProperty(o,r,l){return Reflect.defineProperty(g,r,l)},deleteProperty(o,r){return Reflect.deleteProperty(g,r)}},A=Object.create(e);return new Proxy(A,a)},k=e=>({addListener(s,n,...g){s.addListener(e.get(n),...g)},hasListener(s,n){return s.hasListener(e.get(n))},removeListener(s,n){s.removeListener(e.get(n))}}),j=new E(e=>typeof e!="function"?e:function(n){let g=p(n,{},{getContent:{minArgs:0,maxArgs:0}});e(g)}),S=new E(e=>typeof e!="function"?e:function(n,g,a){let A=!1,o,r=new Promise(b=>{o=function(x){A=!0,b(x)}}),l;try{l=e(n,g,o)}catch(b){l=Promise.reject(b)}let t=l!==!0&&L(l);if(l!==!0&&!t&&!A)return!1;let f=b=>{b.then(x=>{a(x)},x=>{let C;x&&(x instanceof Error||typeof x.message=="string")?C=x.message:C="An unexpected error occurred",a({__mozWebExtensionPolyfillReject__:!0,message:C})}).catch(x=>{console.error("Failed to send onMessage rejected reply",x)})};return f(t?l:r),!0}),O=({reject:e,resolve:s},n)=>{u.runtime.lastError?u.runtime.lastError.message===i?s():e(new Error(u.runtime.lastError.message)):n&&n.__mozWebExtensionPolyfillReject__?e(new Error(n.message)):s(n)},F=(e,s,n,...g)=>{if(g.length<s.minArgs)throw new Error(`Expected at least ${s.minArgs} ${h(s.minArgs)} for ${e}(), got ${g.length}`);if(g.length>s.maxArgs)throw new Error(`Expected at most ${s.maxArgs} ${h(s.maxArgs)} for ${e}(), got ${g.length}`);return new Promise((a,A)=>{let o=O.bind(null,{resolve:a,reject:A});g.push(o),n.sendMessage(...g)})},B={devtools:{network:{onRequestFinished:k(j)}},runtime:{onMessage:k(S),onMessageExternal:k(S),sendMessage:F.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:F.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},y={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return d.privacy={network:{"*":y},services:{"*":y},websites:{"*":y}},p(u,B,d)};m.exports=c(chrome)}else m.exports=globalThis.browser})});var v=G(_(),1);function H(m){v.default.runtime.sendMessage(m)}H("request_license_status");v.default.runtime.onMessage.addListener(async m=>{if("license_status"in m){let i=m.license_status;("unneeded"in i||"unset"in i)&&document.body.classList.add("show-donation")}});})();
