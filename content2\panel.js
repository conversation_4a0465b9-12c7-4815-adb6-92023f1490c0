var st=Object.create;var Ne=Object.defineProperty;var lt=Object.getOwnPropertyDescriptor;var ut=Object.getOwnPropertyNames;var dt=Object.getPrototypeOf,ct=Object.prototype.hasOwnProperty;var mt=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var pt=(e,t,o,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of ut(t))!ct.call(e,r)&&r!==o&&Ne(e,r,{get:()=>t[r],enumerable:!(n=lt(t,r))||n.enumerable});return e};var ie=(e,t,o)=>(o=e!=null?st(dt(e)):{},pt(t||!e||!e.__esModule?Ne(o,"default",{value:e,enumerable:!0}):o,e));var Y=mt((_e,$e)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof _e<"u")t($e);else{var o={exports:{}};t(o),e.browser=o.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:_e,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",o=n=>{let r={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(r).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class a extends WeakMap{constructor(p,_=void 0){super(_),this.createItem=p}get(p){return this.has(p)||this.set(p,this.createItem(p)),super.get(p)}}let i=m=>m&&typeof m=="object"&&typeof m.then=="function",l=(m,p)=>(..._)=>{n.runtime.lastError?m.reject(new Error(n.runtime.lastError.message)):p.singleCallbackArg||_.length<=1&&p.singleCallbackArg!==!1?m.resolve(_[0]):m.resolve(_)},u=m=>m==1?"argument":"arguments",s=(m,p)=>function(h,...E){if(E.length<p.minArgs)throw new Error(`Expected at least ${p.minArgs} ${u(p.minArgs)} for ${m}(), got ${E.length}`);if(E.length>p.maxArgs)throw new Error(`Expected at most ${p.maxArgs} ${u(p.maxArgs)} for ${m}(), got ${E.length}`);return new Promise((I,V)=>{if(p.fallbackToNoCallback)try{h[m](...E,l({resolve:I,reject:V},p))}catch(g){console.warn(`${m} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,g),h[m](...E),p.fallbackToNoCallback=!1,p.noCallback=!0,I()}else p.noCallback?(h[m](...E),I()):h[m](...E,l({resolve:I,reject:V},p))})},d=(m,p,_)=>new Proxy(p,{apply(h,E,I){return _.call(E,m,...I)}}),c=Function.call.bind(Object.prototype.hasOwnProperty),w=(m,p={},_={})=>{let h=Object.create(null),E={has(V,g){return g in m||g in h},get(V,g,P){if(g in h)return h[g];if(!(g in m))return;let k=m[g];if(typeof k=="function")if(typeof p[g]=="function")k=d(m,m[g],p[g]);else if(c(_,g)){let K=s(g,_[g]);k=d(m,m[g],K)}else k=k.bind(m);else if(typeof k=="object"&&k!==null&&(c(p,g)||c(_,g)))k=w(k,p[g],_[g]);else if(c(_,"*"))k=w(k,p[g],_["*"]);else return Object.defineProperty(h,g,{configurable:!0,enumerable:!0,get(){return m[g]},set(K){m[g]=K}}),k;return h[g]=k,k},set(V,g,P,k){return g in h?h[g]=P:m[g]=P,!0},defineProperty(V,g,P){return Reflect.defineProperty(h,g,P)},deleteProperty(V,g){return Reflect.deleteProperty(h,g)}},I=Object.create(m);return new Proxy(I,E)},A=m=>({addListener(p,_,...h){p.addListener(m.get(_),...h)},hasListener(p,_){return p.hasListener(m.get(_))},removeListener(p,_){p.removeListener(m.get(_))}}),T=new a(m=>typeof m!="function"?m:function(_){let h=w(_,{},{getContent:{minArgs:0,maxArgs:0}});m(h)}),M=new a(m=>typeof m!="function"?m:function(_,h,E){let I=!1,V,g=new Promise(ae=>{V=function($){I=!0,ae($)}}),P;try{P=m(_,h,V)}catch(ae){P=Promise.reject(ae)}let k=P!==!0&&i(P);if(P!==!0&&!k&&!I)return!1;let K=ae=>{ae.then($=>{E($)},$=>{let fe;$&&($ instanceof Error||typeof $.message=="string")?fe=$.message:fe="An unexpected error occurred",E({__mozWebExtensionPolyfillReject__:!0,message:fe})}).catch($=>{console.error("Failed to send onMessage rejected reply",$)})};return K(k?P:g),!0}),qe=({reject:m,resolve:p},_)=>{n.runtime.lastError?n.runtime.lastError.message===t?p():m(new Error(n.runtime.lastError.message)):_&&_.__mozWebExtensionPolyfillReject__?m(new Error(_.message)):p(_)},me=(m,p,_,...h)=>{if(h.length<p.minArgs)throw new Error(`Expected at least ${p.minArgs} ${u(p.minArgs)} for ${m}(), got ${h.length}`);if(h.length>p.maxArgs)throw new Error(`Expected at most ${p.maxArgs} ${u(p.maxArgs)} for ${m}(), got ${h.length}`);return new Promise((E,I)=>{let V=qe.bind(null,{resolve:E,reject:I});h.push(V),_.sendMessage(...h)})},re={devtools:{network:{onRequestFinished:A(T)}},runtime:{onMessage:A(M),onMessageExternal:A(M),sendMessage:me.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:me.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},ne={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return r.privacy={network:{"*":ne},services:{"*":ne},websites:{"*":ne}},w(n,re,r)};e.exports=o(chrome)}else e.exports=globalThis.browser})});function z(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var ft=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,o){return t},e.prototype.mapOrElse=function(t,o){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return D(t)},e.prototype.toString=function(){return"None"},e}(),f=new ft;Object.freeze(f);var gt=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return v(t(this.value))},e.prototype.mapOr=function(t,o){return o(this.value)},e.prototype.mapOrElse=function(t,o){return o(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return y(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(z(this.value),")")},e.EMPTY=new e(void 0),e}(),v=gt,B;(function(e){function t(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];for(var i=[],l=0,u=r;l<u.length;l++){var s=u[l];if(s.isSome())i.push(s.value);else return s}return v(i)}e.all=t;function o(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];for(var i=0,l=r;i<l.length;i++){var u=l[i];return u.isSome(),u}return f}e.any=o;function n(r){return r instanceof v||r===f}e.isOption=n})(B||(B={}));var _t=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var o=new Error().stack.split(`
`).slice(2);o&&o.length>0&&o[0].includes("ErrImpl")&&o.shift(),this._stack=o.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(z(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(z(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new D(t(this.error))},e.prototype.mapOr=function(t,o){return t},e.prototype.mapOrElse=function(t,o){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return f},e.prototype.toString=function(){return"Err(".concat(z(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new ge(this)},e.EMPTY=new e(void 0),e}();var D=_t,wt=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(z(this.value)),{cause:this.value})},e.prototype.map=function(t){return new y(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,o){return o(this.value)},e.prototype.mapOrElse=function(t,o){return o(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return v(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(z(this.value),")")},e.prototype.toAsyncResult=function(){return new ge(this)},e.EMPTY=new e(void 0),e}();var y=wt,J;(function(e){function t(){for(var i=[],l=0;l<arguments.length;l++)i[l]=arguments[l];for(var u=[],s=0,d=i;s<d.length;s++){var c=d[s];if(c.isOk())u.push(c.value);else return c}return new y(u)}e.all=t;function o(){for(var i=[],l=0;l<arguments.length;l++)i[l]=arguments[l];for(var u=[],s=0,d=i;s<d.length;s++){var c=d[s];if(c.isOk())return c;u.push(c.error)}return new D(u)}e.any=o;function n(i){try{return new y(i())}catch(l){return new D(l)}}e.wrap=n;function r(i){try{return i().then(function(l){return new y(l)}).catch(function(l){return new D(l)})}catch(l){return Promise.resolve(new D(l))}}e.wrapAsync=r;function a(i){return i instanceof D||i instanceof y}e.isResult=a})(J||(J={}));var Le=function(e,t,o,n){function r(a){return a instanceof o?a:new o(function(i){i(a)})}return new(o||(o=Promise))(function(a,i){function l(d){try{s(n.next(d))}catch(c){i(c)}}function u(d){try{s(n.throw(d))}catch(c){i(c)}}function s(d){d.done?a(d.value):r(d.value).then(l,u)}s((n=n.apply(e,t||[])).next())})},je=function(e,t){var o={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,r,a,i;return i={next:l(0),throw:l(1),return:l(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function l(s){return function(d){return u([s,d])}}function u(s){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,s[0]&&(o=0)),o;)try{if(n=1,r&&(a=s[0]&2?r.return:s[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,s[1])).done)return a;switch(r=0,a&&(s=[s[0]&2,a.value]),s[0]){case 0:case 1:a=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,r=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(a=o.trys,!(a=a.length>0&&a[a.length-1])&&(s[0]===6||s[0]===2)){o=0;continue}if(s[0]===3&&(!a||s[1]>a[0]&&s[1]<a[3])){o.label=s[1];break}if(s[0]===6&&o.label<a[1]){o.label=a[1],a=s;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(s);break}a[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(d){s=[6,d],r=0}finally{n=a=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}},ge=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var o=this;return this.thenInternal(function(n){return Le(o,void 0,void 0,function(){var r;return je(this,function(a){return n.isErr()?[2,n]:(r=t(n.value),[2,r instanceof e?r.promise:r])})})})},e.prototype.map=function(t){var o=this;return this.thenInternal(function(n){return Le(o,void 0,void 0,function(){var r;return je(this,function(a){switch(a.label){case 0:return n.isErr()?[2,n]:(r=y,[4,t(n.value)]);case 1:return[2,r.apply(void 0,[a.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();function C(e){return Object.assign(e.prototype,{find:function(t){for(let o of this)if(t(o))return v(o);return f},count:function(t){return this.reduce((o,n)=>(t(n)&&o++,o),0)},reduce:function(t,o){let n=o;for(let r of this)n=t(n,r);return n},every:function(t){return!this.any(o=>!t(o))},any:function(t){for(let o of this)if(t(o))return!0;return!1},map:function(t){return this.filterMap(o=>v(t(o)))},filter:function(t){return this.filterMap(o=>t(o)?v(o):f)},enumerate:function(){let t=this;return C(function*(){let o=0;for(let n of t)yield[o,n],o++})()},filterMap:function(t){let o=this;return C(function*(){for(let n of o){let r=t(n);r.isSome()&&(yield r.unwrap())}})()},sort:function(t){let o=this.toArray();return o.sort(t),o},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return C(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return C(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return C(function*(){for(let t of e)yield t})()});var S=ie(Y(),1);var W=ie(Y(),1);var Z="google";function we(e,t,o){let n=Z=="mozilla";if(!n&&(chrome.sidePanel&&chrome.sidePanel.setPanelBehavior?(chrome.sidePanel.setOptions({enabled:e}),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:e})):e=!1,o))if(e)t!=0&&chrome.sidePanel?.open?.({windowId:t});else try{chrome.action.openPopup()}catch{}n&&!e&&(W.default.browserAction.setPopup({popup:"/content2/popup.html"}),W.default.sidebarAction.setPanel({panel:null}),o&&W.default.sidebarAction.close()),n&&e&&(W.default.browserAction.setPopup({popup:null}),W.default.sidebarAction.setPanel({panel:"/content2/sidebar.html"}),o&&W.default.sidebarAction.open())}var Re=ie(Y(),1);function F(e){return e?e.replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll('"',"&quot;").replaceAll("'","&#039;"):""}function Q(e,t,o){let n=()=>(console.error(`Requesting unknown i18n string ${e}`),e);t=t.map(r=>r.toString()).map(F);try{if(e in o){let r=o[e],a=1;for(let i=0;i<t.length;i++)r=r.replace(`$${a}`,t[i]);return r}else{let r=Re.default.i18n.getMessage(e,t);return r||n()}}catch{return n()}}function ue(e,t){for(let o of Array.from(e.querySelectorAll("[data-i18n]"))){let n=o.dataset.i18nArgs,r=o.dataset.i18nAttr,a;n?a=Q(o.dataset.i18n,JSON.parse(n),t):a=Q(o.dataset.i18n,[],t),r?o.setAttribute(r,a):o.textContent=a}}var He=(e,t)=>typeof e[t]=="string";function q(e){try{if(He(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return y(e.__serializer_value);if(e.__serializer_tag==="regex"){let n=new RegExp(e.__serializer_value);return y(n)}else if(e.__serializer_tag==="array"){let n=[];for(let r of e.__serializer_value){let a=q(r);if(a.isErr())return a;n.push(a.unwrap())}return y(n)}else if(e.__serializer_tag==="map"){let n=[];for(let r of e.__serializer_value){let a=q(r);if(a.isErr())return a;n.push(a.unwrap())}return y(new Map(n))}else if(e.__serializer_tag==="set"){let n=[];for(let r of e.__serializer_value){let a=q(r);if(a.isErr())return a;n.push(a.unwrap())}return y(new Set(n))}else if(e.__serializer_tag==="result_ok"){let n=e.__serializer_value,r=q(n);return r.isErr()?r:y(y(r.unwrap()))}else if(e.__serializer_tag==="result_err"){let n=e.__serializer_value,r=q(n);return r.isErr()?r:y(D(r.unwrap()))}else if(e.__serializer_tag==="option_some"){let n=e.__serializer_value,r=q(n);return r.isErr()?r:y(v(r.unwrap()))}else if(e.__serializer_tag==="option_none")return y(f)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return D("This object was not serialized with Serialize");let o={};for(let n of Object.keys(e))if(typeof n=="string"){let r=q(e[n]);if(r.isErr())return r;o[n]=r.unwrap()}return y(o)}catch{return D("Failed to inspect object. Not JSON?")}}function N(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return y({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return y({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let o=e.map(a=>N(a)),n=o.as_iter().find(a=>a.isErr());if(n.isSome())return n.unwrap();let r=o.as_iter().map(a=>a.unwrap()).toArray();return y({__serializer_tag:"array",__serializer_value:r})}else if(e instanceof Map){let o=[...e.entries()].map(a=>N(a)),n=o.as_iter().find(a=>a.isErr());if(n.isSome())return n.unwrap();let r=o.as_iter().map(a=>a.unwrap()).toArray();return y({__serializer_tag:"map",__serializer_value:r})}else if(e instanceof Set){let o=[...e.values()].map(a=>N(a)),n=o.as_iter().find(a=>a.isErr());if(n.isSome())return n.unwrap();let r=o.as_iter().map(a=>a.unwrap()).toArray();return y({__serializer_tag:"set",__serializer_value:r})}else if(J.isResult(e))if(e.isOk()){let o=e.unwrap(),n=N(o);return n.isErr()?n:y({__serializer_tag:"result_ok",__serializer_value:n.unwrap()})}else{let o=e.unwrapErr(),n=N(o);return n.isErr()?n:y({__serializer_tag:"result_err",__serializer_value:n.unwrap()})}else if(B.isOption(e))if(e.isSome()){let o=e.unwrap(),n=N(o);return n.isErr()?n:y({__serializer_tag:"option_some",__serializer_value:n.unwrap()})}else return y({__serializer_tag:"option_none"});else if(t==="object"){let o={},n=e;for(let r of Object.keys(e)){let a=n[r],i=N(a);if(i.isErr())continue;let l=i.unwrap();o[r]=l}return y(o)}else return D("Unsupported value")}function H(e){if(typeof e>"u")return"undef";if(typeof e=="string"||typeof e=="number"||typeof e=="boolean"||e==null)return e;if(e instanceof RegExp)return e.source;if(Array.isArray(e))return e.map(H);if(e instanceof Map)return[...e.values()].map(H);if(e instanceof Set)return[...e.values()].map(H);if(J.isResult(e))return e.isOk()?H(e.unwrap()):H(e.unwrapErr());if(B.isOption(e))return e.isSome()?H(e.unwrap()):"None";if(typeof e=="object"){let t={},o=e;for(let n of Object.keys(e)){let r=o[n];t[n]=H(r)}return t}else return"???"}function Ue(e,t){let o=new Map,n=[];{for(let u of e.downloadable.values()){o.has(u.tab_id)||o.set(u.tab_id,{downloadables:[],filter_out_low_quality:!1});let s=o.get(u.tab_id);s.downloadables.push(u),u.is_low_quality||(s.filter_out_low_quality=!0)}for(let u of[...e.downloading.values(),...e.downloaded.values()]){let s=o.get(u.downloadable.tab_id);s&&!u.downloadable.is_low_quality&&(s.filter_out_low_quality=!0)}for(let[u,s]of o.entries()){let d=u==e.current_tab_id,c=d||u=="none"||t.all_tabs;for(let w of s.downloadables){let A={order:0,is_current_tab:d,id:w.id,timestamp:w.timestamp,reach:"downloadable",is_visible:c};s.filter_out_low_quality&&w.is_low_quality&&!t.low_quality&&(A.is_visible=!1),n.push([w,A])}}}let r=[...e.downloading.values()].map(u=>[u,{order:0,id:u.downloadable.id,is_current_tab:u.downloadable.tab_id==e.current_tab_id,timestamp:u.downloadable.timestamp,reach:"downloading",is_visible:!0}]),a=[...e.downloaded.values()].map(u=>[u,{order:0,id:u.downloadable.id,is_current_tab:u.downloadable.tab_id==e.current_tab_id,timestamp:u.downloadable.timestamp,reach:"downloaded",is_visible:!t.hide_downloaded}]),i;{let u=([,s],[,d])=>d.timestamp-s.timestamp;t.sort_by_status?(n.sort(u),r.sort(u),a.sort(u),i=[...n,...r,...a]):(i=[...n,...r,...a],i.sort(u)),t.sort_reverse&&i.reverse()}let l=0;for(let u of i)u[1].is_visible&&(u[1].order=l++);return i}function de(e){return e<1048576?`${(e/1024).toFixed(0)}Kb`:`${(e/1048576).toFixed(0)}Mb`}function ye(e){let t=Math.floor(e/3600);e-=t*3600;let o=Math.floor(e/60);e-=o*60;let n=Math.round(e),r=("0"+t+":").slice(-3),a=("0"+o+":").slice(-3),i=("0"+n).slice(-2);return r=="00:"&&(r=""),r+a+i}var ze=["download","copy","download_as","download_audio"];function be(e,t){let o=document.querySelector(`[data-downloadable-id="${e}"]`),n=o.querySelector("sl-progress-bar"),r=o.querySelector(".span-percent"),a=o.querySelector(".span-bitrate"),i=o.querySelector(".span-downloading-duration");o.classList.remove("progress-unknown"),typeof t!="string"?(i.textContent=ye(t.duration_since_start/1e3)+"s",o.querySelector(".button-stop").removeAttribute("loading"),o.querySelector(".button-stop").removeAttribute("disabled"),a.removeAttribute("hidden"),a.querySelector("sl-format-bytes").setAttribute("value",t.bitrate_bs.toString()),t.progress!="unknown"?(r.removeAttribute("hidden"),r.querySelector("sl-format-number").setAttribute("value",t.progress.toString()),n.removeAttribute("indeterminate"),n.setAttribute("value",(t.progress*100).toString())):(r.setAttribute("hidden","true"),n.removeAttribute("value"),n.setAttribute("indeterminate","true"),o.classList.add("progress-unknown"))):(o.querySelector(".button-stop").setAttribute("loading","true"),o.querySelector(".button-stop").setAttribute("disabled","true"),a.setAttribute("hidden","true"))}function bt(e,t){let o=e.core_media,n=[],r=o.container.extension;o.av.video||(r=o.container.audio_only_extension),n.push(`<span class="variant-container">${r}</span>`);let a=Q("v9_panel_downloadable_variant_no_details",[],t);if(o.av.video){if(o.av.video.quality.isSome()){let i=o.av.video.quality.unwrap()+"p";n.push(`<span class="variant-quality _${i}">${i}</span>`)}if(o.av.video.dimensions.isSome()){let i=o.av.video.dimensions.unwrap();a=`${i.width}x${i.height}`,o.av.video.bitrate.isSome()&&(a+=` ${de(o.av.video.bitrate.unwrap())}/s`)}else o.content_length.isSome()?a=de(o.content_length.unwrap()):o.av.video.bitrate.isSome()&&(a+=`${de(o.av.video.bitrate.unwrap())}/s`)}return n.push(`<span class="variant-differentiator">${a}</span>`),`<sl-menu-item class="menu-item-variant" data-variant-id="${e.id}" data-to-copy="${F(e.to_copy)}">
    ${n.join("")}
  </sl-menu-item>`}function Fe(e,t){e.dataset.defaultAction=t;let o=e.querySelector("sl-menu-item.action-"+t),n=o.querySelector("sl-icon").getAttribute("name"),r=o.querySelector("span").textContent,a=e.querySelector(".button-group-actions > sl-button");for(let i of ze)a.classList.remove("action-"+i);a.classList.add("action-"+t),a.querySelector("sl-icon").setAttribute("name",n),a.querySelector("span").textContent=r}function Be(e){let t=e.core_media.container.extension;e.core_media.av.video||(t=e.core_media.container.audio_only_extension);let o=`<span class="variant-container">${t}</span>`,n=e.core_media.av.video,r=o;if(n&&n.quality.isSome()){let i=n.quality.unwrap()+"p",l=`<span class="variant-quality _${i}">${i}</span>`;r+=l}else if(e.core_media.content_length.isSome()){let a=e.core_media.content_length.unwrap();r+=`<span class="variant-quality">${de(a)}</span>`}return r}function he(e,t){let o=[...e.variants.values()][0],n=document.createElement("hbox");n.classList.add("download"),n.dataset.selectedVariantId=o.id,n.dataset.downloadableId=e.id,n.dataset.timestamp=e.timestamp.toString(),n.dataset.toCopy=o.to_copy;let r;{let u=o.core_media.duration,s="";typeof u=="number"&&(s=`<div class="duration">${ye(u)}</div>`),r=`<hbox class="download-left" align="start" style="background-image:url('${F(e.thumbnail_url)}')">
      <div class="favicon" style="background-image:url('${F(e.favicon_url)}')"></div>
      ${s}
    </hbox>`}let a;{let u=`
      <span class="span-bitrate"><sl-format-bytes></sl-format-bytes>/s</span>
      <span class="span-downloading-duration"></span>
      <span size="small" class="span-percent"><sl-format-number type="percent"></sl-format-number></span>`,s='<sl-icon name="film"></sl-icon>';o.core_media.av.video?s='<sl-icon name="film"></sl-icon><sl-icon name="plus"></sl-icon><sl-icon name="music-note-beamed"></sl-icon>':s='<sl-icon name="music-note-beamed"></sl-icon>';let d=`
      <hbox class="hbox-tags">
        <sl-tag variant="primary" size="small">${o.core_media.builder}</sl-tag>
        <sl-tooltip content="Video & Audio">
          <sl-tag variant="neutral" size="small">${s}</sl-tag>
        </sl-tooltip>
      </hbox>`,c=F(e.page_title);a=`
    <hbox class="download-top" align="center">
      ${d}<div class="favicon" style="background-image:url('${F(e.favicon_url)}')"></div>
      <p class="title" flex="1" title="${c}">${c}</p>
      ${u}
      <sl-icon-button name="x" class="button-hide"></sl-icon-button>
    </hbox>`}let i;{let u="";for(let w of e.variants.values())u+=bt(w,t);i=`
      <hbox align="center" pack="end" class="download-bottom" flex="1">
        ${`
        <sl-dropdown stay-open-on-select="true" class="dropdown-variants">
          <sl-button slot="trigger" size="small" caret>
            ${Be(o)}
          </sl-button>
          <sl-menu>
            ${u}
            <sl-divider></sl-divider>
            <sl-menu-item type="checkbox" class="menu-prefer-quality"><span data-i18n="v9_panel_variant_menu_prefer_quality"></span></sl-menu-item>
            <sl-menu-item type="checkbox" class="menu-prefer-container"><span data-i18n="v9_panel_variant_menu_prefer_format"></span></sl-menu-item>
          </sl-menu>
        </sl-dropdown>`}
        <sl-progress-bar flex="1"></sl-progress-bar>
        <spacer flex="1"></spacer>
        <hbox class="progress-unknown-component"><sl-icon name="broadcast"></sl-icon></hbox>
        <sl-button size="small" variant="danger" loading class="button-stop" data-i18n="v9_panel_downloading_stop" pill></sl-button>
        
      <sl-tooltip data-i18n-attr="content" data-i18n="v9_panel_downloaded_retry_tooltip" class="button-downloaded-action">
        <sl-icon-button name="arrow-clockwise" class="button-retry"></sl-icon-button>
      </sl-tooltip>

      <sl-tooltip data-i18n-attr="content" data-i18n="v9_panel_downloaded_delete_file_tooltip" class="button-downloaded-action">
        <sl-icon-button variant="danger" name="trash" class="button-rm"></sl-icon-button>
      </sl-tooltip>

      <!-- FIXME: there's a duplicate of that code in history.html -->
      <sl-button-group class="button-downloaded-action" pill>
        <sl-button variant="success" class="button-play" size="small" pill>
          <sl-icon slot="prefix" name="play-circle-fill"></sl-icon>Play
        </sl-button>
        <sl-tooltip data-i18n-attr="content" data-i18n="v9_panel_downloaded_show_dir_tooltip">
          <sl-button variant="success" class="button-dir" size="small" pill>
            <sl-icon slot="suffix" name="folder-fill"></sl-icon>
          </sl-button>
        </sl-tooltip>
      </sl-button-group>
    
        <!-- button-group-actions from template -->
        <!-- menu-actions from template -->
      </hbox>`}n.innerHTML=`
  ${r}
  <vbox class="download-right" flex="1">
    ${a}
    ${i}
  </vbox>
  `;let l=document.querySelector("template").content;{let u=l.querySelector(".button-group-actions").cloneNode(!0),s=l.querySelector(".menu-actions").cloneNode(!0),d=n.querySelector(".download-bottom");d.appendChild(u),d.appendChild(s)}return ue(n,t),n}function Je(e,t){let o=document.querySelector(`[data-downloadable-id="${e.downloadable.id}"]`);return o||(o=he(e.downloadable,t),document.querySelector("#core-downloads").append(o)),o.setAttribute("status","downloading"),be(e.downloadable.id,e.progress),o}function We(e,t){let o=document.querySelector(`[data-downloadable-id="${e.id}"]`);o||(o=he(e,t),document.querySelector("#core-downloads").append(o)),o.setAttribute("status","downloadable");{let n=o.dataset.selectedVariantId,r=e.variants.get(n),a=Be(r),i=o.querySelector(".dropdown-variants > sl-button");i.innerHTML=a}return o}function Qe(e,t){let o=document.querySelector(`[data-downloadable-id="${e.downloadable.id}"]`);o||(o=he(e.downloadable,t),document.querySelector("#core-downloads").append(o)),o.setAttribute("status","downloaded");let n=o.querySelector(".title");return e.download_result.inbrowser?(o.dataset.inBrowserDownloadId=e.download_result.download_id.toString(),n.textContent=e.downloadable.title):n.textContent=e.download_result.filename,o}var pe=ie(Y(),1);var X=/.^/,Ae={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:X,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:X,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:X,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:X,defacto_container:"Mp4"}},Ge={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:X,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:X,defacto_container:"Mp4"}},Ke=C(function*(){for(let e of Object.keys(Ae))yield Ae[e]}),Ye=C(function*(){for(let e of Object.keys(Ge))yield Ge[e]});function ve(e){return typeof e=="string"&&e in Ae?v(e):f}var xe={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:Ke().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:Ye().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:f,video:f},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:v("MP3"),video:v("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:v("AAC"),video:f},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:v("FLAC"),video:f},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:v("MP3"),video:v("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:f,video:f},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:v("Wav"),video:f},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:f,video:f},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},ht=C(function*(){for(let e of Object.keys(xe))yield e}),Po=C(function*(){for(let e of ht())yield xe[e]});function Se(e){return typeof e=="string"&&e in xe?v(e):f}var Ze={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};var Xe=C(function*(){for(let e of Object.keys(Ze))yield e}),Ro=C(function*(){for(let e of Xe())yield Ze[e]});function ce(e){if(typeof e=="string")return Xe().find(t=>t==e);if(typeof e=="number"){let t=e.toString();return ce(t)}return f}function Me(){return{prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"4320",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:[]}}function et(e){return N(e).unwrap()}function tt(e){let t=q(e).unwrapOr({}),o=Me(),n=Se(t.container).unwrapOr(o.container),r=ve(t.video_codec).unwrapOr(o.video_codec),a=ce(t.best_video_quality).unwrapOr(o.best_video_quality),i=ce(t.lowest_video_quality).unwrapOr(o.lowest_video_quality),l;if("prefered_video_quality"in t){let T=ce(t.prefered_video_quality);T.isSome()&&(l=T.unwrap())}let u=o.max_variants;if(typeof t.max_variants=="number"){let T=t.max_variants;Number.isInteger(T)&&T<=11&&T>0&&(u=T)}let s=o.prefer_60fps;typeof t.prefer_60fps=="boolean"&&(s=t.prefer_60fps);let d=o.ignore_low_quality_hits;typeof t.ignore_low_quality_hits=="boolean"&&(d=t.ignore_low_quality_hits);let c=[];if(Array.isArray(t.ignored_containers))for(let T of t.ignored_containers){let M=Se(T);M.isSome()&&c.push(M.unwrap())}let w=[];if(Array.isArray(t.ignored_video_codecs))for(let T of t.ignored_video_codecs){let M=ve(T);M.isSome()&&w.push(M.unwrap())}let A={prefer_60fps:s,ignore_low_quality_hits:d,container:n,max_variants:u,video_codec:r,lowest_video_quality:i,best_video_quality:a,ignored_containers:c,ignored_video_codecs:w};return typeof l<"u"&&(A.prefered_video_quality=l),A}var ot={all_tabs:!1,low_quality:!1,sort_by_status:!0,sort_reverse:!1,show_button_clean:!0,show_button_clean_all:!1,show_button_convert_local:!1,hide_downloaded:!1};var At=ie(Y(),1);function Te(e,t){if(e==null||t===null||t===void 0)return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Function||e instanceof RegExp)return e===t;if(e===t||e.valueOf()===t.valueOf())return!0;if(Array.isArray(e)&&e.length!==t.length||e instanceof Date||!(e instanceof Object)||!(t instanceof Object))return!1;let o=Object.keys(e),n=Object.keys(t).every(a=>o.indexOf(a)!==-1),r=o.every(a=>Te(e[a],t[a]));return n&&r}async function O(e,t){let o=t;e.hooks&&(o=e.hooks.setter(t)),await pe.storage[e.where].set({[e.name]:o})}async function b(e){let t=await pe.storage[e.where].get(e.name);if(e.name in t){let o=t[e.name];return e.hooks?e.hooks.getter(o,e):o}return e.default()}function ee(e,t){pe.storage[e.where].onChanged.addListener(o=>{let n=o[e.name];if(n){if(Te(n.oldValue,n.newValue))return;typeof n.newValue>"u"?t(e.default()):e.hooks?t(e.hooks.getter(n.newValue,e)):t(n.newValue)}})}var rt={name:"debugger_enabled",default:()=>!1,where:"local"};var ke={name:"used_history_button",default:()=>!1,where:"local"},Ee={name:"use_sidebar",default:()=>!1,where:"local"};var L={name:"view_options",default:()=>structuredClone(ot),where:"local"};var j={name:"default_action",default:()=>"download",where:"local"},nt={name:"yt_warning",default:()=>!0,where:"local"},at={name:"use_wide_ui",default:()=>!1,where:"local"},it={name:"use_legacy_ui",default:()=>!1,where:"local"};var De={name:"open_count_store",default:()=>0,where:"session"};var G={name:"view_options",default:()=>({}),where:"session"};var Ce={name:"blacklist",default:()=>[],where:"local",hooks:{setter:e=>e.filter(t=>t.length>0),getter:e=>e}};var Oe={name:"media_user_pref",where:"local",default:()=>Me(),hooks:{setter:e=>et(e),getter:e=>tt(e)}};var U={name:"database",where:"session",default:()=>({yt_bulk:f,user_messages:new Set,coapp_status:"checking",license_status:{checking:!0},current_tab_id:0,current_window_id:0,downloadable:new Map,downloading:new Map,downloaded:new Map,download_errors:new Map}),hooks:{setter:e=>N(e).unwrap(),getter:(e,t)=>q(e).unwrapOr(t.default())}};var Ie=[],vt=await b(rt),Ve;function R(e){vt&&(Ie.push({timestamp:Date.now(),message:e}),Ve||(Ve=setTimeout(()=>{Ve=void 0,x({debugger_new_logs:Ie}),Ie.length=0},500)))}async function x(e){typeof e!="string"&&!("debugger_new_logs"in e)&&R(`PostMessageToBackground - ${JSON.stringify(e)}`),S.default.runtime.sendMessage(e)}S.default.runtime.onMessage.addListener(async e=>{let t=e;if("progress_changed"in t){let{downloadable_id:o,progress:n}=t.progress_changed;be(o,n)}});if(await b(it))throw new Error("V9 panel openning while using legacy UI");var Pe;function xt(e,t){let o=document.querySelector("#core"),n=Array.from(o.querySelectorAll(".download-error"));for(let i of n){let l=i.dataset.errorId;e.download_errors.has(l)||i.remove()}let r=document.querySelector("template").content;for(let i of e.download_errors.values()){let l=document.querySelector(`.download-error[data-error-id="${i.id}"]`);if(!l){let s=r.querySelector(`.download-error[error-type="${i.error}"]`);if(!s){R("Didn't find template for error: "+i.error);continue}l=s.cloneNode(!0),ue(l,t),l.dataset.errorId=i.id,o.insertBefore(l,o.firstChild)}l.setAttribute("report-status",i.report_status);let u=l.querySelector(".button-report-error");u&&(i.report_status=="reporting"?(u.setAttribute("loading","true"),u.setAttribute("disabled","true")):(u.removeAttribute("loading"),u.removeAttribute("disabled")))}let a=document.querySelector('.download-error[data-error-id="nocoapp"]');if(a){let i=a.querySelector(".error-nocoapp-button-recheck"),l=a.querySelector(".error-nocoapp-button-install"),u=a.querySelector(".error-nocoapp-button-success");e.coapp_status=="checking"?(u.setAttribute("hidden","true"),l.removeAttribute("hidden"),l.disabled=!0,i.disabled=!0,i.setAttribute("loading","true")):e.coapp_status.found?(u.removeAttribute("hidden"),l.setAttribute("hidden","true"),l.disabled=!0,i.setAttribute("hidden","true"),i.removeAttribute("loading")):(u.setAttribute("hidden","true"),l.removeAttribute("hidden"),l.disabled=!1,i.disabled=!1,i.removeAttribute("loading"))}}function St(e,t){let o=document.querySelector("#core"),n=Array.from(o.querySelectorAll(".user-message"));for(let a of n)e.user_messages.has(a.dataset.userMessageId)||a.remove();let r=document.querySelector("template").content;for(let a of e.user_messages.values()){let i=document.querySelector(`.user-message[data-user-message-id="${a}"]`);if(!i){let l=r.querySelector(`.user-message[data-user-message-id="${a}"]`);if(!l){R("Didn't find template for user message: "+a);continue}i=l.cloneNode(!0),ue(i,t),i.dataset.userMessageId=a,o.insertBefore(i,o.firstChild)}if(a=="yt_bulk_detected"&&e.yt_bulk.isSome()){let l=e.yt_bulk.unwrap();i.querySelector(".title").textContent=Q("v9_yt_bulk_detected",[l.ids.length],t)}}}function oe(e,t,o,n){let r=Ue(e,t),a=r.reduce((s,[,d])=>s+(d.is_visible?1:0),0),i=r.some(([,s])=>s.is_visible&&s.is_current_tab);e.download_errors.size+e.user_messages.size+a+(i?0:1)>1&&document.documentElement.classList.add("expanded");let u=document.querySelector("#nomedia");i?u.setAttribute("hidden","true"):u.removeAttribute("hidden"),document.querySelector("#menu-view-all-tabs").checked=t.all_tabs,document.querySelector("#menu-view-hide-downloaded").checked=t.hide_downloaded,document.querySelector("#menu-view-show-low-quality").checked=t.low_quality,document.querySelector("#menu-view-sort-status").checked=t.sort_by_status,document.querySelector("#menu-view-sort-reverse").checked=t.sort_reverse,document.querySelector("#button-clean-all").setAttribute("hidden",(!t.show_button_clean_all).toString()),document.querySelector("#button-clean").setAttribute("hidden",(!t.show_button_clean).toString()),document.querySelector("#button-convert-local").setAttribute("hidden",(!t.show_button_convert_local).toString()),document.querySelector("#menu-view-clean-all").checked=t.show_button_clean_all,document.querySelector("#menu-view-clean").checked=t.show_button_clean,Pe=e.current_window_id,xt(e,n),St(e,n);for(let s of Array.from(document.querySelectorAll(".download"))){let c=s.dataset.downloadableId;!e.downloadable.has(c)&&!e.downloading.has(c)&&!e.downloaded.has(c)&&s.remove()}for(let[s,d]of r)if(d.is_visible){let c;d.reach==="downloadable"?c=We(s,n):d.reach==="downloading"?c=Je(s,n):c=Qe(s,n),Fe(c,o),c.setAttribute("style",`--order: ${d.order.toString()}`)}else{let c;"downloadable"in s?c=s.downloadable.id:c=s.id;let w=document.querySelector(`#core-downloads > [data-downloadable-id="${c}"]`);w&&w.remove()}}{let t=function(n){let r=n.target;if(!r)return;R(`on_change - #${r.id}.${r.className}`);let a=r.closest('.download-error[error-type="qrcode"] sl-checkbox');if(a){e++;let i=a.checked;O(nt,!i)}},o=function(n){let r=n.target;if(!r)return;let a=r.closest(".download"),i=a?.dataset.downloadableId;if(r.closest(".error-nocoapp-button-install"))S.default.tabs.create({url:"https://www.downloadhelper.net/install-coapp-v2"});else if(r.closest(".button-report-error")){let l=r.closest(".download-error");x({report_error:l.dataset.errorId})}else if(r.closest(".button-trigger-bulk-download"))x("bulk_download");else if(r.closest(".button-leave-review"))x("leave_review");else if(r.closest(".button-never-show-one-hundred-message"))x("never_ask_for_review");else if(r.closest(".user-message-hide-downloaded"))x({rm_user_message:"auto_hide_downloaded"}),b(L).then(l=>{l.hide_downloaded=!0,O(L,l)});else if(r.closest(".error-nocoapp-button-recheck"))x({coapp_check:!0});else if(r.closest(".error-nocoapp-button-success"))x({rm_error:"nocoapp"});else if(r.closest(".error-why-qr"))S.default.tabs.create({url:"https://www.downloadhelper.net/about-licensing"});else if(r.closest(".error-invalid-license-get"))S.default.tabs.create({url:"https://www.downloadhelper.net/convert"});else if(r.closest(".download-error .button-hide")||r.closest(".button-error-reported")){let u=r.closest(".download-error").dataset.errorId;x({rm_error:u})}else if(r.closest(".user-message .button-hide")){let l=r.closest(".user-message");x({rm_user_message:l.dataset.userMessageId})}else if(r.closest("#button-convert-local"))document.documentElement.classList.add("expanded"),document.querySelector("#drawer-convert-to").show();else if(r.closest("#drawer-menu-convert-to")){document.querySelector("#drawer-convert-to").hide();let l=r.value;x({convert_local_to:l})}else if(r.closest(".menu-item-variant")){let l=r.closest(".menu-item-variant"),u=l.dataset.variantId,s=l.dataset.toCopy,d=a.querySelector(".dropdown-variants"),c=a.querySelector(".menu-prefer-quality"),w=a.querySelector(".menu-prefer-container");a.dataset.selectedVariantId=u,a.dataset.toCopy=s;let A=c.checked,T=w.checked;c.checked=!1,w.checked=!1,d.hide(),Promise.resolve().then(async()=>{let M=await b(U),me=M.downloadable.get(i).variants.get(u),re=await b(Oe),ne=await b(L),m=await b(G),p=me.core_media;A&&p.av.video&&p.av.video.quality.isSome()&&(re.prefered_video_quality=p.av.video.quality.unwrap()),T&&(re.container=p.container.name),await O(Oe,re);let _=await b(j);oe(M,ne,_,m)})}else if(r.closest(".dropdown-actions"))Mt(a);else if(r.closest("#button-force")){let l=async()=>{let s=await S.default.tabs.query({currentWindow:!0,active:!0});if(s[0]&&s[0].url){let d=s[0].url,c=new URL(d);if(Z=="mozilla"){let w=c.hostname,A=w.split(".").slice(-2).join(".");await S.default.browsingData.removeCookies({hostnames:[w,A]})}else chrome.browsingData.removeCookies({origins:[c.origin]});S.default.tabs.reload(s[0].id,{bypassCache:!0})}},u=async()=>{let d={permissions:["browsingData"]};await S.default.permissions.request(d)&&l()};S.default.browsingData?.removeCookies?l().catch(s=>{u()}):u()}else if(r.closest(".menu-smartnaming"))te(),S.default.tabs.create({url:`/content2/smartnaming_editor.html?id=${i}`});else if(r.closest("#button-open-privacy"))S.default.tabs.create({url:"https://www.downloadhelper.net/privacy"});else if(r.closest("#button-accept-privacy"))x({privacy_accept:!0});else if(r.closest("#button-decline-privacy"))x({privacy_accept:!1});else if(r.closest("#button-clean-all"))x({clean:!0});else if(r.closest("#button-clean"))x({clean:!1});else if(r.closest("#button-use-sidebar"))O(Ee,!0),we(!0,Pe,!0),window.close();else if(r.closest("#button-use-popup"))O(Ee,!1),we(!1,Pe,!0);else if(r.closest(".button-retry"))x({retry:i});else if(r.closest(".menu-details"))te(),n.shiftKey?b(U).then(l=>{let u=l.downloadable.get(i),s=H(u),d=JSON.stringify(s,null,4),c=new Blob([d],{type:"text/json;charset=utf-8"}),w=URL.createObjectURL(c);S.default.tabs.create({url:w})}):S.default.tabs.create({url:`/content2/details.html?id=${i}`});else if(r.closest(".button-dir"))x({show_dir:i});else if(r.closest(".button-play"))if(a.dataset.inBrowserDownloadId){let l=a.dataset.inBrowserDownloadId,u=()=>{let d={permissions:["downloads.open"]};S.default.permissions.request(d)};S.default.downloads.open?S.default.downloads.open(parseInt(l)).catch(s=>{u()}):u()}else x({play:i});else if(r.closest(".button-rm"))x({rm:i});else if(r.closest(".button-stop"))x({stop:i});else if(r.closest(".download .button-hide"))x({forget:i});else if(r.closest(".menu-blacklist-edit"))te(),S.default.tabs.create({url:"/content2/blacklist.html"});else if(r.closest(".button-open-browser-settings"))Z=="mozilla"?S.default.tabs.create({url:"https://github.com/aclap-dev/video-downloadhelper/wiki/Enable-Incognito"}):S.default.tabs.create({url:`chrome://extensions/?id=${S.default.runtime.id}`});else if(r.closest("#dropdown-menu-view")){let l=r.closest("#dropdown-menu-view");if(r.closest("#menu-view-open-settings"))l.hide(),x({license_check:null}),x({coapp_check:!1}),document.querySelector(".drawer-placement-bottom").show();else{let u=r.closest("#menu-view-all-tabs"),s=r.closest("#menu-view-hide-downloaded"),d=r.closest("#menu-view-show-low-quality"),c=r.closest("#menu-view-sort-status"),w=r.closest("#menu-view-sort-reverse"),A=r.closest("#menu-view-clean-all"),T=r.closest("#menu-view-clean");b(L).then(M=>{u&&(M.all_tabs=u.checked),s&&(M.hide_downloaded=s.checked),d&&(M.low_quality=d.checked),c&&(M.sort_by_status=c.checked),w&&(M.sort_reverse=w.checked),A&&(M.show_button_clean_all=A.checked),T&&(M.show_button_clean=T.checked),O(L,M)})}}else if(r.closest(".menu-blacklist")){let l=r.closest(".menu-blacklist-media"),u=r.closest(".menu-blacklist-domain"),s=r.closest(".menu-blacklist-page");(s||l||u)&&(te(),b(Ce).then(async d=>{let w=(await b(U)).downloadable.get(i),A;s?A=w.page_url:l?A=w.variants.values().next().value.manifest_url:u&&(A=new URL(w.page_url).origin+"/*"),d.push(A),O(Ce,d)}))}else if(r.closest("#button-show-history"))O(ke,!0),S.default.tabs.create({url:"/content2/history.html"});else{let l=r.closest(".action-download"),u=r.closest(".action-download_as"),s=r.closest(".action-download_audio"),d=r.closest(".action-copy");r.closest(".menu-actions")&&a?.querySelector(".checkbox-remember-action")?.checked&&(l?O(j,"download"):u?O(j,"download_as"):s?O(j,"download_audio"):d&&O(j,"copy"));let c=r.closest(".menu-convert-to");if(l??s??u??c){R("on_event - download_buttons is true");let A=a.dataset.selectedVariantId,T;c&&(T=c.value);let M={download:{downloadable_id:i,variant_id:A,audio_only:!!s,ask_for_destination:!!u,convert_to:T}};R(`<do> download args: ${JSON.stringify(M)}`),x(M),te(),R("</done>")}if(d){let A=a.dataset.toCopy;navigator.clipboard.writeText(A),te()}}};Tt=t,kt=o;let e=0;setInterval(()=>{e>1&&R("event counter > 1"),e=0},500),window.addEventListener("click",o),window.addEventListener("sl-change",t)}var Tt,kt;{let e=await b(De);await O(De,++e),e>8&&x("incognito_check")}{async function e(i,l,u){let s=d=>{document.querySelector(l).classList.toggle(u,d)};s(await b(i)),ee(i,s)}e(ke,"#footer","used-history-button"),e(at,"html","wide");let t=document.documentElement.id=="sidebar";document.documentElement.setAttribute("target",Z),document.documentElement.setAttribute("os",(await S.default.runtime.getPlatformInfo()).os),document.documentElement.classList.toggle("sidebar",t),ee(L,async i=>{let l=await b(G),u=await b(U),s=await b(j);oe(u,i,s,l)}),ee(G,async i=>{let l=await b(L),u=await b(U),s=await b(j);oe(u,l,s,i)}),ee(U,async i=>{R("DB changed");let l=await b(G),u=await b(L),s=await b(j);oe(i,u,s,l)}),ee(j,async i=>{let l=await b(U),u=await b(G),s=await b(L);oe(l,s,i,u)});let o=await b(L),n=await b(G),r=await b(U),a=await b(j);oe(r,o,a,n),document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{document.body.classList.remove("not-loaded")}):document.body.classList.remove("not-loaded"),document.addEventListener("sl-show",i=>{let l=i.target;l&&(l.tagName=="SL-DROPDOWN"||l.tagName=="SL-DRAWER")&&(document.documentElement.classList.add("expanded"),l.tagName=="SL-DROPDOWN"&&l.shadowRoot.querySelector("sl-popup").removeAttribute("auto-size"))},!0)}function te(){document.body.click()}function Mt(e){let t=e.querySelector(".menu-actions");if(!t.classList.contains("closed")){R("Dropdown already open");return}document.documentElement.classList.add("expanded"),t.classList.remove("closed"),t.removeAttribute("hidden");let o=()=>{t.classList.add("closed"),setTimeout(()=>{t.classList.contains("closed")&&t.setAttribute("hidden","true")},200),window.removeEventListener("click",r,!0),window.removeEventListener("keydown",n,!0)},n=a=>{a.key==="Escape"&&o()},r=a=>{let i=a.target;i&&(i==t||t.contains(i)||(o(),a.stopPropagation()))};window.addEventListener("click",r,!0),window.addEventListener("keydown",n,!0)}
