"use strict";(()=>{weh.is_safe.then(()=>{class n extends React.Component{constructor(e){super(e),this.state={}}rpcCall(...e){return()=>{weh.rpc.call(...e).then(()=>{close()})}}render(){var e=null,t=null,a=browser.runtime.getManifest();return/a/.test(a.version_name||a.version)?(e=weh._("about_alpha_intro"),/^7\.0.*a/.test(a.version_name||a.version)&&weh.isBrowser("firefox")&&(t=weh._("about_alpha_extra7_fx"))):/b/.test(a.version_name||a.version)&&(e=weh._("about_alpha_intro")),React.createElement("div",{className:"about-vdh"},e&&React.createElement("p",null,e),t&&React.createElement("p",null,t),React.createElement("div",{className:"about-links"},React.createElement("a",{href:"#",onClick:this.rpcCall("openForum")},weh._("support_forum")),React.createElement("a",{href:"#",onClick:this.rpcCall("openHomepage")},weh._("homepage"))),React.createElement(CopyButton,null),React.createElement(AddonInfoPanel,null),React.createElement(PlatformInfoPanel,null),React.createElement(CoAppInfoPanel,null),React.createElement(LicInfoPanel,null))}}render(React.createElement("div",null,React.createElement("div",null,React.createElement(WehHeader,{title:weh._("about_vdh")}),React.createElement("main",null,React.createElement(n,null)))),document.getElementById("root")),weh.setPageTitle(weh._("about_vdh"))});})();
