<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">

    <title data-i18n="v9_history_page_title"></title>

    <link rel="stylesheet" href="xul.css"/>
    <link rel="stylesheet" href="base.css"/>
    <link rel="stylesheet" href="panel.css"/>
    <link rel="shortcut icon" href="/content2/icons/stable-color.png"/>

    <style>

      :root {
        background-color: var(--sl-color-neutral-100);
      }

      body {
        margin: var(--sl-spacing-medium) auto;
        max-width: 600px;
      }

      body.recording #no-recording,
      body:not(.recording) #noentries,
      body:not(.recording) #recording {
        display: none;
      }

      #recording {
        gap: var(--sl-spacing-x-small);
      }

      .filename {
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-transform: uppercase;
        font-style: italic;
        background-size: 15px 15px;
        background-repeat: no-repeat;
        padding-left: 20px;
        min-height: 15px;
      }

      .page-url {
        padding-left: 20px;
      }

      .history-entry, #noentries {
        border: 1px solid var(--sl-color-neutral-200);
        box-shadow: var(--sl-shadow-small);
        border-radius: var(--sl-border-radius-large);
        background-color: var(--sl-color-neutral-0);
        padding: var(--sl-spacing-x-small);
        margin-top: var(--sl-spacing-medium);
      }

      #noentries {
        text-align: center;
        font-style: italic;
        color: var(--sl-color-neutral-500);
        font-weight: lighter;
        font-size: 0.8rem;
      }

      .date {
        color: var(--sl-color-neutral-500);
        margin-left: var(--sl-spacing-x-small);
      }

      .top-box {
        margin-bottom: var(--sl-spacing-x-small);
      }

    </style>

  </head>
  <body>

    <h1 data-i18n="v9_history_page_title"></h1>

    <vbox id="no-recording">
      <p data-i18n="v9_history_no_recording_description"></p>
      <p><span data-i18n="v9_history_no_recording_description_safe"></span>🤟</p>
      <sl-button id="button-start-recording" data-i18n="v9_history_button_start_recording" variant="success"></sl-button>
    </vbox>

    <hbox id="recording">
      <sl-input class="input-inline" id="search" data-i18n="v9_history_input_search" data-i18n-attr="placeholder" type="text" clearable></sl-input>
      <sl-button id="button-clear-history" data-i18n="v9_history_button_clear" variant="danger"></sl-button>
      <sl-button id="button-stop-recording" data-i18n="v9_history_button_stop_recording" variant="primary"></sl-button>
    </hbox>

    <vbox id="main">
    </vbox>

    <vbox id="noentries" pack="center" align="center">
      <p data-i18n="v9_history_no_entries"></p>
    </vbox>

    <template>
      <vbox class="history-entry">
        <hbox align="center" class="top-box">
          <p class="filename" flex="1"></p>
          <sl-icon-button name="x" class="button-hide"></sl-icon-button>
        </hbox>
        <hbox align="center">
          <a class="page-url" target="_blank"></a>
          <em class="date"></em>
          <spacer flex="1"></spacer>

          <!-- FIXME: there's a duplicate of that code in panel_components.mts -->

          <sl-tooltip data-i18n-attr="content" data-i18n="v9_panel_downloaded_delete_file_tooltip" class="button-downloaded-action">
            <sl-icon-button variant="danger" name="trash" class="button-rm"></sl-icon-button>
          </sl-tooltip>

          <sl-button-group class="button-downloaded-action" pill>
            <sl-button variant="success" class="button-play" size="small" pill>
              <sl-icon slot="prefix" name="play-circle-fill"></sl-icon>Play
            </sl-button>
            <sl-tooltip data-i18n-attr="content" data-i18n="v9_panel_downloaded_show_dir_tooltip">
              <sl-button variant="success" class="button-dir" size="small" pill>
                <sl-icon slot="suffix" name="folder-fill"></sl-icon>
              </sl-button>
            </sl-tooltip>
          </sl-button-group>
        </hbox>
      </vbox>
    </template>

    <script type="module" src="shoelace.js"></script>
    <script type="module" src="history.js"></script>
  </body>
</html>
