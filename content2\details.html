<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=200, initial-scale=1.0">
    <style>

      body {
        padding: 20px;
      }

      table {
        border-collapse: collapse;
        font-family: sans-serif;
        letter-spacing: 1px;
        border: 3px solid black;
        text-wrap: nowrap;
      }

      tr.separator {
        border-top: 3px solid black;
      }

      th, td {
        min-width: 150px;
        border: 1px solid rgb(160 160 160);
        padding: 8px 10px;
        text-align: left;
      }

      td {
        font-family: monospace;
      }

      tbody > tr:nth-of-type(even) {
        background-color: rgb(237 238 242);
      }

    </style>
  </head>
  <body>
    <table><tbody></tbody></table>
    <script type="module" src="details.js"></script>
  </body>
</html>
