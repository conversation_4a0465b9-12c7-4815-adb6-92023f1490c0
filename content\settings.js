"use strict";(()=>{var ye=Object.create;var ee=Object.defineProperty;var Ae=Object.getOwnPropertyDescriptor;var we=Object.getOwnPropertyNames;var be=Object.getPrototypeOf,xe=Object.prototype.hasOwnProperty;var ve=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Se=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of we(t))!xe.call(e,a)&&a!==r&&ee(e,a,{get:()=>t[a],enumerable:!(n=Ae(t,a))||n.enumerable});return e};var te=(e,t,r)=>(r=e!=null?ye(be(e)):{},Se(t||!e||!e.__esModule?ee(r,"default",{value:e,enumerable:!0}):r,e));var Q=ve((q,re)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof q<"u")t(re);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:q,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=n=>{let a={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(a).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class o extends WeakMap{constructor(m,g=void 0){super(g),this.createItem=m}get(m){return this.has(m)||this.set(m,this.createItem(m)),super.get(m)}}let i=c=>c&&typeof c=="object"&&typeof c.then=="function",s=(c,m)=>(...g)=>{n.runtime.lastError?c.reject(new Error(n.runtime.lastError.message)):m.singleCallbackArg||g.length<=1&&m.singleCallbackArg!==!1?c.resolve(g[0]):c.resolve(g)},u=c=>c==1?"argument":"arguments",l=(c,m)=>function(_,...b){if(b.length<m.minArgs)throw new Error(`Expected at least ${m.minArgs} ${u(m.minArgs)} for ${c}(), got ${b.length}`);if(b.length>m.maxArgs)throw new Error(`Expected at most ${m.maxArgs} ${u(m.maxArgs)} for ${c}(), got ${b.length}`);return new Promise((T,C)=>{if(m.fallbackToNoCallback)try{_[c](...b,s({resolve:T,reject:C},m))}catch(p){console.warn(`${c} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,p),_[c](...b),m.fallbackToNoCallback=!1,m.noCallback=!0,T()}else m.noCallback?(_[c](...b),T()):_[c](...b,s({resolve:T,reject:C},m))})},h=(c,m,g)=>new Proxy(m,{apply(_,b,T){return g.call(b,c,...T)}}),A=Function.call.bind(Object.prototype.hasOwnProperty),N=(c,m={},g={})=>{let _=Object.create(null),b={has(C,p){return p in c||p in _},get(C,p,k){if(p in _)return _[p];if(!(p in c))return;let w=c[p];if(typeof w=="function")if(typeof m[p]=="function")w=h(c,c[p],m[p]);else if(A(g,p)){let V=l(p,g[p]);w=h(c,c[p],V)}else w=w.bind(c);else if(typeof w=="object"&&w!==null&&(A(m,p)||A(g,p)))w=N(w,m[p],g[p]);else if(A(g,"*"))w=N(w,m[p],g["*"]);else return Object.defineProperty(_,p,{configurable:!0,enumerable:!0,get(){return c[p]},set(V){c[p]=V}}),w;return _[p]=w,w},set(C,p,k,w){return p in _?_[p]=k:c[p]=k,!0},defineProperty(C,p,k){return Reflect.defineProperty(_,p,k)},deleteProperty(C,p){return Reflect.deleteProperty(_,p)}},T=Object.create(c);return new Proxy(T,b)},O=c=>({addListener(m,g,..._){m.addListener(c.get(g),..._)},hasListener(m,g){return m.hasListener(c.get(g))},removeListener(m,g){m.removeListener(c.get(g))}}),S=new o(c=>typeof c!="function"?c:function(g){let _=N(g,{},{getContent:{minArgs:0,maxArgs:0}});c(_)}),I=new o(c=>typeof c!="function"?c:function(g,_,b){let T=!1,C,p=new Promise(F=>{C=function(P){T=!0,F(P)}}),k;try{k=c(g,_,C)}catch(F){k=Promise.reject(F)}let w=k!==!0&&i(k);if(k!==!0&&!w&&!T)return!1;let V=F=>{F.then(P=>{b(P)},P=>{let J;P&&(P instanceof Error||typeof P.message=="string")?J=P.message:J="An unexpected error occurred",b({__mozWebExtensionPolyfillReject__:!0,message:J})}).catch(P=>{console.error("Failed to send onMessage rejected reply",P)})};return V(w?k:p),!0}),_e=({reject:c,resolve:m},g)=>{n.runtime.lastError?n.runtime.lastError.message===t?m():c(new Error(n.runtime.lastError.message)):g&&g.__mozWebExtensionPolyfillReject__?c(new Error(g.message)):m(g)},X=(c,m,g,..._)=>{if(_.length<m.minArgs)throw new Error(`Expected at least ${m.minArgs} ${u(m.minArgs)} for ${c}(), got ${_.length}`);if(_.length>m.maxArgs)throw new Error(`Expected at most ${m.maxArgs} ${u(m.maxArgs)} for ${c}(), got ${_.length}`);return new Promise((b,T)=>{let C=_e.bind(null,{resolve:b,reject:T});_.push(C),g.sendMessage(..._)})},he={devtools:{network:{onRequestFinished:O(S)}},runtime:{onMessage:O(I),onMessageExternal:O(I),sendMessage:X.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:X.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},H={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return a.privacy={network:{"*":H},services:{"*":H},websites:{"*":H}},N(n,he,a)};e.exports=r(chrome)}else e.exports=globalThis.browser})});var pe=te(Q(),1);function D(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var Te=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return x(t)},e.prototype.toString=function(){return"None"},e}(),d=new Te;Object.freeze(d);var Ce=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return y(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return f(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(D(this.value),")")},e.EMPTY=new e(void 0),e}(),y=Ce,j;(function(e){function t(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];for(var i=[],s=0,u=a;s<u.length;s++){var l=u[s];if(l.isSome())i.push(l.value);else return l}return y(i)}e.all=t;function r(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];for(var i=0,s=a;i<s.length;i++){var u=s[i];return u.isSome(),u}return d}e.any=r;function n(a){return a instanceof y||a===d}e.isOption=n})(j||(j={}));var ke=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(D(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(D(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new x(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return d},e.prototype.toString=function(){return"Err(".concat(D(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new $(this)},e.EMPTY=new e(void 0),e}();var x=ke,Ne=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(D(this.value)),{cause:this.value})},e.prototype.map=function(t){return new f(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return y(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(D(this.value),")")},e.prototype.toAsyncResult=function(){return new $(this)},e.EMPTY=new e(void 0),e}();var f=Ne,W;(function(e){function t(){for(var i=[],s=0;s<arguments.length;s++)i[s]=arguments[s];for(var u=[],l=0,h=i;l<h.length;l++){var A=h[l];if(A.isOk())u.push(A.value);else return A}return new f(u)}e.all=t;function r(){for(var i=[],s=0;s<arguments.length;s++)i[s]=arguments[s];for(var u=[],l=0,h=i;l<h.length;l++){var A=h[l];if(A.isOk())return A;u.push(A.error)}return new x(u)}e.any=r;function n(i){try{return new f(i())}catch(s){return new x(s)}}e.wrap=n;function a(i){try{return i().then(function(s){return new f(s)}).catch(function(s){return new x(s)})}catch(s){return Promise.resolve(new x(s))}}e.wrapAsync=a;function o(i){return i instanceof x||i instanceof f}e.isResult=o})(W||(W={}));var oe=function(e,t,r,n){function a(o){return o instanceof r?o:new r(function(i){i(o)})}return new(r||(r=Promise))(function(o,i){function s(h){try{l(n.next(h))}catch(A){i(A)}}function u(h){try{l(n.throw(h))}catch(A){i(A)}}function l(h){h.done?o(h.value):a(h.value).then(s,u)}l((n=n.apply(e,t||[])).next())})},ne=function(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,a,o,i;return i={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function s(l){return function(h){return u([l,h])}}function u(l){if(n)throw new TypeError("Generator is already executing.");for(;i&&(i=0,l[0]&&(r=0)),r;)try{if(n=1,a&&(o=l[0]&2?a.return:l[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,l[1])).done)return o;switch(a=0,o&&(l=[l[0]&2,o.value]),l[0]){case 0:case 1:o=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,a=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!o||l[1]>o[0]&&l[1]<o[3])){r.label=l[1];break}if(l[0]===6&&r.label<o[1]){r.label=o[1],o=l;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(l);break}o[2]&&r.ops.pop(),r.trys.pop();continue}l=t.call(e,r)}catch(h){l=[6,h],a=0}finally{n=o=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}},$=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(n){return oe(r,void 0,void 0,function(){var a;return ne(this,function(o){return n.isErr()?[2,n]:(a=t(n.value),[2,a instanceof e?a.promise:a])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(n){return oe(r,void 0,void 0,function(){var a;return ne(this,function(o){switch(o.label){case 0:return n.isErr()?[2,n]:(a=f,[4,t(n.value)]);case 1:return[2,a.apply(void 0,[o.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();var ae=(e,t)=>typeof e[t]=="string";function M(e){try{if(ae(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return f(e.__serializer_value);if(e.__serializer_tag==="regex"){let n=new RegExp(e.__serializer_value);return f(n)}else if(e.__serializer_tag==="array"){let n=[];for(let a of e.__serializer_value){let o=M(a);if(o.isErr())return o;n.push(o.unwrap())}return f(n)}else if(e.__serializer_tag==="map"){let n=[];for(let a of e.__serializer_value){let o=M(a);if(o.isErr())return o;n.push(o.unwrap())}return f(new Map(n))}else if(e.__serializer_tag==="set"){let n=[];for(let a of e.__serializer_value){let o=M(a);if(o.isErr())return o;n.push(o.unwrap())}return f(new Set(n))}else if(e.__serializer_tag==="result_ok"){let n=e.__serializer_value,a=M(n);return a.isErr()?a:f(f(a.unwrap()))}else if(e.__serializer_tag==="result_err"){let n=e.__serializer_value,a=M(n);return a.isErr()?a:f(x(a.unwrap()))}else if(e.__serializer_tag==="option_some"){let n=e.__serializer_value,a=M(n);return a.isErr()?a:f(y(a.unwrap()))}else if(e.__serializer_tag==="option_none")return f(d)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return x("This object was not serialized with Serialize");let r={};for(let n of Object.keys(e))if(typeof n=="string"){let a=M(e[n]);if(a.isErr())return a;r[n]=a.unwrap()}return f(r)}catch{return x("Failed to inspect object. Not JSON?")}}function E(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return f({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return f({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(o=>E(o)),n=r.as_iter().find(o=>o.isErr());if(n.isSome())return n.unwrap();let a=r.as_iter().map(o=>o.unwrap()).toArray();return f({__serializer_tag:"array",__serializer_value:a})}else if(e instanceof Map){let r=[...e.entries()].map(o=>E(o)),n=r.as_iter().find(o=>o.isErr());if(n.isSome())return n.unwrap();let a=r.as_iter().map(o=>o.unwrap()).toArray();return f({__serializer_tag:"map",__serializer_value:a})}else if(e instanceof Set){let r=[...e.values()].map(o=>E(o)),n=r.as_iter().find(o=>o.isErr());if(n.isSome())return n.unwrap();let a=r.as_iter().map(o=>o.unwrap()).toArray();return f({__serializer_tag:"set",__serializer_value:a})}else if(W.isResult(e))if(e.isOk()){let r=e.unwrap(),n=E(r);return n.isErr()?n:f({__serializer_tag:"result_ok",__serializer_value:n.unwrap()})}else{let r=e.unwrapErr(),n=E(r);return n.isErr()?n:f({__serializer_tag:"result_err",__serializer_value:n.unwrap()})}else if(j.isOption(e))if(e.isSome()){let r=e.unwrap(),n=E(r);return n.isErr()?n:f({__serializer_tag:"option_some",__serializer_value:n.unwrap()})}else return f({__serializer_tag:"option_none"});else if(t==="object"){let r={},n=e;for(let a of Object.keys(e)){let o=n[a],i=E(o);if(i.isErr())continue;let s=i.unwrap();r[a]=s}return f(r)}else return x("Unsupported value")}function v(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return y(r);return d},count:function(t){return this.reduce((r,n)=>(t(n)&&r++,r),0)},reduce:function(t,r){let n=r;for(let a of this)n=t(n,a);return n},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>y(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?y(r):d)},enumerate:function(){let t=this;return v(function*(){let r=0;for(let n of t)yield[r,n],r++})()},filterMap:function(t){let r=this;return v(function*(){for(let n of r){let a=t(n);a.isSome()&&(yield a.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return v(function*(){for(let t of e)yield t})()});var R=/.^/,B={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:R,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:R,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:R,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:R,defacto_container:"Mp4"}},ie={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:R,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:R,defacto_container:"Mp4"}},se=v(function*(){for(let e of Object.keys(B))yield B[e]}),le=v(function*(){for(let e of Object.keys(ie))yield ie[e]});function G(e){return typeof e=="string"&&e in B?y(e):d}var K={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:se().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:le().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:y("MP3"),video:y("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:y("AAC"),video:d},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:y("FLAC"),video:d},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:y("MP3"),video:y("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:d,video:d},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:y("Wav"),video:d},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},Pe=v(function*(){for(let e of Object.keys(K))yield e}),_t=v(function*(){for(let e of Pe())yield K[e]});function Y(e){return typeof e=="string"&&e in K?y(e):d}var ue={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};var ce=v(function*(){for(let e of Object.keys(ue))yield e}),xt=v(function*(){for(let e of ce())yield ue[e]});function U(e){if(typeof e=="string")return ce().find(t=>t==e);if(typeof e=="number"){let t=e.toString();return U(t)}return d}function Z(){return{prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"4320",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:[]}}function me(e){return E(e).unwrap()}function de(e){let t=M(e).unwrapOr({}),r=Z(),n=Y(t.container).unwrapOr(r.container),a=G(t.video_codec).unwrapOr(r.video_codec),o=U(t.best_video_quality).unwrapOr(r.best_video_quality),i=U(t.lowest_video_quality).unwrapOr(r.lowest_video_quality),s;if("prefered_video_quality"in t){let S=U(t.prefered_video_quality);S.isSome()&&(s=S.unwrap())}let u=r.max_variants;if(typeof t.max_variants=="number"){let S=t.max_variants;Number.isInteger(S)&&S<=11&&S>0&&(u=S)}let l=r.prefer_60fps;typeof t.prefer_60fps=="boolean"&&(l=t.prefer_60fps);let h=r.ignore_low_quality_hits;typeof t.ignore_low_quality_hits=="boolean"&&(h=t.ignore_low_quality_hits);let A=[];if(Array.isArray(t.ignored_containers))for(let S of t.ignored_containers){let I=Y(S);I.isSome()&&A.push(I.unwrap())}let N=[];if(Array.isArray(t.ignored_video_codecs))for(let S of t.ignored_video_codecs){let I=G(S);I.isSome()&&N.push(I.unwrap())}let O={prefer_60fps:l,ignore_low_quality_hits:h,container:n,max_variants:u,video_codec:a,lowest_video_quality:i,best_video_quality:o,ignored_containers:A,ignored_video_codecs:N};return typeof s<"u"&&(O.prefered_video_quality=s),O}var Me=te(Q(),1);async function fe(e){await pe.storage[e.where].remove(e.name)}var ge={name:"media_user_pref",where:"local",default:()=>Z(),hooks:{setter:e=>me(e),getter:e=>de(e)}};weh.is_safe.then(()=>{let e=combineReducers({prefs:prefsSettingsReducer}),t=createStore(e);listenPrefs(t),window.UseNewUIButton=class extends React.Component{constructor(o){super(o),this.state={}}enableNewUI(){return()=>browser.storage.local.set({use_legacy_ui:!1})}render(){return React.createElement("button",{onClick:this.enableNewUI(),style:{marginRight:"12px"},className:"btn btn-outline-secondary float-right"},"Enable New UI")}};function r(...o){return()=>{weh.rpc.call(...o)}}let n={renderInput:function(){var o=this;return React.createElement("div",{className:"input-group",id:"weh-param-"+o.paramIndex},React.createElement("input",{ref:i=>{i&&(i.value=o.state.value)},onChange:n.onChange.bind(o),className:"form-control"}),React.createElement("span",{className:"input-group-btn"},React.createElement("button",{className:"btn",onClick:()=>n.selectDirectory.call(o)},weh._("change"))))},selectDirectory:function(){var o=this;weh.rpc.call("selectDirectory",this.state.value).then(i=>{i&&o.setCustomValue(i.directory)})},onChange:function(o){var i=this,s=o.target;weh.rpc.call("coappProxy","path.homeJoin",s.value).then(u=>(s.value=u,i.setCustomValue(u),weh.rpc.call("coappProxy","fs.stat",u))).then(u=>{if(!u||u.mode&!1)throw new Error;s.style.backgroundColor="#e8ffe8"}).catch(()=>{s.style.backgroundColor="#ffe8e8"})}};class a extends React.Component{constructor(i){super(i),this.state={activeTab:"general",moreOpen:!1,conversion:void 0,modal:null},this.fileInputChange=this.fileInputChange.bind(this)}setActiveTab(i){this.setState({activeTab:i,conversion:!1})}local(i,...s){var u=this;return()=>{u[i].apply(u,s)}}toggleMore(){var i=this;return()=>{i.setState({moreOpen:!i.state.moreOpen})}}closeModal(){this.setState({modal:null})}conversion(){var i=this;return()=>{i.setState({activeTab:"general",conversion:!0})}}reloadAddon(){var i=this;return()=>{i.setState({modal:{title:weh._("confirmation_required"),body:weh._("reload_addon_confirm"),buttons:[{text:weh._("cancel"),color:"secondary",click:(()=>{this.closeModal()}).bind(i)},{text:weh._("reload_addon"),color:"danger",click:(()=>{weh.rpc.call("reloadAddon"),this.closeModal()}).bind(i)}]}})}}import(){var i=this;return()=>{i.fileInput.click()}}reset(){var i=this,s={prefs:!0,backlist:!1,outputconfigs:!1};function u(l){return h=>{s[l]=h.target.checked}}return()=>{i.setState({modal:{title:weh._("reset_settings"),body:React.createElement("div",{className:"reset-settings"},React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s.prefs,onChange:u("prefs"),className:"form-control",type:"checkbox",id:"reset-prefs"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-prefs"},weh._("preferences"))),React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s.blacklist,onChange:u("blacklist"),className:"form-control",type:"checkbox",id:"reset-blacklist"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-blacklist"},weh._("blacklist"))),React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s.smartnaming,onChange:u("smartnaming"),className:"form-control",type:"checkbox",id:"reset-smartnaming"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-smartnaming"},weh._("smartnaming_rules"))),React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s.outputconfigs,onChange:u("outputconfigs"),className:"form-control",type:"checkbox",id:"reset-outputconfigs"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-outputconfigs"},weh._("conversion_outputs"))),React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s.convrules,onChange:u("convrules"),className:"form-control",type:"checkbox",id:"reset-convrules"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-convrules"},weh._("conversion_rules"))),React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s[MUP_KEY],onChange:u(MUP_KEY),className:"form-control",type:"checkbox",id:"reset-media-user-prefs"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-media-user-prefs"},weh._("video_qualities"))),React.createElement("div",{className:"form-group row"},React.createElement("input",{defaultChecked:s.license,onChange:u("license"),className:"form-control",type:"checkbox",id:"reset-license"}),React.createElement("label",{className:"col-8 col-form-label",htmlFor:"reset-license"},weh._("license")))),buttons:[{text:weh._("cancel"),color:"secondary",click:(()=>{this.closeModal()}).bind(i)},{text:weh._("reset_settings"),color:"danger",click:(()=>{this.doReset(s),this.closeModal()}).bind(i)}]}})}}doReset(i){i.prefs&&(t.dispatch({type:"PREFS_RESET"}),t.dispatch({type:"PREFS_UPDATED",payload:{}}),t.dispatch({type:"PREFS_SAVE",payload:{}})),i.blacklist&&weh.rpc.call("setBlacklist",{}),i.smartnaming&&weh.rpc.call("setSmartName",{}),i.outputconfigs&&weh.rpc.call("setOutputConfigs",{}),i.convrules&&weh.rpc.call("setConversionRules",[]),i[MUP_KEY]&&fe(ge),i.license&&weh.rpc.call("setLicense",null)}fileInputChange(i){var s=this,u=s.fileInput.files[0];if(u){var l=new FileReader;l.onload=h=>{try{var A=JSON.parse(h.target.result);weh.rpc.call("importSettings",A).then(N=>{Object.keys(N).forEach(O=>{t.dispatch({type:"PREF_UPDATE",payload:{prefName:O,value:N[O]}})})})}catch{s.setState({modal:{title:weh._("error"),body:weh._("import_invalid_format"),buttons:[{text:weh._("continue"),color:"secondary",click:(()=>{this.closeModal()}).bind(s)}]}})}},l.readAsText(u)}}setFileInput(i){var s=this;return u=>{u&&u.removeEventListener("change",s.fileInputChange),s.fileInput=u,u&&u.addEventListener("change",s.fileInputChange)}}renderTabGeneral(){return React.createElement(TabPane,{tabId:"general"},!this.state.conversion&&React.createElement("div",null,React.createElement(CopyButton,null),React.createElement(UseNewUIButton,null),React.createElement(AddonInfoPanel,null),React.createElement(PlatformInfoPanel,null),React.createElement(CoAppInfoPanel,null)),React.createElement(LicInfoPanel,{open:this.state.conversion}))}renderTabAppearance(){return React.createElement(TabPane,{tabId:"appearance"},React.createElement(WehParam,{prefName:"titleMode"}),React.createElement(WehParam,{prefName:"iconActivation"}),React.createElement(WehParam,{prefName:"iconBadge"}),React.createElement(WehParam,{prefName:"hitsGotoTab"}),React.createElement(WehParam,{prefName:"notifyReady"}),React.createElement(WehParam,{prefName:"noPrivateNotification"}),React.createElement(WehParam,{prefName:"fileDialogType"}),React.createElement(WehParam,{prefName:"alertDialogType"}),React.createElement(WehParam,{prefName:"dialogAutoClose"}),React.createElement(WehParam,{prefName:"contextMenuEnabled"}),weh.isBrowser("firefox")&&React.createElement(WehParam,{prefName:"toolsMenuEnabled"}))}renderTabBehavior(){return React.createElement(TabPane,{tabId:"behavior"},React.createElement(WehParam,{prefName:"coappDownloads"}),React.createElement(WehParam,{prefName:"lastDownloadDirectory",renderInput:n.renderInput}),React.createElement(WehParam,{prefName:"rememberLastDir"}),React.createElement(WehParam,{prefName:"networkProbe"}),React.createElement(WehParam,{prefName:"use_native_filepicker"}),React.createElement(WehParam,{prefName:"monitorNetworkRequests"}),React.createElement(WehParam,{prefName:"smartnamerFnameSpaces"}),React.createElement(WehParam,{prefName:"smartnamerFnameMaxlen"}),React.createElement(WehParam,{prefName:"downloadControlledMax"}),React.createElement(WehParam,{prefName:"downloadStreamControlledMax"}),React.createElement(WehParam,{prefName:"convertControlledMax"}),React.createElement(WehParam,{prefName:"autoPin"}),React.createElement(WehParam,{prefName:"mediaExtensions"}),React.createElement(WehParam,{prefName:"coappRestartDelay"}),React.createElement(WehParam,{prefName:"coappIdleExit"}),React.createElement(WehParam,{prefName:"dashHideM4s"}),React.createElement(WehParam,{prefName:"mpegtsHideTs"}),React.createElement(WehParam,{prefName:"orphanExpiration"}),React.createElement(WehParam,{prefName:"chunksEnabled"}),React.createElement(WehParam,{prefName:"hlsEnabled"}),React.createElement(WehParam,{prefName:"dashEnabled"}),React.createElement(WehParam,{prefName:"dashOnAdp"}),React.createElement(WehParam,{prefName:"hlsDownloadAsM2ts"}),React.createElement(WehParam,{prefName:"hlsRememberPrevLiveChunks"}),React.createElement(WehParam,{prefName:"hlsEndTimeout"}),React.createElement(WehParam,{prefName:"chunkedCoappManifestsRequests"}),React.createElement(WehParam,{prefName:"chunkedCoappDataRequests"}),React.createElement(WehParam,{prefName:"coappUseProxy"}),React.createElement(WehParam,{prefName:"checkCoappOnStartup"}),React.createElement(WehParam,{prefName:"networkFilterOut"}),React.createElement(WehParam,{prefName:"mediaweightThreshold"}),React.createElement(WehParam,{prefName:"mediaweightMinSize"}),React.createElement(WehParam,{prefName:"converterThreads"}),React.createElement(WehParam,{prefName:"converterAggregTuneH264"}),React.createElement(WehParam,{prefName:"avplayEnabled"}),React.createElement(WehParam,{prefName:"blacklistEnabled"}),React.createElement(WehParam,{prefName:"chunksConcurrentDownloads"}),React.createElement(WehParam,{prefName:"chunksPrefetchCount"}),React.createElement(WehParam,{prefName:"downloadRetries"}),React.createElement(WehParam,{prefName:"downloadRetryDelay"}),React.createElement(WehParam,{prefName:"converterKeepTmpFiles"}),React.createElement(WehParam,{prefName:"contentRedirectEnabled"}),React.createElement(WehParam,{prefName:"bulkEnabled"}),React.createElement(WehParam,{prefName:"galleryNaming"}),React.createElement(WehParam,{prefName:"tbvwsExtractionMethod"}))}renderTabGallery(){return React.createElement(TabPane,{tabId:"gallery"},React.createElement(WehParam,{prefName:"medialinkAutoDetect"}),React.createElement(WehParam,{prefName:"medialinkMinImgSize"}),React.createElement(WehParam,{prefName:"medialinkMinFilesPerGroup"}),React.createElement(WehParam,{prefName:"medialinkMaxHits"}),React.createElement(WehParam,{prefName:"medialinkExtensions"}),React.createElement(WehParam,{prefName:"medialinkScanImages"}),React.createElement(WehParam,{prefName:"medialinkScanLinks"}))}renderSettings(){return React.createElement("div",null,React.createElement(Nav,{tabs:!0},React.createElement(NavItem,null,React.createElement(NavLink,{href:"#",className:this.state.activeTab==="general"&&!this.state.conversion?"active":"",onClick:this.local("setActiveTab","general")},weh._("general"))),React.createElement(NavItem,null,React.createElement(NavLink,{href:"#",className:this.state.activeTab==="appearance"?"active":"",onClick:this.local("setActiveTab","appearance")},weh._("appearance"))),React.createElement(NavItem,null,React.createElement(NavLink,{href:"#",className:this.state.activeTab==="behavior"?"active":"",onClick:this.local("setActiveTab","behavior")},weh._("behavior"))),React.createElement(Dropdown,{nav:!0,isOpen:this.state.moreOpen,toggle:this.toggleMore()},React.createElement(DropdownToggle,{nav:!0,caret:!0},weh._("more")),React.createElement(DropdownMenu,null,React.createElement(DropdownItem,{onClick:this.local("setActiveTab","gallery")},weh._("gallery")),React.createElement(DropdownItem,{onClick:r("editConverterConfigs")},weh._("conversion_outputs")),React.createElement(DropdownItem,{onClick:r("editConversionRules")},weh._("conversion_rules")),React.createElement(DropdownItem,{onClick:r("editBlacklist")},weh._("blacklist")),React.createElement(DropdownItem,{onClick:r("editSmartName")},weh._("smartnaming_rules")),React.createElement(DropdownItem,{onClick:r("editMediaUserPrefs")},weh._("video_qualities")),React.createElement(DropdownItem,{onClick:this.conversion()},weh._("licensing")),React.createElement(DropdownItem,{onClick:r("openTranslation")},weh._("translation")),weh.unsafe_prefs.coappShellEnabled&&React.createElement(DropdownItem,{onClick:r("openCoapp")},weh._("coapp")),React.createElement(DropdownItem,{divider:!0}),React.createElement(DropdownItem,{onClick:r("exportSettings")},weh._("export")),React.createElement(DropdownItem,{onClick:this.import()},weh._("import")),React.createElement(DropdownItem,{onClick:this.reloadAddon()},weh._("reload_addon")),React.createElement(DropdownItem,{onClick:this.reset()},weh._("reset_settings"))))),React.createElement(TabContent,{activeTab:this.state.activeTab},this.renderTabGeneral(),this.renderTabAppearance(),this.renderTabBehavior(),this.renderTabGallery()))}renderPrefsControls(){return React.createElement("div",{className:"btn-toolbar justify-content-end"},React.createElement("div",{className:"btn-group pull-right"},React.createElement("button",{type:"button",onClick:this.props.cancel,className:"btn btn-default "+(this.props.flags.isModified?"":"disabled")},weh._("cancel")),React.createElement("button",{type:"button",onClick:this.props.reset,className:"btn btn-warning "+(this.props.flags.isDefault?"disabled":"")},weh._("default")),React.createElement("button",{type:"button",onClick:this.props.save,className:"btn btn-primary "+(this.props.flags.isModified&&this.props.flags.isValid?"":"disabled")},weh._("save"))))}render(){return React.createElement(PrefsSettingsApp,null,React.createElement(WehHeader,null),React.createElement("main",null,React.createElement("div",{className:"container settings"},React.createElement("section",null,this.renderSettings()))),this.state.activeTab!="general"&&React.createElement("footer",null,React.createElement(WehPrefsControls,{render:this.renderPrefsControls})),React.createElement("input",{type:"file",style:{display:"none"},accept:"application/json",ref:this.setFileInput()}),React.createElement(VDHModal,{modalData:this.state.modal,close:this.closeModal.bind(this)}))}}render(React.createElement(Provider,{store:t},React.createElement(a,null)),document.getElementById("root")),weh.setPageTitle(weh._("settings"))});})();
