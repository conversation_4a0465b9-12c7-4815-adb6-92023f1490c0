{"Bytes": {"message": "$1 Byte"}, "GB": {"message": "$1 GB"}, "KB": {"message": "$1 KB"}, "MB": {"message": "$1 MB"}, "__MSG_appDesc_": {"message": "Video DownloadHelper"}, "about": {"message": "Informazioni programma"}, "about_alpha_extra7_fx": {"message": "A causa di modifiche tecniche interne a Firefox, l'estensione è stata completamente riscritta. Si prega di pazientare qualche settimana per poter riavere tutte le funzionalità delle versioni precedenti."}, "about_alpha_intro": {"message": "Questa è una versione alpha."}, "about_beta_intro": {"message": "Questa è una versione beta."}, "about_chrome_licenses": {"message": "Informazioni sulla licenza di Chrome"}, "about_qr": {"message": "File generato"}, "about_vdh": {"message": "Informazioni su Video DownloadHelper"}, "action_abort_description": {"message": "Annulla l'azione in esecuzione"}, "action_abort_title": {"message": "<PERSON><PERSON><PERSON>"}, "action_as_default": {"message": "Usa questa azione come predefinita"}, "action_avplay_description": {"message": "Riproduci il video con il visualizzatore nativo del convertitore"}, "action_avplay_title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "action_blacklist_description": {"message": "I video provenienti o serviti dai domini selezionati verranno ignorati"}, "action_blacklist_title": {"message": "Aggiungi all'elenco bloccati"}, "action_bulkdownload_description": {"message": "Scarica i video selezionati"}, "action_bulkdownload_title": {"message": "<PERSON><PERSON><PERSON> tutto"}, "action_bulkdownloadconvert_description": {"message": "Scarica tutto e converti i video selezionati"}, "action_bulkdownloadconvert_title": {"message": "Scarica e Converti tutto"}, "action_copyurl_description": {"message": "Copia l'indirizzo del file multimediale negli appunti"}, "action_copyurl_title": {"message": "Copia indirizzo"}, "action_deletehit_description": {"message": "Elimina la connessione dall'elenco corrente"}, "action_deletehit_title": {"message": "Elimina"}, "action_details_description": {"message": "Visualizza dettagli della connessione"}, "action_details_title": {"message": "<PERSON><PERSON><PERSON>"}, "action_download_description": {"message": "Scarica il file sul tuo disco fisso"}, "action_download_title": {"message": "Scarica"}, "action_downloadaudio_description": {"message": "Scarica solo audio"}, "action_downloadaudio_title": {"message": "Scarica solo audio"}, "action_downloadconvert_description": {"message": "Scarica il file multimediale e converti in un altro formato"}, "action_downloadconvert_title": {"message": "Scarica e Converti"}, "action_openlocalcontainer_description": {"message": "Apri la cartella del file locale"}, "action_openlocalcontainer_title": {"message": "<PERSON><PERSON> cartella"}, "action_openlocalfile_description": {"message": "Apri il file multimediale locale"}, "action_openlocalfile_title": {"message": "Apri file"}, "action_pin_description": {"message": "Rendi permanente la connessione"}, "action_pin_title": {"message": "<PERSON><PERSON>"}, "action_quickdownload_description": {"message": "Scarica senza chiedere la destinazione"}, "action_quickdownload_title": {"message": "Scarica subito"}, "action_quickdownloadaudio_description": {"message": "Scarica solo l'audio, senza chiedere la destinazione"}, "action_quickdownloadaudio_title": {"message": "Scaricamento veloce solo audio"}, "action_quicksidedownload_description": {"message": "Scarica senza chiedere la destinazione"}, "action_quicksidedownload_title": {"message": "Scarica subito"}, "action_sidedownload_description": {"message": "Scaricamento sperimentale"}, "action_sidedownload_title": {"message": "Scarica subito"}, "action_sidedownloadconvert_description": {"message": "Scaricamento laterale per file multimediali e conversione in un altro formato"}, "action_sidedownloadconvert_title": {"message": "Scarica subito e Converti"}, "action_stop_description": {"message": "Ferma la cattura"}, "action_stop_title": {"message": "Ferma"}, "adaptative": {"message": "$1 adattivo"}, "add_to_blacklist": {"message": "Aggiungi alla lista nera"}, "add_to_blacklist_help": {"message": "I video provenienti o serviti dai domini selezionati verranno ignorati"}, "advanced": {"message": "Avanzate"}, "aggregating": {"message": "Unione..."}, "analyze_page": {"message": "<PERSON><PERSON><PERSON> pagina"}, "appDesc": {"message": "Scarica i video dal Web"}, "appName": {"message": "Video DownloadHelper"}, "appearance": {"message": "Aspetto"}, "audio_only": {"message": "Solo audio"}, "behavior": {"message": "Comportamento"}, "blacklist": {"message": "<PERSON>enco b<PERSON>i"}, "blacklist_add_domain": {"message": "Aggiungi dominio all'elenco bloccati"}, "blacklist_add_placeholder": {"message": "Dominio da inserire nell'elenco b<PERSON>i"}, "blacklist_edit_descr": {"message": "L'elenco bloccati consente di ignorare i file multimediali provenienti da alcuni domini"}, "blacklist_empty": {"message": "<PERSON><PERSON><PERSON> dominio nell'elen<PERSON> b<PERSON>i"}, "browser_info": {"message": ""}, "browser_locale": {"message": "Lingua del browser: $1"}, "build_options": {"message": "Opzioni di versione: $1"}, "built_on": {"message": "Compilato il $1"}, "bulk_in_progress": {"message": "Video DownloadHelper sta lavorando. Non chiudere questa scheda, sarà fatto automaticamente"}, "bulk_n_videos": {"message": "$1 video"}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "change": {"message": "Modifica"}, "chrome_basic_mode": {"message": "Chrome Base (aggiornamento consigliato)"}, "chrome_inapp_descr_premium_lifetime": {"message": "Validità senza limiti di tempo Premium"}, "chrome_inapp_descr_premium_monthly": {"message": "Validità abbonamento mensile Premium"}, "chrome_inapp_descr_premium_yearly": {"message": "Validità abbonamento annuale Premium"}, "chrome_inapp_no_subs": {"message": "Nota: a seguito del ritiro dei servizi di pagamento di Chrome da parte di Google, gli abbonamenti non sono più disponibili"}, "chrome_inapp_not_avail": {"message": "Non disponibile"}, "chrome_inapp_premium_lifetime": {"message": "Abbonamento a vita Premium"}, "chrome_inapp_premium_monthly": {"message": "Abbonamento mensile Premium"}, "chrome_inapp_premium_yearly": {"message": "Abbonamento annuale Premium"}, "chrome_install_firefox": {"message": "Installa Firefox"}, "chrome_install_fx_vdh": {"message": "Video DownloadHelper per Firefox"}, "chrome_license_webstore_accepted": {"message": "Attiva licenza dal web store di Chrome"}, "chrome_licensing": {"message": "Licenza Chrome"}, "chrome_noyt_text": {"message": "Sfortunatamente, il web store di Chrome non consente estensioni per lo scaricamento di video da YouTube, pertanto si è reso necessario rimuovere questa funzionalità."}, "chrome_noyt_text2": {"message": "Puoi usare Video DownloadHelper con Firefox per scaricare video da YouTube."}, "chrome_noyt_text3": {"message": "Sfortunatamente, il web Store di Chrome non accetta estensioni per lo scaricamento di video da YouTube, quindi non abbiamo potuto includere questa funzione nella versione dell'estensione per Chrome."}, "chrome_premium_audio": {"message": "La generazione di file solo audio è disponibile solo in modalità Premium"}, "chrome_premium_check_error": {"message": "Errore durante il controllo dell'abbonamento Premium"}, "chrome_premium_hls": {"message": "Senza abbonamento Premium, lo scaricamento di un file HLS può essere eseguito solo $1 minuti dopo il precedente"}, "chrome_premium_mode": {"message": "Chrome Premium"}, "chrome_premium_need_sign": {"message": "Per ottenere i vantaggi Premium è necessario registrarsi in Chrome."}, "chrome_premium_not_signed": {"message": "Nessuna registrazione Chrome"}, "chrome_premium_recheck": {"message": "Ricontrolla la validità Premium"}, "chrome_premium_required": {"message": "Validità Premium richiesta"}, "chrome_premium_source": {"message": "Sei un utente premium tramite $1"}, "chrome_product_intro": {"message": "È possibile eseguire l'aggiornamento a Premium usando una delle seguenti opzioni:"}, "chrome_req_review": {"message": "In alternativa, ti dispiacerebbe scrivere una recensione positiva nel sito delle estensioni di Chrome?"}, "chrome_signing_in": {"message": "Registrati su Chrome"}, "chrome_verif_premium": {"message": "Verifico la validità Premium..."}, "chrome_verif_premium_error": {"message": "API per pagamenti in-app non disponibile"}, "chrome_warning_yt": {"message": "Avviso sulle estensioni di Chrome e YouTube"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "clear_hits": {"message": "Svuota elenco con<PERSON>i"}, "clear_logs": {"message": "Pulisci i registri"}, "coapp": {"message": "App Integrativa"}, "coapp_error": {"message": "Esito del controllo della app integrativa:"}, "coapp_found": {"message": "Versione app integrativa:"}, "coapp_help": {"message": "Fai clic qui per risolvere il problema."}, "coapp_install": {"message": "Installa app integrativa"}, "coapp_installed": {"message": "App integrativa installata"}, "coapp_latest_version": {"message": "La versione più recente disponibile è la $1"}, "coapp_not_installed": {"message": "App integrativa non installata"}, "coapp_outdated": {"message": "Applicazione integrativa obsoleta - prego aggiornare"}, "coapp_outofdate": {"message": "Aggiornamento richiesto dell'Applicazione Integrativa"}, "coapp_outofdate_text": {"message": "Stai usando la versione $1 dell'applicazione integrativa ma questa funzione richiede la versione $2"}, "coapp_path": {"message": "Percorso app integrativa:"}, "coapp_recheck": {"message": "Rico<PERSON><PERSON><PERSON>"}, "coapp_required": {"message": "Applicazione integrativa richiesta"}, "coapp_required_text": {"message": "Questa operazione richiede un'applicazione esterna per essere completata."}, "coapp_shell": {"message": "Interfaccia Comandi Applicazione Integrativa"}, "coapp_unchecked": {"message": "Verifico la App Integrativa ..."}, "coapp_update": {"message": "Aggiorna l'Applicazione Integrativa"}, "collecting": {"message": "Unione ..."}, "confirmation_required": {"message": "Conferma richiesta"}, "congratulations": {"message": "Congratulazioni!"}, "continue": {"message": "Continua"}, "convconf_2passes": {"message": "2 passate"}, "convconf_ac": {"message": "Canali audio"}, "convconf_acnone": {"message": "<PERSON><PERSON><PERSON>"}, "convconf_acodec": {"message": "Codec audio"}, "convconf_aspect": {"message": "Proporzioni"}, "convconf_audiobitrate": {"message": "Qualità (bitrate) audio"}, "convconf_audiofreq": {"message": "Frequenza audio"}, "convconf_audioonly": {"message": "Solo audio"}, "convconf_bitrate": {"message": "Qualità (bitrate)"}, "convconf_container": {"message": "Formato"}, "convconf_duplicate": {"message": "Copia"}, "convconf_ext": {"message": "Estensione del file in uscita"}, "convconf_extra": {"message": "Parametri aggiunti<PERSON>"}, "convconf_level": {"message": "<PERSON><PERSON>"}, "convconf_mono": {"message": "Mono"}, "convconf_new": {"message": "Nuovo"}, "convconf_preset": {"message": "Preimpostazioni"}, "convconf_profilev": {"message": "Profilo video"}, "convconf_rate": {"message": "Frequenza fotogrammi"}, "convconf_readonly": {"message": "Questa configurazione predefinita è in sola lettura. Copiala per effettuare modifiche."}, "convconf_remove": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "convconf_reset": {"message": "<PERSON><PERSON><PERSON><PERSON> tutto"}, "convconf_reset_confirm": {"message": "Questo rimuoverà tutte le configurazioni personali"}, "convconf_save": {"message": "<PERSON><PERSON>"}, "convconf_size": {"message": "Dimensione fotogramma"}, "convconf_stereo": {"message": "Stereo"}, "convconf_target": {"message": "Destinazione"}, "convconf_tune": {"message": "Regolazione"}, "convconf_vcodec": {"message": "Codec video"}, "convconf_videobitrate": {"message": "Qualità (bitrate) video"}, "conversion_create_rule": {"message": "<PERSON><PERSON> regola"}, "conversion_outputs": {"message": "Formati di uscita"}, "conversion_rules": {"message": "Regole di conversione"}, "conversion_update_rule": {"message": "Aggiorna regola"}, "convert": {"message": "<PERSON><PERSON><PERSON>"}, "convert_local_files": {"message": "Converti file locali"}, "converter_needed_aggregate": {"message": "Questa operazione richiede una licenza del convertitore sul tuo sistema per unire i flussi audio e video."}, "converter_needed_aggregate_why": {"message": "perché mi serve un convertitore?"}, "converter_needs_reg": {"message": "Registrazione necessaria"}, "converter_queued": {"message": "Convertitore accodato ..."}, "converter_reg_audio": {"message": "<PERSON> richiesto, esplicitamente o da una regola automatica di conversione, la generazione di un file con solo audio. Ciò richiede un convertitore registrato."}, "converting": {"message": "Conversione ..."}, "convrule_convert": {"message": "<PERSON><PERSON><PERSON>"}, "convrule_domain": {"message": "<PERSON>inio"}, "convrule_extension": {"message": "Estensione"}, "convrule_format": {"message": "nel formato $1"}, "convrule_from_domain": {"message": "dal formato $1"}, "convrule_no_convert": {"message": "Non convertire"}, "convrule_output_format": {"message": "Formato di uscita"}, "convrule_refresh_formats": {"message": "Aggiorna i formati di uscita"}, "convrule_with_ext": {"message": "con estensione $1"}, "convrules_add_rule": {"message": "Crea una nuova regola di conversione"}, "convrules_edit_descr": {"message": "Le regole di conversione consentono di eseguire la conversione dei file multimediali automaticamente dopo lo scaricamento"}, "convrules_empty": {"message": "Nessuna regola di conversione"}, "copy_of": {"message": "Copia di $1"}, "copy_settings_info_to_clipboard": {"message": "Copia le informazioni negli appunti"}, "copy_settings_info_to_clipboard_success": {"message": "Informazioni copiate negli appunti."}, "corrupted_media_file": {"message": "Impossibile ottenere informazioni dal supporto $1 a causa del file $2. Il file potrebbe essere danneggiato."}, "create": {"message": "<PERSON><PERSON>"}, "custom_output": {"message": "Formato di uscita personalizzato"}, "dash_streaming": {"message": "Flusso DASH"}, "default": {"message": "Predefinito"}, "details_parenthesis": {"message": "(<PERSON><PERSON><PERSON>)"}, "dev_build": {"message": "<PERSON>e di sviluppo"}, "dialog_audio_impossible": {"message": "Questo tipo di file multimediale non supporta lo scaricamento del solo audio"}, "dialog_audio_impossible_title": {"message": "Impossibile scaricare l'audio"}, "directory_not_exist": {"message": "La cartella non esiste"}, "directory_not_exist_body": {"message": "La cartella $1 non esiste, vuoi crearla?"}, "dlconv_download_and_convert": {"message": "Scarica e Converti"}, "dlconv_output_details": {"message": "Configura dettagli destinazione"}, "donate": {"message": "<PERSON><PERSON>"}, "donate_vdh": {"message": "Aiuta Video DownloadHelper"}, "download_error": {"message": "Errore di scaricamento"}, "download_method": {"message": "<PERSON><PERSON><PERSON> di scaricamento"}, "download_method_not_again": {"message": "La prossima volta usa questo metodo come predefinito"}, "download_modes1": {"message": "Questo scaricamento può essere eseguito con il browser o con l'applicazione integrativa."}, "download_modes2": {"message": "Per motivi tecnici, lo scaricamento come servizio del browser può causare il rifiuto da parte del server che ospita il video e non è possibile definire una cartella di scaricamento predefinita alternativa."}, "download_with_browser": {"message": "Usa il browser"}, "download_with_coapp": {"message": "Usa l'Applicazione Integrativa"}, "downloading": {"message": "Scaricamento ..."}, "edge_req_review": {"message": "In alternativa, ti dispiacerebbe scrivere una recensione positiva sul sito delle estensioni di Microsoft Edge?"}, "error": {"message": "Errore"}, "error_not_directory": {"message": "$1 esiste ma non è una cartella"}, "errors": {"message": "<PERSON><PERSON><PERSON>"}, "exit_natmsgsh": {"message": "Esci dall'Applicazione Integrativa"}, "explain_qr1": {"message": "Noterai che il video risultante avrà una filigrana in un angolo."}, "explain_qr2": {"message": "Questo perché la funzione di conversione non è stata registrata."}, "export": {"message": "Esporta"}, "failed_aggregating": {"message": "Unione di $1 fallita"}, "failed_converting": {"message": "Conversione di $1 fallita"}, "failed_getting_info": {"message": "Raccolta informazioni da $1 fallita"}, "failed_opening_directory": {"message": "Impossibile aprire la cartella del file"}, "failed_playing_file": {"message": "Impossibile riprodurre il file"}, "file_dialog_date": {"message": "Data"}, "file_dialog_name": {"message": "Nome"}, "file_dialog_size": {"message": "Dimensione"}, "file_generated": {"message": "Il file $1 è stato generato."}, "file_ready": {"message": "Il file $1 è pronto"}, "finalizing": {"message": "Messa a punto ..."}, "from_domain": {"message": "Da $1"}, "gallery": {"message": "Galleria"}, "gallery_files_types": {"message": "$1 file"}, "gallery_from_domain": {"message": "Galleria da $1"}, "gallery_links_from_domain": {"message": "Collegamento da $1"}, "general": {"message": "Generale"}, "get_conversion_license": {"message": "Ottieni una licenza di conversione"}, "help_translating": {"message": "Aiutaci nella traduzione"}, "hit_details": {"message": "Dettagli <PERSON>"}, "hit_go_to_tab": {"message": "Vai alla scheda"}, "hls_streaming": {"message": "Flusso HLS"}, "homepage": {"message": "Pagina principale"}, "import": {"message": "Importa"}, "import_invalid_format": {"message": "Formato non valido"}, "in_current_tab": {"message": "<PERSON>a scheda attuale"}, "in_other_tab": {"message": "In altre schede"}, "lic_mismatch1": {"message": "La licenza è per $1 ma non è stata specificata la versione dell'estensione del browser"}, "lic_mismatch2": {"message": "La licenza è per $1 ma la versione dell'estensione è $2"}, "lic_not_needed_linux": {"message": "Il nostro contributo a Linux: non è richiesta alcuna licenza"}, "lic_status_accepted": {"message": "Licenza verificata"}, "lic_status_blocked": {"message": "Licenza bloccata"}, "lic_status_error": {"message": "Errore licenza"}, "lic_status_locked": {"message": "Licenza bloccata (da rivalidare)"}, "lic_status_mismatch": {"message": "Licenza e/o browser non corrispondono"}, "lic_status_nocoapp": {"message": "Impossibile verificare la licenza"}, "lic_status_unneeded": {"message": "Licenza non necessaria"}, "lic_status_unset": {"message": "Licenza non impostata"}, "lic_status_unverified": {"message": "Licenza non verificata"}, "lic_status_verifying": {"message": "Verifica della licenza ..."}, "license": {"message": "Licenza"}, "license_key": {"message": "Codice della licenza"}, "licensing": {"message": "Licenza concessa"}, "live_stream": {"message": "flusso in diretta"}, "logs": {"message": "Registri eventi"}, "media": {"message": "File multimediale"}, "merge_error": {"message": "Errore di unione"}, "merge_local_files": {"message": "Unisci i file audio e video locali"}, "more": {"message": "Altro ..."}, "mup_best_video_quality": {"message": "Qualità video preferita"}, "mup_ignore_low_quality": {"message": "Ignora video di bassa qualità"}, "mup_ignore_low_quality_help": {"message": "Alcune pagine includono contenuti multimediali di bassa qualità che vengono usati solo come effetti, come un file WAV per il suono di un clic o un breve video per una piccola animazione della pagina"}, "mup_ignored_containers": {"message": "Ignora i contenitori dei file multimediali"}, "mup_ignored_video_codecs": {"message": "Ignora i codec dei file multimediali"}, "mup_lowest_video_quality": {"message": "Ignora rigorosamente la qualità inferiore"}, "mup_max_variants": {"message": "Numero massimo di varianti"}, "mup_max_variants_help": {"message": "Ogni video è disponibile in formati diversi. Visualizziamo prima la versione migliore e anche alcuni formati alternativi (varianti)"}, "mup_page_title": {"message": "Preferenze multimediali"}, "mup_prefer_60fps": {"message": "Preferisci dai 60 fotogrammi al secondo in su"}, "mup_prefered_container": {"message": "Formato contenitore preferito"}, "mup_prefered_video_codecs": {"message": "Codec video preferiti"}, "mup_reset": {"message": "<PERSON><PERSON><PERSON>"}, "mup_saved": {"message": "salvato!"}, "network_error_no_response": {"message": "Errore di rete - nessuna risposta"}, "network_error_status": {"message": "Errore di rete - stato $1"}, "new_sub_directory": {"message": "Crea una sotto cartella"}, "next": {"message": "Successivo"}, "no": {"message": "No"}, "no_audio_in_file": {"message": "<PERSON><PERSON><PERSON> flusso audio nel file $1"}, "no_coapp_license_unverified": {"message": "La licenza non può essere verificata perché l'app integrativa non è installata"}, "no_license_registered": {"message": "Nessuna licenza registrata"}, "no_media_current_tab": {"message": "Nessun file multimediale da elaborare in questa pagina"}, "no_media_to_process": {"message": "Nessun file multimediale da elaborare"}, "no_media_to_process_descr": {"message": "Clicca riproduci sul video per rilevare il tipo di file ..."}, "no_such_hit": {"message": "<PERSON><PERSON><PERSON>o"}, "no_validate_without_coapp": {"message": "L'app integrativa deve essere installata per convalidare la licenza"}, "no_video_in_file": {"message": "<PERSON><PERSON><PERSON> flusso video nel file $1"}, "not_again_3months": {"message": "Non chiedermelo più per 3 mesi"}, "not_see_again": {"message": "Non visualizzare più quel messaggio"}, "number_type": {"message": "$1 $2"}, "ok": {"message": "OK"}, "orphan": {"message": "Orfan<PERSON>"}, "output_configuration": {"message": "Configurazione di uscita"}, "overwrite_file": {"message": "Sovrascrivo il file $1?"}, "per_month": {"message": "/ mese"}, "per_year": {"message": "/ anno"}, "pinned": {"message": "Fissato"}, "platform": {"message": "Piattaforma"}, "platform_info": {"message": "Piattaforma $1 $2"}, "powered_by_weh": {"message": "Implementato da We<PERSON>"}, "preferences": {"message": "Prefer<PERSON><PERSON>"}, "prod_build": {"message": "Versione di produzione"}, "quality_medium": {"message": "Media"}, "quality_small": {"message": "Bass<PERSON>"}, "queued": {"message": "In coda ..."}, "recheck_license": {"message": "Ricontrollo licenza"}, "register_converter": {"message": "Registra convertitore"}, "register_existing_license": {"message": "Registra una licenza esistente"}, "registered_email": {"message": "Email"}, "registered_key": {"message": "Codice"}, "reload_addon": {"message": "Ricarica estensione"}, "reload_addon_confirm": {"message": "Sei sicuro di voler ricaricare l'estensione?"}, "req_donate": {"message": "Vorresti considerare di supportare lo sviluppo e donare un piccolo contributo?"}, "req_locale": {"message": "O forse puoi aiutare a tradurre il componente aggiuntivo in $1? ($2 stringhe non tradotte)"}, "req_review": {"message": "<PERSON><PERSON><PERSON>, ti dispiacerebbe scrivere una recensione positiva sul sito di Mozilla del componente aggiuntivo?"}, "req_review_link": {"message": "Scrivi una recensione su Video DownloadHelper"}, "reset_settings": {"message": "Rip<PERSON><PERSON> le impostazioni"}, "running": {"message": "In esecuzione"}, "save": {"message": "<PERSON><PERSON>"}, "save_as": {"message": "<PERSON><PERSON> come ..."}, "save_file_as": {"message": "Salva file come ..."}, "select_audio_file_to_merge": {"message": "Seleziona il file che contiene il flusso audio"}, "select_files_to_convert": {"message": "Converti file locali"}, "select_output_config": {"message": "Seleziona la configurazione di uscita ..."}, "select_output_directory": {"message": "Cartella di destinazione"}, "select_video_file_to_merge": {"message": "Seleziona il file che contiene il flusso video"}, "selected_media": {"message": "Se<PERSON><PERSON>na tutto"}, "settings": {"message": "Impostazioni"}, "smartname_add_domain": {"message": "Aggiungi una regola di Rinomina Rapida"}, "smartname_create_rule": {"message": "<PERSON><PERSON> regola"}, "smartname_define": {"message": "Definisci una regola di Rinomina Rapida"}, "smartname_edit_descr": {"message": "Una regola di Rinomina Rapida consente di personalizzare i nomi dei video in base alla provenienza"}, "smartname_empty": {"message": "Nessuna regola di Rinomina Rapida"}, "smartname_update_rule": {"message": "Aggiorna regola"}, "smartnamer_delay": {"message": "<PERSON><PERSON> nel denominare la cattura (ms)"}, "smartnamer_domain": {"message": "<PERSON>inio"}, "smartnamer_get_name_from_header_url": {"message": "Prendi il nome dal titolo del documento/indirizzo"}, "smartnamer_get_name_from_page_content": {"message": "Prendi il nome dal contenuto della pagina"}, "smartnamer_get_name_from_page_title": {"message": "Prendi il nome dal titolo della pagina"}, "smartnamer_get_obfuscated_name": {"message": "Usa nome offuscato"}, "smartnamer_regexp": {"message": "<PERSON><PERSON><PERSON><PERSON> regol<PERSON>"}, "smartnamer_selected_text": {"message": "Testo selezionato"}, "smartnamer_xpath_expr": {"message": "Espressione XPath"}, "smartnaming_rule": {"message": "Regola di Rinomina Rapida"}, "smartnaming_rules": {"message": "Regole di Rinomina Rapida"}, "sub_directory_name": {"message": "Nome della sotto cartella"}, "support_forum": {"message": "Forum di supporto"}, "supported_sites": {"message": "Siti supportati"}, "tbsn_quality_hd": {"message": "Qualità media"}, "tbsn_quality_sd": {"message": "Qualit<PERSON> bassa"}, "tell_me_more": {"message": "<PERSON><PERSON> altre informazioni"}, "title": {"message": "Video DownloadHelper"}, "translation": {"message": "Traduzione"}, "up": {"message": "Su"}, "v9_about_qr": {"message": "File generato"}, "v9_badge_new": {"message": "nuovo"}, "v9_blacklist_glob": {"message": "Usa '*' per una corrispondenza più ampia."}, "v9_checkbox_remember_action": {"message": "Ricorda come azione predefinita"}, "v9_chrome_noyt_text2": {"message": "Per scaricare video da YouTube puoi usare Video DownloadHelper con Firefox."}, "v9_chrome_noyt_text3": {"message": "Sfortunatamente, il web store di Chrome non accetta estensioni per lo scaricamento di video da YouTube, quindi non abbiamo potuto includere questa funzione nella versione dell'estensione per Chrome."}, "v9_chrome_premium_hls": {"message": "Senza abbonamento Premium, lo scaricamento di un file HLS può essere eseguito solo $1 minuti dopo il precedente"}, "v9_chrome_premium_required": {"message": "Abbonamento Premium richiesto"}, "v9_chrome_warning_yt": {"message": "Avviso sulle estensioni di Chrome e YouTube"}, "v9_coapp_help": {"message": "Fai clic qui per risolvere il problema."}, "v9_coapp_install": {"message": "Installa App Integrativa"}, "v9_coapp_installed": {"message": "App Integrativa installata"}, "v9_coapp_not_installed": {"message": "App Integrativa non installata"}, "v9_coapp_outdated": {"message": "App integrativa obsoleta - prego aggiornare"}, "v9_coapp_recheck": {"message": "Rico<PERSON><PERSON><PERSON>"}, "v9_coapp_required": {"message": "App Integrativa richiesta"}, "v9_coapp_required_text": {"message": "Questa operazione richiede un'applicazione esterna per essere completata."}, "v9_coapp_unchecked": {"message": "Verifico la App Integrativa ..."}, "v9_coapp_update": {"message": "Aggiorna la App Integrativa"}, "v9_converter_needs_reg": {"message": "Registrazione necessaria"}, "v9_converter_reg_audio": {"message": "<PERSON> richiesto, esplicitamente o da una regola automatica di conversione, la generazione di un file con solo audio. Ciò richiede un convertitore registrato."}, "v9_copy_settings_info_to_clipboard": {"message": "Copia le informazioni negli appunti"}, "v9_date_long_ago": {"message": "molto tempo fa"}, "v9_date_today": {"message": "oggi"}, "v9_date_x_days_ago": {"message": "$1 giorni fa"}, "v9_date_yesterday": {"message": "ieri"}, "v9_dialog_audio_impossible": {"message": "Questo tipo di file multimediale non supporta lo scaricamento del solo audio"}, "v9_dialog_audio_impossible_title": {"message": "Impossibile scaricare l'audio"}, "v9_error": {"message": "Errore"}, "v9_explain_qr1": {"message": "Noterai che il video risultante avrà una filigrana in un angolo."}, "v9_file_ready": {"message": "Il file $1 è pronto"}, "v9_filepicker_select_download_dir": {"message": "Seleziona la Cartella di Scaricamento"}, "v9_filepicker_select_file": {"message": "Seleziona file"}, "v9_get_conversion_license": {"message": "Ottieni una licenza di conversione"}, "v9_history_button_clear": {"message": "Pulisci la cronologia"}, "v9_history_button_start_recording": {"message": "Salva la cronologia"}, "v9_history_button_stop_recording": {"message": "Non salvare la cronologia"}, "v9_history_input_search": {"message": "Cerca"}, "v9_history_no_entries": {"message": "Nessuna voce ancora."}, "v9_history_no_recording_description": {"message": "Non registriamo la cronologia degli scaricamenti. Vuoi che Video DownloadHelper salvi la cronologia degli scaricamenti?"}, "v9_history_no_recording_description_safe": {"message": "Non preoccuparti, tutto rimane sul tuo computer. Diamo valore alla tua riservatezza."}, "v9_history_page_title": {"message": "La tua cronologia degli scaricamenti"}, "v9_lic_mismatch2": {"message": "La licenza è per $1 ma la versione dell'estensione è $2"}, "v9_lic_status_accepted": {"message": "Licenza verificata"}, "v9_lic_status_blocked": {"message": "Licenza bloccata"}, "v9_lic_status_locked": {"message": "Licenza bloccata (da rivalidare)"}, "v9_lic_status_locked2": {"message": "Licenza bloccata (controlla la tua email)"}, "v9_lic_status_unset": {"message": "Licenza non impostata"}, "v9_lic_status_verifying": {"message": "Verifica della licenza ..."}, "v9_menu_item_blacklist": {"message": "Elementi nell'elenco b<PERSON>i"}, "v9_menu_item_blacklist_domain": {"message": "Domini degli elementi bloccati"}, "v9_menu_item_blacklist_media": {"message": "File multimediali nell'elenco b<PERSON>i"}, "v9_menu_item_blacklist_page": {"message": "Pagina degli elementi bloccati"}, "v9_menu_item_details": {"message": "<PERSON><PERSON><PERSON>"}, "v9_menu_item_download_and_convert": {"message": "Scarica e converti in"}, "v9_menu_item_smartnaming": {"message": "Elementi in Rinomina Rapida"}, "v9_mup_max_variants": {"message": "Numero di varianti"}, "v9_no": {"message": "No"}, "v9_no_license_registered": {"message": "Nessuna licenza registrata"}, "v9_no_media_current_tab": {"message": "Nessun file multimediale da elaborare in questa pagina"}, "v9_no_media_to_process_descr": {"message": "Clicca riproduci sul video per rilevare il tipo di file ..."}, "v9_no_validate_without_coapp": {"message": "La App Integrativa deve essere installata per convalidare la licenza"}, "v9_not_see_again": {"message": "Non visualizzare più quel messaggio"}, "v9_panel_copy_url_button_label": {"message": "Copia Indirizzo"}, "v9_panel_download_as_button_label": {"message": "Scari<PERSON> come ..."}, "v9_panel_download_audio_button_label": {"message": "Scarica Audio"}, "v9_panel_download_button_label": {"message": "Scarica"}, "v9_panel_downloadable_variant_no_details": {"message": "nessun dettaglio"}, "v9_panel_downloaded_delete_file_tooltip": {"message": "Elimina file"}, "v9_panel_downloaded_retry_tooltip": {"message": "Scarica di nuovo"}, "v9_panel_downloaded_show_dir_tooltip": {"message": "Visualizza cartella di scaricamento"}, "v9_panel_downloading_stop": {"message": "stop"}, "v9_panel_error_coapp_failure_description": {"message": "Sfortunatamente lo scaricamento di questo file multimediale non è andato a buon fine. Cerchiamo di supportare più siti internet possibile e ci aiuterebbe molto se ci inviassi un resoconto di questo errore (è anonimo!)."}, "v9_panel_error_coapp_failure_description_no_report": {"message": "Purtroppo non siamo riusciti a scaricare quello specifico file multimediale."}, "v9_panel_error_coapp_failure_title": {"message": "Scaricamento fallito"}, "v9_panel_error_coapp_too_old_button_udpate": {"message": "Aggiorna"}, "v9_panel_error_nocoapp_button_install": {"message": "Scarica e Installa"}, "v9_panel_error_report_button2": {"message": "<PERSON><PERSON><PERSON>"}, "v9_panel_error_reported_button": {"message": "<PERSON><PERSON><PERSON><PERSON>, grazie!"}, "v9_panel_error_unknown_description": {"message": "Purtroppo il componente aggiuntivo ha riscontrato un errore imprevisto. Sarebbe di grande aiuto se potessi segnalare l'errore (è anonimo!)."}, "v9_panel_footer_clean_all_tooltip": {"message": "Rimuovi i file multimediali rilevati e scaricati (i file non vengono cancellati)"}, "v9_panel_footer_clean_tooltip": {"message": "Rimuovi i file multimediali rilevati"}, "v9_panel_footer_convert_local_tooltip": {"message": "Converti file locali"}, "v9_panel_footer_force_reload": {"message": "Forza il rilevamento"}, "v9_panel_footer_show_history_tooltip": {"message": "Visualizza la cronologia degli scaricamenti"}, "v9_panel_footer_show_in_popup_tooltip": {"message": "Mostra come finestra a comparsa"}, "v9_panel_footer_show_in_sidebar_tooltip": {"message": "Visualizza nella barra laterale"}, "v9_panel_variant_menu_prefer_format": {"message": "Preferisci sempre questo formato"}, "v9_panel_variant_menu_prefer_quality": {"message": "Preferisci sempre questa qualità"}, "v9_panel_view_clean": {"message": "Visualizza pulsante <PERSON>"}, "v9_panel_view_clean_all": {"message": "Visualizza pulsante P<PERSON> tutto"}, "v9_panel_view_hide_downloaded": {"message": "Nascondi automaticamente i file multimediali scaricati"}, "v9_panel_view_open_settings": {"message": "Altre impostazioni"}, "v9_panel_view_show_all_tabs": {"message": "<PERSON><PERSON><PERSON> tutte le schede"}, "v9_panel_view_show_low_quality": {"message": "Visualizza i contenuti multimediali di bassa qualità"}, "v9_panel_view_sort_reverse": {"message": "Ordinamento inverso"}, "v9_panel_view_sort_status": {"message": "Ordina per stato"}, "v9_reset": {"message": "R<PERSON><PERSON><PERSON>"}, "v9_save": {"message": "<PERSON><PERSON>"}, "v9_settings": {"message": "Impostazioni"}, "v9_settings_button_export": {"message": "Esporta impostazioni"}, "v9_settings_button_import": {"message": "Importa impostazioni"}, "v9_settings_button_reload": {"message": "Ricarica il componente aggiuntivo"}, "v9_settings_button_reset": {"message": "<PERSON><PERSON><PERSON><PERSON>ost<PERSON>"}, "v9_settings_button_reset_privacy": {"message": "Azzera Impostazioni Riservatezza"}, "v9_settings_checkbox_force_inbrowser": {"message": "Non usare la App Integrativa quando possibile"}, "v9_settings_checkbox_forget_on_close": {"message": "Pulisci i file multimediali rilevati quando si chiude la scheda"}, "v9_settings_checkbox_notification": {"message": "Visualizza la notifica a scaricamento completato"}, "v9_settings_checkbox_notification_incognito": {"message": "Visualizza le notifiche in modalità Navigazione Privata"}, "v9_settings_checkbox_thumbnail_in_notification": {"message": "Mostra l'anteprima nelle notifiche"}, "v9_settings_checkbox_use_legacy_ui": {"message": "Usa l'Interfaccia Utente precedente"}, "v9_settings_checkbox_use_wide_ui": {"message": "Ingrandisci la finestra a comparsa del componente aggiuntivo"}, "v9_settings_checkbox_view_convert_local": {"message": "Visualizza pulsante Converti in Locale"}, "v9_settings_download_directory": {"message": "Cartella di Scaricamento"}, "v9_settings_download_directory_change": {"message": "Modifica"}, "v9_settings_history_limit": {"message": "Dimentica la cronologia degli scaricamenti dopo X giorni"}, "v9_settings_license_check": {"message": "Verifica la chiave della licenza"}, "v9_settings_license_get": {"message": "Ottieni licenza"}, "v9_settings_license_placeholder": {"message": "Inserisci licenza"}, "v9_settings_theme_dark": {"message": "scuro"}, "v9_settings_theme_light": {"message": "chiaro"}, "v9_settings_theme_system": {"message": "sistema"}, "v9_settings_theme_title": {"message": "<PERSON><PERSON>"}, "v9_settings_variants_clear": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "v9_settings_variants_title": {"message": "Preferenze varie"}, "v9_short_help": {"message": "<PERSON><PERSON>"}, "v9_smartnaming_max_length": {"message": "Lunghezza massima"}, "v9_smartnaming_reset_for": {"message": "<PERSON><PERSON><PERSON><PERSON> per"}, "v9_smartnaming_reset_for_all": {"message": "<PERSON><PERSON><PERSON><PERSON> tutte le regole dei siti"}, "v9_smartnaming_result": {"message": "Risultato"}, "v9_smartnaming_save_for": {"message": "Salva per"}, "v9_smartnaming_save_for_all": {"message": "Salva per tutti i siti"}, "v9_smartnaming_selector": {"message": "Testo dal selettore CSS (opzionale)"}, "v9_smartnaming_template": {"message": "Modello. Usa: %title %hostname %pathname %selector"}, "v9_smartnaming_test": {"message": "<PERSON><PERSON>"}, "v9_smartnaming_title": {"message": "Rinomina Rapida"}, "v9_tell_me_more": {"message": "<PERSON><PERSON> altre informazioni"}, "v9_user_message_auto_hide_downloaded": {"message": "Vuoi nascondere automaticamente i file multimediali scaricati?"}, "v9_user_message_no_incognito_body": {"message": "Video DownloadHelper non è abilitato nelle finestre Private o in Incognito. È necessario attivare questa opzione manualmente (non è obbligatorio)."}, "v9_user_message_no_incognito_open_settings": {"message": "Abilita nelle Impostazioni del Browser"}, "v9_user_message_no_incognito_title": {"message": "Nessuna modalità in Incognito"}, "v9_user_message_one_hundred_downloads": {"message": "Hai scaricato 100 video!"}, "v9_user_message_one_hundred_downloads_body": {"message": "Ci auguriamo che Video DownloadHelper ti piaccia :) Ti dispiacerebbe scrivere una bella recensione sul sito Web del componente aggiuntivo?"}, "v9_user_message_one_hundred_downloads_leave_review": {"message": "Lascia una recensione"}, "v9_user_message_one_hundred_downloads_never_show_again": {"message": "Non chiedermelo più"}, "v9_user_message_privacy_policy_accept": {"message": "Accetta"}, "v9_user_message_privacy_policy_accept_long": {"message": "Continua condividendo e con gli scaricamenti avanzati con la Coapp"}, "v9_user_message_privacy_policy_decline": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "v9_user_message_privacy_policy_decline_long": {"message": "Continua senza condividere e solo per scaricamenti basilari"}, "v9_user_message_privacy_policy_details": {"message": "<PERSON><PERSON><PERSON>"}, "v9_user_message_privacy_policy_details_long": {"message": "Apri le Informazioni sulla Riservatezza su addons.mozilla.org"}, "v9_user_message_privacy_policy_text_1": {"message": "Quando l'utente avvia uno scaricamento, scarichiamo il video direttamente, o per scaricamenti più complessi (M3U8 e MPD) trasmettiamo le intestazioni di indirizzo e protocollo (URL e HTTP) all'applicazione nativa Video DownloadHelper sul tuo computer (l'indirizzo non ci viene mai inviato)."}, "v9_user_message_privacy_policy_text_2": {"message": "Il rifiuto della nostra politica sulla riservatezza consentirà comunque di scaricare alcuni video, ma lo scaricamento di video M3U8 e MPD non funzionerà."}, "v9_user_message_privacy_policy_text_3": {"message": "Se l'utente segnala un problema, questo viene inviato direttamente alla nostra piattaforma che gestisce le segnalazioni: numero di versione, agente del browser, sistema operativo, descrizione del problema, origine dello scaricamento (registriamo solo il nome del dominio). Non utilizziamo né archiviamo alcun dettaglio sull'identità del segnalatore. N<PERSON>un indirizzo, nessuna impronta digitale, nessun cookie, nessun identificativo. La segnalazione del problema è anonima."}, "v9_user_message_privacy_policy_title": {"message": "Informativa sulla riservatezza - <PERSON><PERSON><PERSON>, rimani anonimo"}, "v9_vdh_notification": {"message": "Video DownloadHelper"}, "v9_weh_prefs_description_contextMenuEnabled": {"message": "Accedi ai comandi nella pagina con il clic destro del mouse"}, "v9_weh_prefs_label_downloadControlledMax": {"message": "Numero massimo di scaricamenti simultanei"}, "v9_yes": {"message": "Sì"}, "v9_yt_bulk_detected": {"message": "Trovati $1 video da Youtube"}, "v9_yt_bulk_detected_trigger": {"message": "Avvia lo scaricamento in blocco"}, "validate_license": {"message": "Registra licenza"}, "variants_list_adp": {"message": "V<PERSON>ti video adattivi"}, "variants_list_full": {"message": "<PERSON><PERSON><PERSON>"}, "vdh_notification": {"message": "Video DownloadHelper"}, "version": {"message": "Versione $1"}, "video_only": {"message": "Solo video"}, "video_qualities": {"message": "Qualità video"}, "weh_prefs_alertDialogType_option_panel": {"message": "Finestra"}, "weh_prefs_alertDialogType_option_tab": {"message": "Scheda"}, "weh_prefs_coappDownloads_option_ask": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_coappDownloads_option_browser": {"message": "Browser"}, "weh_prefs_coappDownloads_option_coapp": {"message": "Applicazione Integrativa"}, "weh_prefs_dashOnAdp_option_audio": {"message": "Scarica audio"}, "weh_prefs_dashOnAdp_option_audio_video": {"message": "Unisci audio e video"}, "weh_prefs_dashOnAdp_option_video": {"message": "Scarica video"}, "weh_prefs_description_adpHide": {"message": "Non visualizzare le varianti ADP nell'elenco di scaricamento"}, "weh_prefs_description_alertDialogType": {"message": "Come sono visualizzate le finestre di avviso"}, "weh_prefs_description_autoPin": {"message": "Blocca la connessione dopo lo scaricamento."}, "weh_prefs_description_avplayEnabled": {"message": "Permetti la riproduzione del video con l'applicazione integrativa"}, "weh_prefs_description_blacklistEnabled": {"message": "Impedisci ad alcuni siti di attivare il riconoscimento dei file multimediali."}, "weh_prefs_description_bulkEnabled": {"message": "Abilita le operazioni per scaricare tutto"}, "weh_prefs_description_checkCoappOnStartup": {"message": "All'avvio del componente aggiuntivo, verifica se l'applicazione integrativa è disponibile per un migliore rilevamento dei file multimediali"}, "weh_prefs_description_chunkedCoappDataRequests": {"message": "Richiedi i dati dei blocchi quando usi l'applicazione integrativa"}, "weh_prefs_description_chunkedCoappManifestsRequests": {"message": "<PERSON><PERSON> i blocchi in chiaro quando usi l'applicazione integrativa"}, "weh_prefs_description_chunksConcurrentDownloads": {"message": "Numero massimo di blocchi da scaricare simultaneamente"}, "weh_prefs_description_chunksEnabled": {"message": "Flusso a blocchi attivato"}, "weh_prefs_description_chunksPrefetchCount": {"message": "Quanti blocchi scaricare in anticipo"}, "weh_prefs_description_coappDownloads": {"message": "Applicazione che esegue lo scaricamento"}, "weh_prefs_description_coappIdleExit": {"message": "Chiusura automatica dopo il numero di millisecondi, disabilitata se 0"}, "weh_prefs_description_coappRestartDelay": {"message": "<PERSON><PERSON> in millisecondi per il riavvio dell'applicazione integrativa"}, "weh_prefs_description_coappUseProxy": {"message": "La App Integrativa usa lo stesso proxy dell'istanza originale"}, "weh_prefs_description_contentRedirectEnabled": {"message": "Alcuni siti potrebbero restituire un nuovo indirizzo invece del contenuto multimediale"}, "weh_prefs_description_contextMenuEnabled": {"message": "Accedi ai comandi nella pagina con il clic destro del mouse"}, "weh_prefs_description_convertControlledMax": {"message": "Il numero massimo di processi simultanei di unione o di conversione"}, "weh_prefs_description_converterAggregTuneH264": {"message": "Forza l'ottimizzazione H264 durante l'unione"}, "weh_prefs_description_converterKeepTmpFiles": {"message": "Non rimuovere i file temporanei dopo l'elaborazione"}, "weh_prefs_description_converterThreads": {"message": "Numero di processi usati durante la conversione"}, "weh_prefs_description_dashEnabled": {"message": "Flusso DASH a blocchi attivo"}, "weh_prefs_description_dashHideM4s": {"message": "Non visualizzare le voci .m4s nell'elenco scaricamenti"}, "weh_prefs_description_dashOnAdp": {"message": "Quando il flusso DASH contiene sia audio che video"}, "weh_prefs_description_dialogAutoClose": {"message": "Le finestre di dialogo si chiudono quando perdono il focus"}, "weh_prefs_description_downloadControlledMax": {"message": "Per preservare un po' di banda controlla il numero di scaricamenti generati dal componente aggiuntivo gestiti simultaneamente."}, "weh_prefs_description_downloadRetries": {"message": "Numero dei tentativi di scaricamento"}, "weh_prefs_description_downloadRetryDelay": {"message": "<PERSON><PERSON> tra ulteriori tentativi di scaricamento (ms)"}, "weh_prefs_description_downloadStreamControlledMax": {"message": "Controlla il numero di flussi scaricati per ogni singolo elemento"}, "weh_prefs_description_fileDialogType": {"message": "Come sono visualizzati i file di dialogo"}, "weh_prefs_description_galleryNaming": {"message": "Come denominare i file della galleria da scaricare"}, "weh_prefs_description_hitsGotoTab": {"message": "Visualizza un collegamento nella descrizione per passare alla scheda del video"}, "weh_prefs_description_hlsDownloadAsM2ts": {"message": "Scarica i flussi HLS come M2TS"}, "weh_prefs_description_hlsEnabled": {"message": "Flusso HLS a blocchi attivo"}, "weh_prefs_description_hlsEndTimeout": {"message": "<PERSON><PERSON> in secondi per interrompere l'attesa di nuovi blocchi HLS"}, "weh_prefs_description_hlsRememberPrevLiveChunks": {"message": "Ricorda i precedenti blocchi HLS del flusso in diretta"}, "weh_prefs_description_iconActivation": {"message": "Quando attivare l'icona nella barra strumenti"}, "weh_prefs_description_iconBadge": {"message": "Cosa visualizza l'indicatore nell'icona nella barra strumenti"}, "weh_prefs_description_ignoreProtectedVariants": {"message": "Non visualizzare le varianti protette."}, "weh_prefs_description_lastDownloadDirectory": {"message": "Usata solo quando scarichi con l'app integrativa"}, "weh_prefs_description_mediaExtensions": {"message": "Estensioni da considerare multimediali"}, "weh_prefs_description_medialinkAutoDetect": {"message": "Esegui a ogni caricamento della pagina (potrebbe influire sulle prestazioni)"}, "weh_prefs_description_medialinkExtensions": {"message": "Estensioni di file da considerare per l'acquisizione di gallerie"}, "weh_prefs_description_medialinkMaxHits": {"message": "Limita il numero di voci rilevate come galleria"}, "weh_prefs_description_medialinkMinFilesPerGroup": {"message": "Numero minimo di voci per definire una galleria"}, "weh_prefs_description_medialinkMinImgSize": {"message": "Dimensione minima dell'immagine per essere inserita nella galleria"}, "weh_prefs_description_medialinkScanImages": {"message": "<PERSON><PERSON><PERSON> le immagini all'interno della pagina"}, "weh_prefs_description_medialinkScanLinks": {"message": "<PERSON><PERSON><PERSON> nella pagina i contenuti multimediali collegati direttamente"}, "weh_prefs_description_mediaweightMinSize": {"message": "Ignora connessioni al di sotto di questa dimensione."}, "weh_prefs_description_mediaweightThreshold": {"message": "Forza la rilevazione dei file multimediali al di sopra di questa dimensione."}, "weh_prefs_description_monitorNetworkRequests": {"message": "Scarica usando le stesse intestazioni della richiesta originale"}, "weh_prefs_description_mpegtsHideTs": {"message": "Non visualizzare le voci .ts nell'elenco scaricamenti"}, "weh_prefs_description_networkFilterOut": {"message": "Espressione regolare per ignorare alcuni indirizzi dei file multimediali."}, "weh_prefs_description_networkProbe": {"message": "Scansiona il traffico di rete per rilevare gli accessi"}, "weh_prefs_description_noPrivateNotification": {"message": "Nessuna notifica per connessioni private"}, "weh_prefs_description_notifyReady": {"message": "Avvisa a operazione completata"}, "weh_prefs_description_orphanExpiration": {"message": "<PERSON><PERSON> in secondi prima di rimuovere le connessioni orfane."}, "weh_prefs_description_qualitiesMaxVariants": {"message": "Numero massimo di varianti visualizzate per lo stesso video."}, "weh_prefs_description_rememberLastDir": {"message": "Usa come percorso predefinito l'ultima cartella usata"}, "weh_prefs_description_smartnamerFnameMaxlen": {"message": "I file generati non supereranno questa dimensione."}, "weh_prefs_description_smartnamerFnameSpaces": {"message": "Come gestire gli spazi nei nomi dei video"}, "weh_prefs_description_tbsnEnabled": {"message": "Consenti di rilevare e scaricare i video di Facebook"}, "weh_prefs_description_titleMode": {"message": "Come saranno visualizzati i titoli dei video quando sono troppo lunghi"}, "weh_prefs_description_toolsMenuEnabled": {"message": "Accedi ai comandi dal menu Strumenti"}, "weh_prefs_description_use_native_filepicker": {"message": "Usa il selettore file del sistema operativo"}, "weh_prefs_fileDialogType_option_panel": {"message": "Finestra"}, "weh_prefs_fileDialogType_option_tab": {"message": "Scheda"}, "weh_prefs_galleryNaming_option_index_url": {"message": "Indice - Indir<PERSON><PERSON>"}, "weh_prefs_galleryNaming_option_type_index": {"message": "Tipo - Indice"}, "weh_prefs_galleryNaming_option_url": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "weh_prefs_iconActivation_option_anytab": {"message": "Connessione da qualsiasi scheda"}, "weh_prefs_iconActivation_option_currenttab": {"message": "Connessione dalla scheda corrente"}, "weh_prefs_iconBadge_option_activetab": {"message": "File multimediale dalla scheda attiva"}, "weh_prefs_iconBadge_option_anytab": {"message": "File multimediale da qualsiasi scheda"}, "weh_prefs_iconBadge_option_mixed": {"message": "<PERSON><PERSON>"}, "weh_prefs_iconBadge_option_none": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_iconBadge_option_pinned": {"message": "<PERSON><PERSON><PERSON><PERSON> bloccate"}, "weh_prefs_iconBadge_option_tasks": {"message": "Processi in esecuzione"}, "weh_prefs_label_adpHide": {"message": "Nascondi le varianti ADP"}, "weh_prefs_label_alertDialogType": {"message": "Finestra di avviso"}, "weh_prefs_label_autoPin": {"message": "Blocca automaticamente"}, "weh_prefs_label_avplayEnabled": {"message": "Riproduttore abilitato"}, "weh_prefs_label_blacklistEnabled": {"message": "Abilita el<PERSON> b<PERSON>i"}, "weh_prefs_label_bulkEnabled": {"message": "Seleziona tutto abilitato"}, "weh_prefs_label_checkCoappOnStartup": {"message": "Controlla l'Applicazione Integrativa all'avvio"}, "weh_prefs_label_chunkedCoappDataRequests": {"message": "Dati a blocchi richiesti dall'Applicazione Integrativa"}, "weh_prefs_label_chunkedCoappManifestsRequests": {"message": "Blocchi in chiaro richiesti dall'Applicazione Integrativa"}, "weh_prefs_label_chunksConcurrentDownloads": {"message": "Blocchi scaricati simultaneamente"}, "weh_prefs_label_chunksEnabled": {"message": "Flusso diviso in blocchi"}, "weh_prefs_label_chunksPrefetchCount": {"message": "Numero di blocchi precaricati"}, "weh_prefs_label_coappDownloads": {"message": "Processore dello scaricamento"}, "weh_prefs_label_coappIdleExit": {"message": "Dopo quanto tempo di inattività L'Applicazione Integrativa si chiude"}, "weh_prefs_label_coappRestartDelay": {"message": "Ritardo per il riavvio dell'Applicazione Integrativa"}, "weh_prefs_label_coappUseProxy": {"message": "Proxy per la App Integrativa"}, "weh_prefs_label_contentRedirectEnabled": {"message": "Abilita il reindirizzamento del contenuto"}, "weh_prefs_label_contextMenuEnabled": {"message": "Menu Contestuale"}, "weh_prefs_label_convertControlledMax": {"message": "Operazioni simultanee di conversione"}, "weh_prefs_label_converterAggregTuneH264": {"message": "Ottimizzazione H264"}, "weh_prefs_label_converterKeepTmpFiles": {"message": "Mantieni i file temporanei"}, "weh_prefs_label_converterThreads": {"message": "Processi di conversione"}, "weh_prefs_label_dashEnabled": {"message": "DASH attivato"}, "weh_prefs_label_dashHideM4s": {"message": "Nascondi .m4s"}, "weh_prefs_label_dashOnAdp": {"message": "Flussi DASH"}, "weh_prefs_label_dialogAutoClose": {"message": "Chiusura automatica delle finestre di dialogo"}, "weh_prefs_label_downloadControlledMax": {"message": "Numero massimo di scaricamenti simultanei"}, "weh_prefs_label_downloadRetries": {"message": "Numero dei tentativi di scaricamento"}, "weh_prefs_label_downloadRetryDelay": {"message": "<PERSON><PERSON> tra ulteriori tentativi di scaricamento"}, "weh_prefs_label_downloadStreamControlledMax": {"message": "Numero massimo di flussi simultanei scaricati"}, "weh_prefs_label_fileDialogType": {"message": "File di dialogo"}, "weh_prefs_label_galleryNaming": {"message": "Nome della Galleria"}, "weh_prefs_label_hitsGotoTab": {"message": "Visualizza il puntatore Vai alla scheda"}, "weh_prefs_label_hlsDownloadAsM2ts": {"message": "HLS come M2TS"}, "weh_prefs_label_hlsEnabled": {"message": "HLS attivato"}, "weh_prefs_label_hlsEndTimeout": {"message": "HLS fine pausa"}, "weh_prefs_label_hlsRememberPrevLiveChunks": {"message": "Cronologia HLS in diretta"}, "weh_prefs_label_iconActivation": {"message": "Attivazione icona"}, "weh_prefs_label_iconBadge": {"message": "Indicatore sull'icona nella barra strumenti"}, "weh_prefs_label_ignoreProtectedVariants": {"message": "Ignora varianti protette"}, "weh_prefs_label_lastDownloadDirectory": {"message": "Cartella predefinita di scaricamento"}, "weh_prefs_label_mediaExtensions": {"message": "<PERSON><PERSON>va estensioni"}, "weh_prefs_label_medialinkAutoDetect": {"message": "Rileva automaticamente una galleria"}, "weh_prefs_label_medialinkExtensions": {"message": "Estensioni dei collegamenti multimediali"}, "weh_prefs_label_medialinkMaxHits": {"message": "Numero massimo di voci"}, "weh_prefs_label_medialinkMinFilesPerGroup": {"message": "Numero minimo di voci"}, "weh_prefs_label_medialinkMinImgSize": {"message": "Dimensione minima immagine"}, "weh_prefs_label_medialinkScanImages": {"message": "<PERSON><PERSON><PERSON> immagini incorporate"}, "weh_prefs_label_medialinkScanLinks": {"message": "Rileva collegamenti a file multimediali"}, "weh_prefs_label_mediaweightMinSize": {"message": "Dimensione minima"}, "weh_prefs_label_mediaweightThreshold": {"message": "Limite della dimensione"}, "weh_prefs_label_monitorNetworkRequests": {"message": "<PERSON>di intestazioni"}, "weh_prefs_label_mpegtsHideTs": {"message": "Nascondi .ts"}, "weh_prefs_label_networkFilterOut": {"message": "Filtro di rete disattivato"}, "weh_prefs_label_networkProbe": {"message": "Esplora rete"}, "weh_prefs_label_noPrivateNotification": {"message": "Nessuna notifica per connessioni private"}, "weh_prefs_label_notifyReady": {"message": "Notifica"}, "weh_prefs_label_orphanExpiration": {"message": "Tempo di scadenza dei collegamenti orfani"}, "weh_prefs_label_qualitiesMaxVariants": {"message": "Numero massimo di varianti"}, "weh_prefs_label_rememberLastDir": {"message": "Ricorda l'ultima cartella"}, "weh_prefs_label_smartnamerFnameMaxlen": {"message": "Lunghezza massima del nome file"}, "weh_prefs_label_smartnamerFnameSpaces": {"message": "Come gestire gli spazi nei nomi dei video"}, "weh_prefs_label_tbsnEnabled": {"message": "Supporto Facebook"}, "weh_prefs_label_tbvwsExtractionMethod": {"message": "Metodo di estrazione"}, "weh_prefs_label_titleMode": {"message": "Come saranno visualizzati i titoli dei video quando sono troppo lunghi"}, "weh_prefs_label_toolsMenuEnabled": {"message": "<PERSON><PERSON>"}, "weh_prefs_label_use_native_filepicker": {"message": "Usa selettore file nativo"}, "weh_prefs_smartnamerFnameSpaces_option_hyphen": {"message": "Sostituisci spazi con trattini"}, "weh_prefs_smartnamerFnameSpaces_option_keep": {"message": "<PERSON><PERSON><PERSON> gli spazi"}, "weh_prefs_smartnamerFnameSpaces_option_remove": {"message": "<PERSON><PERSON><PERSON><PERSON> gli spazi"}, "weh_prefs_smartnamerFnameSpaces_option_underscore": {"message": "Sostituisci gli spazi con il trattino basso"}, "weh_prefs_titleMode_option_left": {"message": "Puntini di sospensione a sinistra"}, "weh_prefs_titleMode_option_multiline": {"message": "Su più righe"}, "weh_prefs_titleMode_option_right": {"message": "Puntini di sospensione a destra"}, "yes": {"message": "Sì"}, "you_downloaded_n_videos": {"message": "Hai appena scaricato con successo il file n° $1 con Video DownloadHelper!"}}