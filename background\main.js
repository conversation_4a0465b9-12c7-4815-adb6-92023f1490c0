"use strict";(()=>{var Wh=Object.create;var En=Object.defineProperty;var Xh=Object.getOwnPropertyDescriptor;var Gh=Object.getOwnPropertyNames;var Qh=Object.getPrototypeOf,zh=Object.prototype.hasOwnProperty;var N=(e,t)=>()=>(e&&(t=e(e=0)),t);var y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),ne=(e,t)=>{for(var r in t)En(e,r,{get:t[r],enumerable:!0})},Iu=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Gh(t))!zh.call(e,n)&&n!==r&&En(e,n,{get:()=>t[n],enumerable:!(i=Xh(t,n))||i.enumerable});return e};var yt=(e,t,r)=>(r=e!=null?Wh(Qh(e)):{},Iu(t||!e||!e.__esModule?En(r,"default",{value:e,enumerable:!0}):r,e)),P=e=>Iu(En({},"__esModule",{value:!0}),e);var Nu=y(()=>{"use strict"});var Ft=y((wa,ku)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof wa<"u")t(ku);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:wa,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=i=>{let n={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(n).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class o extends WeakMap{constructor(_,S=void 0){super(S),this.createItem=_}get(_){return this.has(_)||this.set(_,this.createItem(_)),super.get(_)}}let s=w=>w&&typeof w=="object"&&typeof w.then=="function",a=(w,_)=>(...S)=>{i.runtime.lastError?w.reject(new Error(i.runtime.lastError.message)):_.singleCallbackArg||S.length<=1&&_.singleCallbackArg!==!1?w.resolve(S[0]):w.resolve(S)},l=w=>w==1?"argument":"arguments",u=(w,_)=>function(M,...I){if(I.length<_.minArgs)throw new Error(`Expected at least ${_.minArgs} ${l(_.minArgs)} for ${w}(), got ${I.length}`);if(I.length>_.maxArgs)throw new Error(`Expected at most ${_.maxArgs} ${l(_.maxArgs)} for ${w}(), got ${I.length}`);return new Promise((U,W)=>{if(_.fallbackToNoCallback)try{M[w](...I,a({resolve:U,reject:W},_))}catch(q){console.warn(`${w} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,q),M[w](...I),_.fallbackToNoCallback=!1,_.noCallback=!0,U()}else _.noCallback?(M[w](...I),U()):M[w](...I,a({resolve:U,reject:W},_))})},d=(w,_,S)=>new Proxy(_,{apply(M,I,U){return S.call(I,w,...U)}}),c=Function.call.bind(Object.prototype.hasOwnProperty),m=(w,_={},S={})=>{let M=Object.create(null),I={has(W,q){return q in w||q in M},get(W,q,$){if(q in M)return M[q];if(!(q in w))return;let O=w[q];if(typeof O=="function")if(typeof _[q]=="function")O=d(w,w[q],_[q]);else if(c(S,q)){let k=u(q,S[q]);O=d(w,w[q],k)}else O=O.bind(w);else if(typeof O=="object"&&O!==null&&(c(_,q)||c(S,q)))O=m(O,_[q],S[q]);else if(c(S,"*"))O=m(O,_[q],S["*"]);else return Object.defineProperty(M,q,{configurable:!0,enumerable:!0,get(){return w[q]},set(k){w[q]=k}}),O;return M[q]=O,O},set(W,q,$,O){return q in M?M[q]=$:w[q]=$,!0},defineProperty(W,q,$){return Reflect.defineProperty(M,q,$)},deleteProperty(W,q){return Reflect.deleteProperty(M,q)}},U=Object.create(w);return new Proxy(U,I)},T=w=>({addListener(_,S,...M){_.addListener(w.get(S),...M)},hasListener(_,S){return _.hasListener(w.get(S))},removeListener(_,S){_.removeListener(w.get(S))}}),f=new o(w=>typeof w!="function"?w:function(S){let M=m(S,{},{getContent:{minArgs:0,maxArgs:0}});w(M)}),A=new o(w=>typeof w!="function"?w:function(S,M,I){let U=!1,W,q=new Promise(z=>{W=function(J){U=!0,z(J)}}),$;try{$=w(S,M,W)}catch(z){$=Promise.reject(z)}let O=$!==!0&&s($);if($!==!0&&!O&&!U)return!1;let k=z=>{z.then(J=>{I(J)},J=>{let Oe;J&&(J instanceof Error||typeof J.message=="string")?Oe=J.message:Oe="An unexpected error occurred",I({__mozWebExtensionPolyfillReject__:!0,message:Oe})}).catch(J=>{console.error("Failed to send onMessage rejected reply",J)})};return k(O?$:q),!0}),h=({reject:w,resolve:_},S)=>{i.runtime.lastError?i.runtime.lastError.message===t?_():w(new Error(i.runtime.lastError.message)):S&&S.__mozWebExtensionPolyfillReject__?w(new Error(S.message)):_(S)},p=(w,_,S,...M)=>{if(M.length<_.minArgs)throw new Error(`Expected at least ${_.minArgs} ${l(_.minArgs)} for ${w}(), got ${M.length}`);if(M.length>_.maxArgs)throw new Error(`Expected at most ${_.maxArgs} ${l(_.maxArgs)} for ${w}(), got ${M.length}`);return new Promise((I,U)=>{let W=h.bind(null,{resolve:I,reject:U});M.push(W),S.sendMessage(...M)})},g={devtools:{network:{onRequestFinished:T(f)}},runtime:{onMessage:T(A),onMessageExternal:T(A),sendMessage:p.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:p.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},x={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return n.privacy={network:{"*":x},services:{"*":x},websites:{"*":x}},m(i,g,n)};e.exports=r(chrome)}else e.exports=globalThis.browser})});var Ur=y((Hx,vt)=>{"use strict";vt.exports.browser=Ft();var Ti;typeof browser>"u"&&typeof chrome<"u"&&chrome.runtime?/\bOPR\//.test(navigator.userAgent)?Ti="opera":Ti="chrome":/\bEdge\//.test(navigator.userAgent)?Ti="edge":Ti="firefox",vt.exports.browserType=Ti,typeof vt.exports.browser.action>"u"&&(vt.exports.browser.action=vt.exports.browser.browserAction),vt.exports.isBrowser=(...e)=>{for(let t=0;t<e.length;t++)if(e[t]==vt.exports.browserType)return!0;return!1},vt.exports.error=e=>{console.groupCollapsed(e.message),e.stack&&console.error(e.stack),console.groupEnd()}});var Lt=y((Fx,Cu)=>{"use strict";var Aa=class{constructor(){this.replyId=0,this.replies={},this.listeners={},this.hook=this.nullHook,this.debugLevel=0,this.useTarget=!1,this.logger=console,this.posts={}}setPost(t,r){typeof t=="string"?this.posts[t]=r:this.post=t}setUseTarget(t){this.useTarget=t}setDebugLevel(t){this.debugLevel=t}setHook(t){let r=this,i=Date.now();function n(){return typeof window<"u"&&typeof window.performance<"u"?window.performance.now():Date.now()-i}t?this.hook=o=>{o.timestamp=n();try{t(o)}catch(s){r.logger.warn("Hoor error",s)}}:this.hook=this.nullHook}nullHook(){}call(){let t=this,r,i,n,o,s=Array.prototype.slice.call(arguments);return typeof s[0]=="function"&&(r=s.shift()),t.useTarget?[i,n,...o]=s:[n,...o]=s,new Promise(function(a,l){let u=++t.replyId;t.debugLevel>=2&&t.logger.info("rpc #"+u,"call =>",n,o),t.hook({type:"call",callee:i,rid:u,method:n,args:o}),t.replies[u]={resolve:a,reject:l,peer:i};let d=r||t.useTarget&&t.posts[i]||t.post;t.useTarget?d(i,{type:"weh#rpc",_request:u,_method:n,_args:[...o]}):d({type:"weh#rpc",_request:u,_method:n,_args:[...o]})})}receive(t,r,i){let n=this;if(t._request)Promise.resolve().then(()=>{let o=n.listeners[t._method];if(typeof o=="function")return n.debugLevel>=2&&n.logger.info("rpc #"+t._request,"serve <= ",t._method,t._args),n.hook({type:"call",caller:i,rid:t._request,method:t._method,args:t._args}),Promise.resolve(o.apply(null,t._args)).then(s=>(n.hook({type:"reply",caller:i,rid:t._request,result:s}),s)).catch(s=>{throw n.hook({type:"reply",caller:i,rid:t._request,error:s.message}),s});throw new Error("Method "+t._method+" is not a function")}).then(o=>{n.debugLevel>=2&&n.logger.info("rpc #"+t._request,"serve => ",o),r({type:"weh#rpc",_reply:t._request,_result:o})}).catch(o=>{n.debugLevel>=1&&n.logger.info("rpc #"+t._request,"serve => !",o.message),r({type:"weh#rpc",_reply:t._request,_error:o.message})});else if(t._reply){let o=n.replies[t._reply];delete n.replies[t._reply],o?t._error?(n.debugLevel>=1&&n.logger.info("rpc #"+t._reply,"call <= !",t._error),n.hook({type:"reply",callee:o.peer,rid:t._reply,error:t._error}),o.reject(new Error(t._error))):(n.debugLevel>=2&&n.logger.info("rpc #"+t._reply,"call <= ",t._result),n.hook({type:"reply",callee:o.peer,rid:t._reply,result:t._result}),o.resolve(t._result)):n.logger.error("Missing reply handler")}}listen(t){Object.assign(this.listeners,t)}};Cu.exports=new Aa});var Ta=y((Lx,Hu)=>{"use strict";var{browser:xa}=Ur(),Bu={},qu=new RegExp("\\$[a-zA-Z]*([0-9]+)\\$","g"),Vu=!1,$h=xa.storage.local.get("wehI18nCustom").then(e=>{Vu=!0;let t=e.wehI18nCustom;t&&Object.assign(Bu,t)});function Jh(e,t){if(Vu||console.warn("Using `weh._` before custom strings were loaded:",e),/-/.test(e)){let i=e.replace(/-/g,"_");console.warn("Wrong i18n message name. Should it be",i,"instead of",e,"?"),e=i}let r=Bu[e];if(t&&!Array.isArray(t)&&(t=[t]),r&&r.message.length>0)return(r.message||"").replace(qu,i=>{let n=qu.exec(i);return n&&t&&t[parseInt(n[1])-1]||"??"});try{return t?xa.i18n.getMessage(e,t):xa.i18n.getMessage(e)}catch{return""}}Hu.exports={getMessage:Jh,custom_strings_ready:$h}});var Uu=y((Ux,Lu)=>{"use strict";var Ei=Ur(),Kh=Lt(),Y=Ei.browser,Si={},Sn={};function Fu(e,t){let r=!1;return new Promise(function(i,n){return Y.tabs.query({}).then(function(o){o.forEach(function(s){s.url===e&&(Y.tabs.update(s.id,{active:!0}),Y.windows?.update(s.windowId,{focused:!0}),r=!0)}),i(r)})})}function Yh(e,t){return new Promise((r,i)=>{let n=Y.runtime.getURL(t.url+"?panel="+e);Fu(n).then(function(o){if(!o)return Y.tabs.create({url:n}).then(function(s){Ei.__declareAppTab(e,{tab:s.id,initData:t.initData}),Si[e]={type:"tab",tabId:s.id},Sn[s.id]=e})}).then(r).catch(i)})}function Zh(e,t){return new Promise((r,i)=>{let n=Y.runtime.getURL(t.url+"?panel="+e);Y.windows.getCurrent().then(o=>{let s=t.width||500,a=t.height||400,l={url:n,width:s,height:a,type:"popup",left:Math.round((o.width-s)/2+o.left),top:Math.round((o.height-a)/2+o.top)};return Ei.isBrowser("chrome","opera")&&(l.focused=!0),Y.windows.create(l).then(u=>(Si[e]={type:"window",windowId:u.id},Promise.all([u,Y.windows.update(u.id,{focused:!0})]))).then(([u])=>{Promise.resolve().then(()=>{if(!(t.initData&&t.initData.autoResize))return Y.windows.update(u.id,{height:u.height+1}).then(()=>Y.windows.update(u.id,{height:u.height-1}))}).then(()=>{let m=new Promise((f,A)=>{let h;function p(g){g.windowId==u.id&&(clearTimeout(h),Y.tabs.onCreated.removeListener(p),f(g))}h=setTimeout(()=>{Y.tabs.onCreated.removeListener(p),A(new Error("Tab did not open"))},5e3),Y.tabs.onCreated.addListener(p)}),T=Y.tabs.query({windowId:u.id}).then(f=>new Promise((A,h)=>{f.length>0&&A(f[0])}));return Promise.race([m,T])}).then(m=>m.status=="loading"?new Promise((T,f)=>{let A;function h(p,g,x){p==m.id&&x.status=="complete"&&(clearTimeout(A),Y.tabs.onUpdated.removeListener(h),T(x))}A=setTimeout(()=>{Y.tabs.onUpdated.removeListener(h),f(new Error("Tab did not complete"))},6e4),Y.tabs.onUpdated.addListener(h)}):m).then(m=>{Ei.__declareAppTab(e,{tab:m.id,initData:t.initData}),Sn[m.id]=e}).then(r).catch(i);function d(m){m!=u.id&&t.autoClose&&Y.windows.getCurrent().then(T=>{T.id!=u.id&&Y.windows.remove(u.id).then(()=>{},()=>{})})}function c(m){m==u.id&&(Y.windows.onFocusChanged?.removeListener(d),Y.windows.onFocusChanged?.removeListener(c))}Y.windows.onFocusChanged?.addListener(d),Y.windows.onRemoved?.addListener(c)}).catch(i)}).catch(i)})}function e_(e,t){return new Promise((r,i)=>{let n=Y.runtime.getURL(t.url+"?panel="+e);Fu(n).then(o=>{if(!o)return Zh(e,t)}).then(r).catch(i)})}function t_(e,t){switch(t.type){case"panel":return e_(e,t);case"tab":default:return Yh(e,t)}}Y.tabs.onRemoved.addListener(e=>{Ei.__closeByTab(e);let t=Sn[e];t&&(delete Sn[e],delete Si[t])});function r_(e){let t=Si[e];t&&t.type=="tab"?Y.tabs.remove(t.tabId):t&&t.type=="window"?Y.windows.remove(t.windowId):Kh.call(e,"close")}function i_(e){return!!Si[e]}Lu.exports={open:t_,close:r_,isOpen:i_}});var Da=y((jx,Xu)=>{"use strict";var Ea=Ta().getMessage,ju={};function Wu(){this.$specs={},this.$values=null,this.$values||(this.$values={}),this.$listeners={}}Wu.prototype={notify:function(e,t,r,i){let n=this,o=e.split("."),s=[];for(let a=o.length;a>=0;a--)s.push(o.slice(0,a).join("."));s.forEach(function(a){let l=n.$listeners[a];l&&l.forEach(function(u){if(u.specs==i)if(u.pack)u.pack[e]=t,typeof u.old[e]>"u"&&(u.old[e]=r),u.timer&&clearTimeout(u.timer),u.timer=setTimeout(function(){delete u.timer;let d=u.pack,c=u.old;u.pack={},u.old={};try{u.callback(d,c)}catch{}},0);else try{u.callback(e,t,r)}catch{}})})},forceNotify:function(e){typeof e>"u"&&(e=!1);let t=this;Object.keys(t.$specs).forEach(r=>{t.notify(r,t.$values[r],t.$values[r],e)})},declare:function(e){let t=this;Array.isArray(e)||(e=Object.keys(e).map(function(r){let i=e[r];return i.name=r,i})),e.forEach(function(r){if(ju[r.name])throw new Error("Forbidden prefs key "+r.name);let i;r.hidden?(r.label=r.name,r.description=""):(i=r.name.replace(/[^0-9a-zA-Z_]/g,"_"),r.label=r.label||Ea("weh_prefs_label_"+i)||r.name,r.description=r.description||Ea("weh_prefs_description_"+i)||""),r.type=="choice"&&(r.choices=(r.choices||[]).map(function(s){if(typeof s=="object")return s;if(r.hidden)return{value:s,name:s};{let a=s.replace(/[^0-9a-zA-Z_]/g,"_");return{value:s,name:Ea("weh_prefs_"+i+"_option_"+a)||s}}}));let n=null;t.$specs[r.name]||function(s){typeof t[r.name]<"u"&&(n=t[r.name]),Object.defineProperty(t,s,{set:function(a){let l=t.$values[s];l!==a&&(t.$values[s]=a,t.notify(s,a,l,!1))},get:function(){return t.$values[s]!==void 0?t.$values[s]:t.$specs[s]&&t.$specs[s].defaultValue||void 0}})}(r.name);let o=t.$specs[r.name];t.$specs[r.name]=r,n!==null?t.$values[r.name]=n:typeof t.$values[r.name]>"u"&&(t.$values[r.name]=r.defaultValue),t.notify(r.name,r,o,!0)})},on:function(){let e="",t={},r=0;typeof arguments[r]=="string"&&(e=arguments[r++]),typeof arguments[r]=="object"&&(t=arguments[r++]);let i=arguments[r],n=!!t.pack;this.$listeners[e]||(this.$listeners[e]=[]);let o={callback:i,specs:!!t.specs};n&&(o.pack={},o.old={}),this.$listeners[e].push(o)},off:function(){let e="",t=0;typeof arguments[t]=="string"&&(e=arguments[t++]);let r=arguments[t],i=this.$listeners[e];if(i)for(let n=i.length-1;n>=0;n--)(!r||i[n]==r)&&i.splice(n,1)},getAll:function(){return Object.assign({},this.$values)},getSpecs:function(){return Object.assign({},this.$specs)},assign:function(e){for(let t in e)e.hasOwnProperty(t)&&(this[t]=e[t])},isValid:function(e,t){let r=this.$specs[e];if(r){switch(r.type){case"string":if(r.regexp&&!new RegExp(r.regexp).test(t))return!1;break;case"integer":if(!/^-?[0-9]+$/.test(t)||isNaN(parseInt(t)))return!1;case"float":if(r.type=="float"&&(!/^-?[0-9]+(\.[0-9]+)?|(\.[0-9]+)$/.test(t)||isNaN(parseFloat(t)))||typeof r.minimum<"u"&&t<r.minimum||typeof r.maximum<"u"&&t>r.maximum)return!1;break;case"choice":{let i=!1;if((r.choices||[]).forEach(n=>{t==n.value&&(i=!0)}),!i)return!1}break}return!0}},reducer:function(e={},t){switch(t.type){case"weh.SET_PREFS":e=Object.assign({},e,t.payload);break}return e},reduxDispatch(e){this.on("",{pack:!0},t=>{e.dispatch({type:"weh.SET_PREFS",payload:t})})}};var Sa=new Wu;for(let e in Sa)Sa.hasOwnProperty(e)&&(ju[e]=!0);Xu.exports=Sa});var Qu=y((Wx,Gu)=>{"use strict";Gu.exports=[{name:"networkProbe",type:"boolean",defaultValue:!0},{name:"titleMode",type:"choice",defaultValue:"right",choices:["right","left","multiline"]},{name:"iconActivation",type:"choice",defaultValue:"currenttab",choices:["currenttab","anytab"]},{name:"iconBadge",type:"choice",defaultValue:"tasks",choices:["none","tasks","activetab","anytab","pinned","mixed"]},{name:"hitsGotoTab",type:"boolean",defaultValue:!0},{name:"default-action-0",type:"string",defaultValue:"quickdownload",hidden:!0},{name:"default-action-1",type:"string",defaultValue:"openlocalfile",hidden:!0},{name:"default-action-2",type:"string",defaultValue:"abort",hidden:!0},{name:"smartnamerFnameSpaces",type:"choice",defaultValue:"keep",choices:["keep","remove","hyphen","underscore"]},{name:"smartnamerFnameMaxlen",type:"integer",defaultValue:64,minimum:12,maximum:256},{name:"downloadControlledMax",type:"integer",defaultValue:6,minimum:0},{name:"downloadStreamControlledMax",type:"integer",defaultValue:6,minimum:0},{name:"autoPin",type:"boolean",defaultValue:!1},{name:"mediaExtensions",type:"string",defaultValue:"flv|ram|mpg|mpeg|avi|rm|wmv|mov|asf|mp3|rar|movie|divx|rbs|mp4|mpeg4"},{name:"dashHideM4s",type:"boolean",defaultValue:!0},{name:"mpegtsHideTs",type:"boolean",defaultValue:!0},{name:"orphanExpiration",type:"integer",defaultValue:60,minimum:0},{name:"chunksEnabled",type:"boolean",defaultValue:!0},{name:"hlsEnabled",type:"boolean",defaultValue:!0},{name:"dashEnabled",type:"boolean",defaultValue:!0},{name:"dashOnAdp",type:"choice",defaultValue:"audio_video",choices:["audio","video","audio_video"]},{name:"hlsDownloadAsM2ts",type:"boolean",defaultValue:!1},{name:"networkFilterOut",type:"string",defaultValue:"/frag\\\\([0-9]+\\\\)/|[&\\\\?]range=[0-9]+-[0-9]+|/silverlight/"},{name:"mediaweightThreshold",type:"integer",defaultValue:2097152},{name:"mediaweightMinSize",type:"integer",defaultValue:8192},{name:"tbvwsEnabled",type:"boolean",defaultValue:!0,hidden:!0},{name:"converterThreads",type:"string",defaultValue:"auto"},{name:"converterAggregTuneH264",type:"boolean",defaultValue:!1},{name:"notifyReady",type:"boolean",defaultValue:!0},{name:"noPrivateNotification",type:"boolean",defaultValue:!0},{name:"avplayEnabled",type:"boolean",defaultValue:!0},{name:"blacklistEnabled",type:"boolean",defaultValue:!0},{name:"chunksConcurrentDownloads",type:"integer",defaultValue:4},{name:"chunksPrefetchCount",type:"integer",defaultValue:4},{name:"downloadRetries",type:"integer",defaultValue:3},{name:"downloadRetryDelay",type:"integer",defaultValue:1e3},{name:"mpegtsSaveRaw",type:"boolean",defaultValue:!1,hidden:!0},{name:"mpegtsSaveRawStreams",type:"boolean",defaultValue:!1,hidden:!0},{name:"mpegtsEndsOnSeenChunk",type:"boolean",defaultValue:!0,hidden:!0},{name:"converterKeepTmpFiles",type:"boolean",defaultValue:!1},{name:"backgroundReduxLogger",type:"boolean",defaultValue:!1,hidden:!0},{name:"dlconvLastOutput",type:"string",defaultValue:"",hidden:!0},{name:"qrMessageNotAgain",type:"boolean",defaultValue:!1,hidden:!0},{name:"coappShellEnabled",type:"boolean",defaultValue:!1,hidden:!0},{name:"downloadCount",type:"integer",defaultValue:0,hidden:!0},{name:"donateNotAgainExpire",type:"integer",defaultValue:0,hidden:!0},{name:"popupHeightLeftOver",type:"integer",defaultValue:100,hidden:!0},{name:"coappDownloads",type:"choice",defaultValue:"ask",choices:["ask","coapp","browser"]},{name:"lastDownloadDirectory",type:"string",defaultValue:"dwhelper"},{name:"fileDialogType",type:"choice",defaultValue:"tab",choices:["tab","panel"]},{name:"alertDialogType",type:"choice",defaultValue:"tab",choices:["tab","panel"]},{name:"monitorNetworkRequests",type:"boolean",defaultValue:!0},{name:"chunkedCoappManifestsRequests",type:"boolean",defaultValue:!1},{name:"chunkedCoappDataRequests",type:"boolean",defaultValue:!0},{name:"coappRestartDelay",type:"integer",defaultValue:1e3},{name:"rememberLastDir",type:"boolean",defaultValue:!0},{name:"coappIdleExit",type:"integer",defaultValue:6e4},{name:"dialogAutoClose",type:"boolean",defaultValue:!1},{name:"convertControlledMax",type:"integer",defaultValue:1},{name:"checkCoappOnStartup",type:"boolean",defaultValue:!0},{name:"coappUseProxy",type:"boolean",defaultValue:!0},{name:"downloadCompleteDelay",type:"integer",defaultValue:1e3},{name:"contentRedirectEnabled",type:"boolean",defaultValue:!0},{name:"contextMenuEnabled",type:"boolean",defaultValue:!0},{name:"toolsMenuEnabled",type:"boolean",defaultValue:!1},{name:"medialinkExtensions",type:"string",defaultValue:"jpg|jpeg|gif|png|mpg|mpeg|avi|rm|wmv|mov|flv|mp3|mp4"},{name:"medialinkMaxHits",type:"integer",defaultValue:50},{name:"medialinkMinFilesPerGroup",type:"integer",defaultValue:6},{name:"medialinkMinImgSize",type:"integer",defaultValue:80},{name:"medialinkAutoDetect",type:"boolean",defaultValue:!1},{name:"medialinkScanImages",type:"boolean",defaultValue:!0},{name:"medialinkScanLinks",type:"boolean",defaultValue:!0},{name:"bulkEnabled",type:"boolean",defaultValue:!0},{name:"tbvwsGrabDelay",type:"integer",defaultValue:2e3},{name:"forcedCoappVersion",type:"string",regexp:"^$|^\\d+\\.\\d+\\.\\d+$",defaultValue:""},{name:"lastHlsDownload",type:"integer",defaultValue:0,hidden:!0},{name:"galleryNaming",type:"choice",choices:["type-index","url","index-url"],defaultValue:"type-index"},{name:"hlsRememberPrevLiveChunks",type:"boolean",defaultValue:!1},{name:"hlsEndTimeout",type:"integer",defaultValue:20},{name:"tbvwsExtractionMethod",type:"choice",choices:["page_android_ios_tvep","page_ios_android_tvep","android_ios_tvep_page","ios_android_tvep_page","page_tvep_android_ios","android_ios_tvep","page","android","tvep","ios"],defaultValue:"page_android_ios_tvep"},{name:"hitUpdateFloodProtect",type:"integer",defaultValue:100,hidden:!0},{name:"use_native_filepicker",type:"boolean",defaultValue:!1}]});var te=y((Xx,$u)=>{"use strict";var re=Ur(),Oa=re.browser,Ce={},Ue={};re.rpc=Lt(),re.rpc.setUseTarget(!0),re.rpc.setPost((e,t)=>{let r=Ce[e];r&&r.port&&r.port.postMessage(t)}),re.rpc.listen({appStarted:e=>{},appReady:e=>{},closePanel:e=>{re.ui.close(e)}}),Oa.runtime.onConnect.addListener(e=>{/^weh:(.*?):(.*)/.exec(e.name)&&(e.onMessage.addListener(t=>{if(typeof t._method<"u"&&(t._method==="appStarted"||t._method==="appReady")){let r=t._args[0]&&t._args[0].uiName||null,i=Ce[r]||{ready:!1};if(Ce[r]=i,Object.assign(i,t._args[0],{port:e}),t._method=="appReady"){i.ready=!0,i.initData&&setTimeout(()=>re.rpc.call(r,"wehInitData",i.initData));let n=Ue[r];n&&n.timer&&clearTimeout(n.timer)}e._weh_app=r}re.rpc.receive(t,e.postMessage.bind(e),e._weh_app)}),e.onDisconnect.addListener(()=>{let t=e._weh_app;if(t){delete Ce[t];let r=Ue[t];r&&(r.timer&&clearTimeout(r.timer),delete Ue[t],r.reject(new Error("Disconnected waiting for "+t)))}}))}),re.__declareAppTab=function(e,t){Ce[e]||(Ce[e]={}),Object.assign(Ce[e],t)},re.__closeByTab=function(e){Object.keys(Ce).forEach(t=>{if(Ce[t].tab===e){delete Ce[t];let r=Ue[t];r&&(r.timer&&clearTimeout(r.timer),delete Ue[t],r.reject(new Error("Disconnected waiting for "+t)))}})},re._=Ta().getMessage,re.ui=Uu(),re.openedContents=()=>Object.keys(Ce);function n_(e){let t=0,r;if(e.length===0)return t;for(let i=0;i<e.length;i++)r=e.charCodeAt(i),t=(t<<5)-t+r,t=t&t;return t}function o_(e){return JSON.stringify(Object.keys(e).sort().map(function(t){return{name:t,value:e[t]}}))}var zu=0;re.unsafe_prefs=Da(),re.prefs=Oa.storage.local.get("weh-prefs").then(e=>{let t=re.unsafe_prefs,r=e["weh-prefs"]||{};return t.assign(r),t.on("",{pack:!0},function(i,n){Object.assign(r,i);let o=o_(r),s=n_(o);s!=zu&&(zu=s,Oa.storage.local.set({"weh-prefs":r})),Object.keys(Ce).forEach(a=>{re.rpc.call(a,"setPrefs",i)})}),t.declare(Qu()),t}).catch(e=>{console.error("web-background error:",e)}),re.wait=(e,t={})=>{let r=Ue[e];return r&&(r.timer&&clearTimeout(r.timer),delete Ue[e],r.reject(new Error("Waiter for "+e+" overriden"))),new Promise((i,n)=>{Ue[e]={resolve:i,reject:n,timer:setTimeout(()=>{delete Ue[e],n(new Error("Waiter for "+e+" timed out"))},t.timeout||6e4)}})},re.rpc.listen({prefsGetAll:async()=>(await re.prefs).getAll(),prefsGetSpecs:async()=>(await re.prefs).getSpecs(),prefsSet:async e=>(await re.prefs).assign(e),trigger:(e,t)=>{let r=Ue[e];if(!r)throw new Error("No waiter for",e);r.timer&&(clearTimeout(r.timer),delete r.timer),delete Ue[e],r.resolve(t)}}),$u.exports=re});var ur=y((Gx,a_)=>{a_.exports={prod:!0,channel:"stable",buildDate:"2025-05-16",buildOptions:{linuxlic:!1,noyt:!0,target:"google",browser:"chrome"}}});var Zu=y((Qx,Yu)=>{"use strict";var s_=Ur(),Ju=Lt(),Ma=Da(),Dn=s_.browser,Di=null,Ku=null,Oi=!1;Dn.runtime.onMessageExternal&&(Dn.runtime.onMessageExternal.addListener(function(e,t,r){switch(e.type){case"weh#inspect-ping":Di=t.id,r({type:"weh#inspect-pong",version:1,manifest:Dn.runtime.getManifest()});break;case"weh#inspect":Di=t.id,Oi=e.inspected,Oi?Ju.setHook(i=>{Oi&&Di&&Dn.runtime.sendMessage(Di,{type:"weh#inspect-message",message:i}).catch(n=>{console.info("Error sending message",n),Oi=!1})}):Ju.setHook(null),r({type:"weh#inspect",version:1,inspected:Oi});break;case"weh#get-prefs":Di=t.id,r({type:"weh#prefs",prefs:Ma.getAll(),specs:Ma.getSpecs()});break;case"weh#set-pref":Ma[e.pref]=e.value,r(!0);break}}),Ku={send:()=>{console.info("TODO implement inspect.send")}}),Yu.exports=Ku});var td={};ne(td,{removeOriginAndReferrerSetterForUrl:()=>d_,setOriginAndReferrerSetterForUrl:()=>u_});async function u_(e,t,r){if(!(!t&&!r))if(ed){let i=[];t&&i.push({operation:"set",header:"origin",value:t}),r&&i.push({operation:"set",header:"referer",value:r});let n=l_++,o={id:n,priority:1,action:{type:"modifyHeaders",requestHeaders:i},condition:{urlFilter:e,resourceTypes:["xmlhttprequest"]}};Ra.set(e,[n]);try{await Mi.declarativeNetRequest.updateSessionRules({addRules:[o]})}catch{}}else{let i=n=>{let o=n.requestHeaders.filter(s=>s.name!="origin"&&s.name!="referer");return t&&o.push({name:"origin",value:t}),r&&o.push({name:"referer",value:r}),{requestHeaders:o}};Pa.set(e,i),Mi.webRequest.onBeforeSendHeaders.addListener(i,{urls:[e]},["blocking","requestHeaders"])}}async function d_(e){if(ed){let t=Ra.get(e);t&&(Ra.delete(e),await Mi.declarativeNetRequest.updateSessionRules({removeRuleIds:t}))}else{let t=Pa.get(e);t&&(Pa.delete(t),Mi.webRequest.onBeforeSendHeaders.removeListener(t))}}var Mi,Pa,Ra,l_,ed,rd=N(()=>{"use strict";({browser:Mi}=te()),Pa=new Map,Ra=new Map,l_=1,ed=Mi.runtime.getManifest().manifest_version>=3});var ge={};ne(ge,{Cache:()=>Ia,Concurrent:()=>x_,DetailsError:()=>Ca,VDHError:()=>On,arrayEquals:()=>h_,bufferToHex:()=>A_,executeScriptWithGlobal:()=>E_,fromByteArray:()=>b_,generateRandomString:()=>od,gotoOrOpenTab:()=>g_,gotoTab:()=>nd,hash:()=>id,hashHex:()=>m_,headerSubsSalt:()=>v_,isMinimumVersion:()=>T_,request:()=>w_,toByteArray:()=>__});function id(e){let t=0,r,i,n;if(e.length===0)return t;for(r=0,n=e.length;r<n;r++)i=e.charCodeAt(r),t=(t<<5)-t+i,t|=0;return t}function m_(e){return Math.abs(id(e)).toString(16)}function nd(e){return qe.tabs.query({url:e}).then(t=>t.length>0?(qe.tabs.update(t[0].id,{active:!0}),qe.windows.update(t[0].windowId,{focused:!0}),!0):!1)}function g_(e,t=null){let r=0;function i(){return qe.windows.getLastFocused({windowTypes:["normal"]}).then(n=>n.type!="normal"?++r<20?new Promise((o,s)=>{setTimeout(()=>i(),100)}):new Promise((o,s)=>{qe.windows.getAll({windowTypes:["normal"]}).then(a=>{if(a.every(l=>l.type=="normal"?(o(l.id),!1):!0))throw new Error("No normal window to open tab")})}):n.id).then(n=>{let o=null;if(n)return qe.tabs.query({active:!0,lastFocusedWindow:!0}).then(s=>(s.length>0&&(o=s[0].id),new Promise((a,l)=>{let u=null,d=(c,m,T)=>{c==u&&m.status==="complete"&&(qe.tabs.onUpdated.removeListener(d),a(T))};qe.tabs.onUpdated.addListener(d),qe.tabs.create({url:e,windowId:n}).then(c=>{c.status==="complete"?(qe.tabs.onUpdated.removeListener(d),a(c)):u=c.id})}).then(a=>{o&&t&&t(a.id,o)})))})}return nd(e).then(n=>n?Promise.resolve():i())}function h_(e,t){if(e.length!==t.length)return!1;for(let r=0,i=e.length;r<i;r++)if(e[r]!==t[r])return!1;return!0}function od(e){let t=new Uint8Array(e);crypto.getRandomValues(t);let r="";for(let i=0;i<t.length;i++)r+=("0"+t[i].toString(16)).slice(-2);return r.substring(0,e)}async function w_(e){let t="include";e.anonymous&&(t="omit");let r=e.url,i=e.method||"GET",n="",o=new Headers;if(e.headers){if(e.headers instanceof Array)for(let l of e.headers)o.append(l.name,l.value);else o=new Headers(e.headers);o.has("referer")&&(n=o.get("referer")),o.has("referrer")&&(n=o.get("referrer"));for(let l of y_)o.delete(l)}let s;e.contentJSON?s=JSON.stringify(e.contentJSON):e.content&&(s=e.content),await p_(r,o.get("origin"),n);let a;try{a=await fetch(r,{referrer:n,method:i,headers:o,body:s,credentials:t})}finally{await f_(r)}return a}function A_(e){let t=[],r=new DataView(e);for(let i=0;i<r.byteLength;i+=4){let o=r.getUint32(i).toString(16),s="00000000",a=(s+o).slice(-s.length);t.push(a)}return t.join("")}function x_(...e){let t=new Na(...e);return t.callFn().bind(t)}function T_(e="0.0.0",t){let r=e.split(".").map(n=>parseInt(n)),i=t.split(".").map(n=>parseInt(n));for(let n=0;n<r.length;n++){if(typeof i[n]>"u"||r[n]>i[n])return!0;if(r[n]<i[n])return!1}return!0}async function E_(e,t,r){let i=a=>a&&typeof a=="object",n=a=>JSON.parse(JSON.stringify(a));if(!i(t))throw new Error("global argument is not an object");t=n(t);let s={target:e,func:a=>{Object.assign(window,a)},args:[t]};await qe.scripting.executeScript(s),s={target:e,files:[r]},await qe.scripting.executeScript(s)}var c_,qe,p_,f_,__,b_,y_,v_,Ia,Na,ka,On,Ca,he=N(()=>{"use strict";c_=te(),qe=c_.browser,{setOriginAndReferrerSetterForUrl:p_,removeOriginAndReferrerSetterForUrl:f_}=(rd(),P(td));({toByteArray:__,fromByteArray:b_}=(()=>{let e,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=[];for(e=0;e<t.length;e++)r[e]=t[e];let i=[];for(e=0;e<t.length;++e)i[t.charCodeAt(e)]=e;i[45]=62,i[95]=63;let n=typeof Uint8Array<"u"?Uint8Array:Array;function o(c){let m=i[c.charCodeAt(0)];return m!==void 0?m:-1}function s(c){let m,T,f,A,h,p;if(c.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let g=c.length;h=c.charAt(g-2)==="="?2:c.charAt(g-1)==="="?1:0,p=new n(c.length*3/4-h),f=h>0?c.length-4:c.length;let x=0;function w(_){p[x++]=_}for(m=0,T=0;m<f;m+=4,T+=3)A=o(c.charAt(m))<<18|o(c.charAt(m+1))<<12|o(c.charAt(m+2))<<6|o(c.charAt(m+3)),w((A&16711680)>>>16>>>0),w((A&65280)>>>8>>>0),w((A&255)>>>0);return h===2?(A=o(c.charAt(m))<<2|o(c.charAt(m+1))>>>4>>>0,w(A&255)):h===1&&(A=o(c.charAt(m))<<10|o(c.charAt(m+1))<<4|o(c.charAt(m+2))>>>2>>>0,w(A>>>8>>>0&255),w(A&255)),p}function a(c){return r[c]}function l(c){return a(c>>>18>>>0&63)+a(c>>>12>>>0&63)+a(c>>>6>>>0&63)+a(c&63)}function u(c,m,T){let f,A=[];for(let h=m;h<T;h+=3)f=(c[h]<<16)+(c[h+1]<<8)+c[h+2],A.push(l(f));return A.join("")}function d(c){let m,T=c.length%3,f="",A=[],h,p,g=16383;for(m=0,p=c.length-T;m<p;m+=g)A.push(u(c,m,m+g>p?p:m+g));switch(T){case 1:h=c[c.length-1],f+=a(h>>>2>>>0),f+=a(h<<4&63),f+="==";break;case 2:h=(c[c.length-2]<<8)+c[c.length-1],f+=a(h>>>10>>>0),f+=a(h>>>4>>>0&63),f+=a(h<<2&63),f+="=";break;default:break}return A.push(f),A.join("")}return{toByteArray:s,fromByteArray:d}})()),y_=["Accept-Charset","Accept-Encoding","Access-Control-Request-Headers","Access-Control-Request-Method","Connection","Content-Length","Cookie","Cookie2","Date","DNT","Expect","Host","Keep-Alive","Referer","TE","Trailer","Transfer-Encoding","Upgrade","Via","x-chrome-uma-enabled","x-client-data"];v_=od(8);Ia=class{constructor(t,r){this.getFn=t,this.setFn=r,this.callbacks=[],this.queried=!1,this.value=void 0}get(){let t=this;return()=>typeof t.value<"u"?Promise.resolve(t.value):new Promise((r,i)=>{if(t.callbacks.push({resolve:r,reject:i}),!t.queried){t.queried=!0;try{Promise.resolve(t.getFn()).then(n=>{for(t.value=n;t.callbacks.length;)t.callbacks.shift().resolve(n)}).catch(n=>{for(;t.callbacks.length;)t.callbacks.shift().reject(n)})}catch(n){for(t.queried=!1;t.callbacks.length;)t.callbacks.shift().reject(n)}}})}set(t){if(!this.setFn)return Promise.reject(new Error("Value is read-only"));if(typeof t>"u")return Promise.reject(new Error("Cannot set undefined value"));for(this.value=t;this.callbacks.length;)this.callbacks.shift().resolve();return this.setFn(t),Promise.resolve()}},Na=class{constructor(t=1){this.maxFn=t,this.pendings=[],this.count=0}async getMax(){return typeof this.maxFn=="function"?this.maxFn():this.maxFn}callFn(){let t=this;return(r,i)=>t.getMax().then(n=>t.count<n?t.doCall(r):new Promise((o,s)=>{let a=()=>Promise.resolve(r()).then(o).catch(s);t.pendings.push(a),i&&i(l=>{let u=t.pendings.indexOf(a);u>=0&&(t.pendings.splice(u,1),o(l))},l=>{let u=t.pendings.indexOf(a);u>=0&&(t.pendings.splice(u,1),s(l))})}))}attempt(){if(this.pendings.length>0){let t=this;t.getMax().then(r=>{t.count<r&&t.doCall(t.pendings.shift())})}}doCall(t){let r=this;return this.count++,Promise.resolve(t()).then(i=>(r.count--,r.attempt(),i)).catch(i=>{throw r.count--,r.attempt(),i})}};ka=class extends Error{constructor(t){super(t),this.name=this.constructor.name,typeof Error.captureStackTrace=="function"?Error.captureStackTrace(this,this.constructor):this.stack=new Error(t).stack}},On=class extends ka{constructor(t,r){super(t),Object.assign(this,r)}},Ca=class extends On{constructor(t,r){super(t,{_details:r})}get details(){return this._details}toString(){return`${this.message}: ${this._details}`}}});var ad={};ne(ad,{Downloads:()=>qa});var qa,sd=N(()=>{"use strict";qa=class{constructor(t){this.coapp=t}download(t){return this.coapp.call("downloads.download",t)}search(t){return this.coapp.call("downloads.search",t)}cancel(t){return this.coapp.call("downloads.cancel",t)}}});var jr,it,ld=N(()=>{jr=e=>Object.prototype.toString.call(e).slice(8,-1),it=e=>typeof e=="string"||e instanceof String});var S_,Mn,pd,ud,D_,dd,O_,fd,cd,Ba,Va,Wr,dr,Pi,Ha,M_,P_,R_,md,gd=N(()=>{ld();S_=10,Mn="0|[1-9]\\d*",pd="\\d*[A-Z-][A-Z\\d-]*",ud=`(?:${pd}|${Mn})`,D_=`${ud}(?:\\.${ud})*`,dd=`(?:${pd}|\\d+)`,O_=`${dd}(?:\\.${dd})*`,fd=`((?:${Mn})(?:\\.(?:${Mn})){2})(?:-(${D_}))?(?:\\+(${O_}))?`,cd=new RegExp(`^(?:${Mn})$`),Ba=new RegExp(`^v?${fd}$`,"i"),Va=new RegExp(`^${fd}$`,"i"),Wr=(e,t=!1)=>{if(!it(e))throw new TypeError(`Expected String but got ${jr(e)}.`);return(t?Va:Ba).test(e)},dr=(e,t=!1)=>{if(!it(e))throw new TypeError(`Expected String but got ${jr(e)}.`);if(!(t||cd.test(e)))throw new Error(`${e} is not a stringified positive integer.`);let r;if(cd.test(e)){if(r=parseInt(e,S_),!Number.isSafeInteger(r))throw new RangeError(`${r} exceeds ${Number.MAX_SAFE_INTEGER}.`)}else r=e;return r},Pi=(e,t,r=!1)=>{if(!it(e))throw new TypeError(`Expected String but got ${jr(e)}.`);if(!it(t))throw new TypeError(`Expected String but got ${jr(t)}.`);if(!Wr(e,!!r))throw new Error(`${e} is not valid version string.`);if(!Wr(t,!!r))throw new Error(`${t} is not valid version string.`);let i;if(e===t)i=0;else{let n=r?Va:Ba,[,o,s]=e.match(n),[,a,l]=t.match(n),[u,d,c]=o.split(".").map(A=>dr(A)),[m,T,f]=a.split(".").map(A=>dr(A));if(u>m)i=1;else if(u<m)i=-1;else if(d>T)i=1;else if(d<T)i=-1;else if(c>f)i=1;else if(c<f)i=-1;else if(s===l)i=0;else if(!s&&l)i=1;else if(s&&!l)i=-1;else{let A=s.split(".").map(x=>dr(x,!0)),h=l.split(".").map(x=>dr(x,!0)),p=Math.max(A.length,h.length),g=0;for(;g<p;){let x=A[g],w=h[g];if(x&&!w||it(x)&&Number.isInteger(w)?i=1:!x&&w||Number.isInteger(x)&&it(w)?i=-1:x!==w&&it(x)&&it(w)?i=x.localeCompare(w):Number.isInteger(x)&&Number.isInteger(w)&&(x>w?i=1:x<w&&(i=-1)),Number.isInteger(i))break;g++}}}return i},Ha=(e,t=!1)=>{if(!it(e))throw new TypeError(`Expected String but got ${jr(e)}.`);let r=Wr(e,!!t),i,n,o,s,a;if(r){let l=t?Va:Ba,[,u,d,c]=e.match(l);[i,n,o]=u.split(".").map(m=>dr(m)),d&&(s=d.split(".").map(m=>dr(m,!0))),c&&(a=c.split(".").map(m=>dr(m,!0)))}return{version:e,matches:r,major:i,minor:n,patch:o,pre:s,build:a}},M_=async(e,t,r=!1)=>Pi(e,t,r),P_=async(e,t=!1)=>Wr(e,t),R_=async(e,t=!1)=>Ha(e,t),md={compareSemVer:M_,isValidSemVer:P_,parseSemVer:R_}});var hd={};ne(hd,{compareSemVer:()=>Pi,isValidSemVer:()=>Wr,parseSemVer:()=>Ha,promises:()=>md});var Fa=N(()=>{gd();});var bd=y((Yx,_d)=>{"use strict";var I_=Ur(),La=I_.browser,Pn=Lt(),Ri=class{constructor(){this.listeners=[]}addListener(t){this.listeners.push(t)}removeListener(t){this.listeners=this.listeners.filter(r=>t!==r)}removeAllListeners(){this.listeners=[]}notify(...t){this.listeners.forEach(r=>{try{r(...t)}catch(i){console.warn(i)}})}},Ut=1,Rn=2,Ua=class{constructor(t,r={}){this.appId=t,this.name=r.name||t,this.appPort=null,this.pendingCalls=[],this.runningCalls=[],this.state="idle",this.postFn=this.post.bind(this),this.postMessageFn=this.postMessage.bind(this),this.onAppNotFound=new Ri,this.onAppNotFoundCheck=new Ri,this.onCallCount=new Ri,this.appStatus="unknown",this.app2AddonCallCount=0,this.addon2AppCallCount=0}post(t,r){this.appPort.postMessage(r)}postMessage(t){this.appPort.postMessage(t)}updateCallCount(t,r){switch(t){case Rn:this.app2AddonCallCount+=r;break;case Ut:this.addon2AppCallCount+=r;break}this.onCallCount.notify(this.addon2AppCallCount,this.app2AddonCallCount)}close(){if(this.appPort)try{this.appPort.disconnect(),this.cleanup()}catch{}}call(...t){return this.callCatchAppNotFound(null,...t)}callCatchAppNotFound(t,...r){let i=this;function n(o){let s;for(;s=i.pendingCalls.shift();)if(o)s.reject(o);else{i.runningCalls.push(s);let a=s;Pn.call(i.postFn,i.name,...s.params).then(l=>(i.runningCalls.splice(i.runningCalls.indexOf(a),1),l)).then(a.resolve).catch(l=>{i.runningCalls.splice(i.runningCalls.indexOf(a),1),a.reject(l)})}}switch(t&&(i.appStatus=="unknown"||i.appStatus=="checking")&&i.onAppNotFoundCheck.addListener(t),i.updateCallCount(Ut,1),this.state){case"running":return new Promise((o,s)=>{let a={resolve:o,reject:s,params:[...r]};i.runningCalls.push(a),Pn.call(i.postFn,i.name,...r).then(l=>(i.runningCalls.splice(i.runningCalls.indexOf(a),1),l)).then(a.resolve).catch(l=>{i.runningCalls.splice(i.runningCalls.indexOf(a),1),a.reject(l)})}).then(o=>(i.updateCallCount(Ut,-1),o)).catch(o=>{throw i.updateCallCount(Ut,-1),o});case"idle":return i.state="pending",new Promise((o,s)=>{i.pendingCalls.push({resolve:o,reject:s,params:[...r]});let a=La.runtime.connectNative(i.appId);i.appStatus="checking",i.appPort=a,a.onMessage.addListener(l=>{i.appStatus=="checking"&&(i.appStatus="ok",i.onAppNotFoundCheck.removeAllListeners()),Pn.receive(l,i.postMessageFn,i.name)}),a.onDisconnect.addListener(()=>{n(new Error("Disconnected")),i.cleanup(),i.appStatus=="checking"&&!t&&i.onAppNotFound.notify(i.appPort&&i.appPort.error||La.runtime.lastError)}),i.state="running",n()}).then(o=>(i.updateCallCount(Ut,-1),o)).catch(o=>{throw i.updateCallCount(Ut,-1),o});case"pending":return new Promise((o,s)=>{i.pendingCalls.push({resolve:o,reject:s,params:[...r]})}).then(o=>(i.updateCallCount(Ut,-1),o)).catch(o=>{throw i.updateCallCount(Ut,-1),o})}}listen(t){let r=this,i={};return Object.keys(t).forEach(n=>{i[n]=(...o)=>(r.updateCallCount(Rn,1),Promise.resolve(t[n](...o)).then(s=>(r.updateCallCount(Rn,-1),s)).catch(s=>{throw r.updateCallCount(Rn,-1),s}))}),Pn.listen(i)}cleanup(){let t=this;t.appStatus=="checking"&&(t.onAppNotFoundCheck.notify(t.appPort&&t.appPort.error||La.runtime.lastError),t.onAppNotFoundCheck.removeAllListeners());let r;for(;r=t.runningCalls.shift();)r.reject(new Error("Native port disconnected"));t.state="idle",t.appStatus="unknown",t.appPort=null}};_d.exports=function(...e){return new Ua(...e)}});function jt(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var ja=N(()=>{});var N_,D,k_,C,cr,Wa=N(()=>{ja();In();N_=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return j(t)},e.prototype.toString=function(){return"None"},e}(),D=new N_;Object.freeze(D);k_=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return C(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return L(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(jt(this.value),")")},e.EMPTY=new e(void 0),e}(),C=k_;(function(e){function t(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];for(var s=[],a=0,l=n;a<l.length;a++){var u=l[a];if(u.isSome())s.push(u.value);else return u}return C(s)}e.all=t;function r(){for(var n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];for(var s=0,a=n;s<a.length;s++){var l=a[s];return l.isSome(),l}return D}e.any=r;function i(n){return n instanceof C||n===D}e.isOption=i})(cr||(cr={}))});var C_,j,q_,L,pr,In=N(()=>{ja();Wa();Ga();C_=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(jt(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(jt(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new j(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return D},e.prototype.toString=function(){return"Err(".concat(jt(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new Xa(this)},e.EMPTY=new e(void 0),e}(),j=C_,q_=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(jt(this.value)),{cause:this.value})},e.prototype.map=function(t){return new L(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return C(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(jt(this.value),")")},e.prototype.toAsyncResult=function(){return new Xa(this)},e.EMPTY=new e(void 0),e}(),L=q_;(function(e){function t(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];for(var l=[],u=0,d=s;u<d.length;u++){var c=d[u];if(c.isOk())l.push(c.value);else return c}return new L(l)}e.all=t;function r(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];for(var l=[],u=0,d=s;u<d.length;u++){var c=d[u];if(c.isOk())return c;l.push(c.error)}return new j(l)}e.any=r;function i(s){try{return new L(s())}catch(a){return new j(a)}}e.wrap=i;function n(s){try{return s().then(function(a){return new L(a)}).catch(function(a){return new j(a)})}catch(a){return Promise.resolve(new j(a))}}e.wrapAsync=n;function o(s){return s instanceof j||s instanceof L}e.isResult=o})(pr||(pr={}))});var yd,vd,Xa,Ga=N(()=>{In();yd=function(e,t,r,i){function n(o){return o instanceof r?o:new r(function(s){s(o)})}return new(r||(r=Promise))(function(o,s){function a(d){try{u(i.next(d))}catch(c){s(c)}}function l(d){try{u(i.throw(d))}catch(c){s(c)}}function u(d){d.done?o(d.value):n(d.value).then(a,l)}u((i=i.apply(e,t||[])).next())})},vd=function(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},i,n,o,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(u){return function(d){return l([u,d])}}function l(u){if(i)throw new TypeError("Generator is already executing.");for(;s&&(s=0,u[0]&&(r=0)),r;)try{if(i=1,n&&(o=u[0]&2?n.return:u[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,u[1])).done)return o;switch(n=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,n=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){r.label=u[1];break}if(u[0]===6&&r.label<o[1]){r.label=o[1],o=u;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(u);break}o[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(d){u=[6,d],n=0}finally{i=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}},Xa=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(i){return yd(r,void 0,void 0,function(){var n;return vd(this,function(o){return i.isErr()?[2,i]:(n=t(i.value),[2,n instanceof e?n.promise:n])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(i){return yd(r,void 0,void 0,function(){var n;return vd(this,function(o){switch(o.label){case 0:return i.isErr()?[2,i]:(n=L,[4,t(i.value)]);case 1:return[2,n.apply(void 0,[o.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}()});var wd=N(()=>{Ga();In();Wa()});var ae=N(()=>{"use strict";wd()});var je=N(()=>{"use strict";ae()});function ye(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return C(r);return D},count:function(t){return this.reduce((r,i)=>(t(i)&&r++,r),0)},reduce:function(t,r){let i=r;for(let n of this)i=t(i,n);return i},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>C(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?C(r):D)},enumerate:function(){let t=this;return ye(function*(){let r=0;for(let i of t)yield[r,i],r++})()},filterMap:function(t){let r=this;return ye(function*(){for(let i of r){let n=t(i);n.isSome()&&(yield n.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}var Ii=N(()=>{"use strict";ae();je();Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return ye(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return ye(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return ye(function*(){for(let t of e)yield t})()})});function We(e){return Nn[e]}function Xe(e){return Qa[e]}function za(e){return typeof e=="string"&&e in Nn?C(e):D}var Xr,Nn,Qa,Gr,Qr,xt=N(()=>{"use strict";ae();Ii();je();Xr=/.^/,Nn={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:Xr,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:Xr,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:Xr,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:Xr,defacto_container:"Mp4"}},Qa={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:Xr,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:Xr,defacto_container:"Mp4"}},Gr=ye(function*(){for(let e of Object.keys(Nn))yield Nn[e]}),Qr=ye(function*(){for(let e of Object.keys(Qa))yield Qa[e]})});function Ja(e){return typeof e=="string"&&e in kn?C(e):D}function Ni(e){for(let t of $a()){let i=t.supported_video_codecs.length==0?"audio_only":"whole";if(t.extension===e)return C([t,i]);if(t.audio_only_extension&&t.audio_only_extension===e)return C([t,"audio_only"]);if(t.other_extensions){for(let n of t.other_extensions)if(n==e)return C([t,i])}}return D}function ce(e){return kn[e]}var kn,B_,$a,Ge=N(()=>{"use strict";ae();Ii();xt();je();kn={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:Gr().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:Qr().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:D,video:D},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:C("MP3"),video:C("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:C("AAC"),video:D},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:C("FLAC"),video:D},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:C("MP3"),video:C("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:D,video:D},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:C("Wav"),video:D},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:D,video:D},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},B_=ye(function*(){for(let e of Object.keys(kn))yield e}),$a=ye(function*(){for(let e of B_())yield kn[e]})});function Ad(e,t){let r=!!e.audio,i=!!t.audio,n=!!e.video,o=!!t.video;return r===i&&n&&o}function Qe(e,t,r){if(e.audio&&e.video)return{audio:t(e.audio),video:r(e.video)};if(e.video)return{video:r(e.video),audio:!1};if(e.audio)return{audio:t(e.audio),video:!1};throw"unreachable"}var fr=N(()=>{"use strict"});function Td(e,t){let r=parseInt(e),i=parseInt(t);return r<i}function Cn(e,t){let r=parseInt(e),i=parseInt(t);return r>i}function Ed(e){for(let t of qn())if(e.includes(t))return C(t);return D}function zr(e){let t=qn().map(r=>parseInt(r)).toArray();t.sort((r,i)=>r-i),t.reverse();for(let r of t)if(e>=r)return r.toString();return V_}function ki(e){if(typeof e=="string")return qn().find(t=>t==e);if(typeof e=="number"){let t=e.toString();return ki(t)}return D}var V_,xd,qn,V3,$r=N(()=>{"use strict";ae();Ii();je();V_="240",xd={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};qn=ye(function*(){for(let e of Object.keys(xd))yield e}),V3=ye(function*(){for(let e of qn())yield xd[e]})});function Wt(){return{codec:We("unknown"),fps:D,dimensions:D,quality:D,bitrate:D}}function Sd(){return{codec:Xe("unknown"),bitrate:D}}function Ci(e,t,r){if(e.protocol==="hls"&&t.protocol!="hls")return-1;if(t.protocol==="hls"&&e.protocol!="hls"||e.protocol==="non-adaptative"&&t.protocol!="non-adaptative")return 1;if(t.protocol==="non-adaptative"&&e.protocol!="non-adaptative")return-1;if(e.container.name!=t.container.name){if(e.container.name==r.container)return-1;if(t.container.name==r.container)return 1;let s=r.ignored_containers.includes(e.container.name),a=r.ignored_containers.includes(t.container.name);if(!s&&a)return-1;if(s&&!a)return 1}if(!Ad(e.av,t.av)){if(e.av.audio&&e.av.video)return-1;if(t.av.audio&&t.av.video)return 1;if(e.av.video)return-1;if(t.av.video)return 1}if(e.duration&&t.duration){if(e.duration>t.duration)return-1;if(t.duration>e.duration)return 1}if(e.av.video&&t.av.video){let s=e.av.video,a=t.av.video;if(s.codec.name!=a.codec.name){if(s.codec.name==r.video_codec)return-1;if(a.codec.name==r.video_codec)return 1;let l=r.ignored_video_codecs.includes(s.codec.name),u=r.ignored_video_codecs.includes(a.codec.name);if(!l&&u)return-1;if(l&&!u)return 1}if(s.quality.isSome()){if(a.quality.isNone())return-1;let l=s.quality.unwrap(),u=a.quality.unwrap();if(l!=u){if(l==r.prefered_video_quality)return-1;if(u==r.prefered_video_quality)return 1;let d=T=>Cn(T,r.best_video_quality)||Td(T,r.lowest_video_quality),c=d(l),m=d(u);if(!c&&m)return-1;if(c&&!m)return 1;if(Cn(l,u))return-1;if(Cn(u,l))return 1}}if(s.dimensions.isSome()){if(a.dimensions.isNone())return-1;let l=s.dimensions.unwrap(),u=a.dimensions.unwrap();if(l.height>u.height)return-1;if(u.height>l.height)return 1}if(s.bitrate.isSome()){if(a.bitrate.isNone())return-1;let l=s.bitrate.unwrap(),u=a.bitrate.unwrap();if(l>u)return-1;if(u>l)return 1}if(s.fps.isSome()){if(a.fps.isNone())return-1;let l=s.fps.unwrap(),u=a.fps.unwrap();if(l!=u){if(l==60&&r.prefer_60fps||u==60&&r.prefer_60fps||l>u)return-1;if(u>l)return 1}}}return 0}function Dd(e,t){let r={...t};return r.duration==="unknown"&&(r.duration=e.duration),r.av.audio||(r.av.audio=e.av.audio),r.av.video||(r.av.video=e.av.video),e.av.audio&&r.av.audio&&(r.av.audio.codec.name=="unknown"&&(r.av.audio.codec=e.av.audio.codec),r.av.audio.bitrate.isNone()&&(r.av.audio.bitrate=e.av.audio.bitrate)),e.av.video&&r.av.video&&(r.av.video.codec.name=="unknown"&&(r.av.video.codec=e.av.video.codec),r.av.video.quality.isNone()&&(r.av.video.quality=e.av.video.quality),r.av.video.dimensions.isNone()&&(r.av.video.dimensions=e.av.video.dimensions),r.av.video.fps.isNone()&&(r.av.video.fps=e.av.video.fps),r.av.video.bitrate.isNone()&&(r.av.video.bitrate=e.av.video.bitrate)),r}var mr=N(()=>{"use strict";ae();Ge();xt();fr();$r();je()});var Od,Md,Pd,Rd,Id=N(()=>{"use strict";Od=Object.keys({id:1,actions:1,status:1,raw_bitrate:1,operation:1,description:1,opStartDate:1,descrPrefix:1,title:1,topUrl:1,thumbnail:1,thumbnailUrl:1,size:1,duration:1,quality:1,bitrate:1,length:1,mediaDomain:1,type:1,extension:1,originalExt:1}),Md=Object.keys({id:1,type:1,originalExt:1,running:1,localFilePath:1,localDirectory:1,extension:1,extensions:1,baseJs:1,chunked:1,proxy:1,isPrivate:1,possibleContentRedirect:1,urls:1,masterManifest:1,audioMediaManifest:1,videoMediaManifest:1,headers:1,title:1,referrer:1,convert:1,baseUrl:1,mpd_url:1,mpd_video_id:1,mpd_audio_id:1,gallery_urls:1,bulk_ids:1,bulk:1,tabId:1}),Pd=Object.keys({group:1}),Rd=["url","videoUrl","audioUrl","topUrl","pageUrl","mediaManifest","mediaDomain"]});function Nd(e,t,r){return new Map([...e.entries()].filter(([,i])=>!t.some(o=>Rd.some(s=>{if(s in i){let a=i[s];if(typeof a=="string")try{let u=new URL(a).hostname.split(".").reverse();for(let d=0;d<u.length;d++)if(u[d]!=o[d])return!1;return!0}catch{}}return!1}))).filter(([,i])=>i.status=="running"?!0:!(typeof i.length=="number"&&i.length<r)))}function kd(e,t){let r=new Map;for(let s of e.values()){let a=s.group??s.id;r.has(a)||r.set(a,[]),r.get(a).push(s)}let i=[...r.values()];for(let s of i)s.sort((a,l)=>!a.core_media||!l.core_media?(console.warn("No core_media for hit"),0):Ci(a.core_media,l.core_media,t));i.sort((s,a)=>{let c=s[0]?.core_media?.builder,m=a[0]?.core_media?.builder;return c===m?0:c==="HTTPMedia"?1:m==="HTTPMedia"?-1:c==="RawHls"?1:m==="RawHls"||c==="Hls"?-1:m==="Hls"?1:c==="MPD"?-1:m==="MPD"?1:0});let n=s=>{let a=s[0]?.status=="active",l=s[0]?.core_media?.builder==="Hls"||s[0]?.core_media?.builder==="JsonMPD"||s[0]?.core_media?.builder==="MPD";return a&&l},o=s=>s.every(a=>a.status=="active");return t.ignore_low_quality_hits&&i.some(n)&&(i=i.filter(s=>{let a=o(s),l=n(s);return!(a&&!l)})),i.map(s=>s.map(a=>{let l={};for(let u of Od)l[u]=a[u];for(let u of Md)l[u]=a[u];for(let u of Pd)l[u]=a[u];return l}).slice(0,t.max_variants))}var Cd=N(()=>{"use strict";mr();Id()});var gr,Jr,Bn=N(()=>{"use strict";gr=(e,t)=>typeof e[t]=="string",Jr=(e,t)=>typeof e[t]=="number"});function ve(e){try{if(gr(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return L(e.__serializer_value);if(e.__serializer_tag==="regex"){let i=new RegExp(e.__serializer_value);return L(i)}else if(e.__serializer_tag==="array"){let i=[];for(let n of e.__serializer_value){let o=ve(n);if(o.isErr())return o;i.push(o.unwrap())}return L(i)}else if(e.__serializer_tag==="map"){let i=[];for(let n of e.__serializer_value){let o=ve(n);if(o.isErr())return o;i.push(o.unwrap())}return L(new Map(i))}else if(e.__serializer_tag==="set"){let i=[];for(let n of e.__serializer_value){let o=ve(n);if(o.isErr())return o;i.push(o.unwrap())}return L(new Set(i))}else if(e.__serializer_tag==="result_ok"){let i=e.__serializer_value,n=ve(i);return n.isErr()?n:L(L(n.unwrap()))}else if(e.__serializer_tag==="result_err"){let i=e.__serializer_value,n=ve(i);return n.isErr()?n:L(j(n.unwrap()))}else if(e.__serializer_tag==="option_some"){let i=e.__serializer_value,n=ve(i);return n.isErr()?n:L(C(n.unwrap()))}else if(e.__serializer_tag==="option_none")return L(D)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return j("This object was not serialized with Serialize");let r={};for(let i of Object.keys(e))if(typeof i=="string"){let n=ve(e[i]);if(n.isErr())return n;r[i]=n.unwrap()}return L(r)}catch{return j("Failed to inspect object. Not JSON?")}}function we(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return L({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return L({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(o=>we(o)),i=r.as_iter().find(o=>o.isErr());if(i.isSome())return i.unwrap();let n=r.as_iter().map(o=>o.unwrap()).toArray();return L({__serializer_tag:"array",__serializer_value:n})}else if(e instanceof Map){let r=[...e.entries()].map(o=>we(o)),i=r.as_iter().find(o=>o.isErr());if(i.isSome())return i.unwrap();let n=r.as_iter().map(o=>o.unwrap()).toArray();return L({__serializer_tag:"map",__serializer_value:n})}else if(e instanceof Set){let r=[...e.values()].map(o=>we(o)),i=r.as_iter().find(o=>o.isErr());if(i.isSome())return i.unwrap();let n=r.as_iter().map(o=>o.unwrap()).toArray();return L({__serializer_tag:"set",__serializer_value:n})}else if(pr.isResult(e))if(e.isOk()){let r=e.unwrap(),i=we(r);return i.isErr()?i:L({__serializer_tag:"result_ok",__serializer_value:i.unwrap()})}else{let r=e.unwrapErr(),i=we(r);return i.isErr()?i:L({__serializer_tag:"result_err",__serializer_value:i.unwrap()})}else if(cr.isOption(e))if(e.isSome()){let r=e.unwrap(),i=we(r);return i.isErr()?i:L({__serializer_tag:"option_some",__serializer_value:i.unwrap()})}else return L({__serializer_tag:"option_none"});else if(t==="object"){let r={},i=e;for(let n of Object.keys(e)){let o=i[n],s=we(o);if(s.isErr())continue;let a=s.unwrap();r[n]=a}return L(r)}else return j("Unsupported value")}function nt(e){if(typeof e>"u")return"undef";if(typeof e=="string"||typeof e=="number"||typeof e=="boolean"||e==null)return e;if(e instanceof RegExp)return e.source;if(Array.isArray(e))return e.map(nt);if(e instanceof Map)return[...e.values()].map(nt);if(e instanceof Set)return[...e.values()].map(nt);if(pr.isResult(e))return e.isOk()?nt(e.unwrap()):nt(e.unwrapErr());if(cr.isOption(e))return e.isSome()?nt(e.unwrap()):"None";if(typeof e=="object"){let t={},r=e;for(let i of Object.keys(e)){let n=r[i];t[i]=nt(n)}return t}else return"???"}var Vn=N(()=>{"use strict";ae();Bn();je()});function Ka(){return{prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"4320",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:[]}}function qd(e){return we(e).unwrap()}function Bd(e){let t=ve(e).unwrapOr({}),r=Ka(),i=Ja(t.container).unwrapOr(r.container),n=za(t.video_codec).unwrapOr(r.video_codec),o=ki(t.best_video_quality).unwrapOr(r.best_video_quality),s=ki(t.lowest_video_quality).unwrapOr(r.lowest_video_quality),a;if("prefered_video_quality"in t){let f=ki(t.prefered_video_quality);f.isSome()&&(a=f.unwrap())}let l=r.max_variants;if(typeof t.max_variants=="number"){let f=t.max_variants;Number.isInteger(f)&&f<=11&&f>0&&(l=f)}let u=r.prefer_60fps;typeof t.prefer_60fps=="boolean"&&(u=t.prefer_60fps);let d=r.ignore_low_quality_hits;typeof t.ignore_low_quality_hits=="boolean"&&(d=t.ignore_low_quality_hits);let c=[];if(Array.isArray(t.ignored_containers))for(let f of t.ignored_containers){let A=Ja(f);A.isSome()&&c.push(A.unwrap())}let m=[];if(Array.isArray(t.ignored_video_codecs))for(let f of t.ignored_video_codecs){let A=za(f);A.isSome()&&m.push(A.unwrap())}let T={prefer_60fps:u,ignore_low_quality_hits:d,container:i,max_variants:l,video_codec:n,lowest_video_quality:s,best_video_quality:o,ignored_containers:c,ignored_video_codecs:m};return typeof a<"u"&&(T.prefered_video_quality=a),T}var Vd=N(()=>{"use strict";Vn();Ge();xt();$r();je()});var Hd,Fd=N(()=>{"use strict";Hd={all_tabs:!1,low_quality:!1,sort_by_status:!0,sort_reverse:!1,show_button_clean:!0,show_button_clean_all:!1,show_button_convert_local:!1,hide_downloaded:!1}});function Za(){return{template:"%title",max_length:64}}async function Ld(e,t){let r=e.variants.values().next().value,i=e.page_title,n=new URL(e.page_url).hostname,o=n.replace(/\.com$|\.net$|\.org$/,""),s=new URL(r.manifest_url).pathname.split("/").pop()||"none",a="";if(i.length<4?i=o:i.length<8&&(i+="-"+o),s.includes(".")){let u=s.split(".");u.pop(),s=u.join(".")}if(!t){let u=await B(Ud);t=u.get(n)||u.get("*"),t||(console.error("Missing '*' rule"),t=Za())}try{if(t.selector&&e.tab_id!="none"){let u={tabId:e.tab_id};a=(await Ya.default.scripting.executeScript({target:u,world:Ya.default.scripting.ExecutionWorld.MAIN,args:[t.selector],func:c=>document.querySelector(c)?.textContent}))[0]?.result}}catch{}let l=t.template.replaceAll("%title",i).replaceAll("%hostname",o).replaceAll("%pathname",s).replaceAll("%selector",a);return l.length<3&&(l=o),l.trim().normalize("NFD").replace(/\./gu," ").replace(/[^\p{L}\p{N}\-\s]/ug,"").replace(/-+/gu,"-").replace(/\s+/gu," ").substring(0,t.max_length)}var Ya,es=N(()=>{"use strict";Ya=yt(Ft(),1);Kr()});function ts(e,t){if(e==null||t===null||t===void 0)return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Function||e instanceof RegExp)return e===t;if(e===t||e.valueOf()===t.valueOf())return!0;if(Array.isArray(e)&&e.length!==t.length||e instanceof Date||!(e instanceof Object)||!(t instanceof Object))return!1;let r=Object.keys(e),i=Object.keys(t).every(o=>r.indexOf(o)!==-1),n=r.every(o=>ts(e[o],t[o]));return i&&n}var jd=N(()=>{"use strict"});async function ee(e,t){let r=t;e.hooks&&(r=e.hooks.setter(t)),await qi.storage[e.where].set({[e.name]:r})}async function B(e){let t=await qi.storage[e.where].get(e.name);if(e.name in t){let r=t[e.name];return e.hooks?e.hooks.getter(r,e):r}return e.default()}function Xd(e,t){hr(e,r=>{e.delayed_on_change?console.warn("on_changed triggered too often"):e.delayed_on_change=setTimeout(async()=>{delete e.delayed_on_change;let i=await B(e);t(i)},400)})}function hr(e,t){qi.storage[e.where].onChanged.addListener(r=>{let i=r[e.name];if(i){if(ts(i.oldValue,i.newValue))return;typeof i.newValue>"u"?t(e.default()):e.hooks?t(e.hooks.getter(i.newValue,e)):t(i.newValue)}})}async function rc(){if(!await B(Wd)){await ee(Wd,!0);let e=await qi.storage.local.get("weh-prefs");if("weh-prefs"in e){let t=e["weh-prefs"];if("default-action-0"in t&&t["default-action-0"]=="copyurl"&&await ee(ns,"copy"),"lastDownloadDirectory"in t){let r=t.lastDownloadDirectory;await ee(Vi,r)}}}}var qi,Wd,Hn,Gd,Bi,Fn,rs,Xt,Vi,Qd,zd,$d,Jd,_r,is,Kd,ns,Yd,Hi,os,as,ss,Ln,ls,Zd,ec,Yr,tc,us,ds,Fi,Li,Ud,cs,Kr=N(()=>{"use strict";qi=yt(Ft(),1);Vd();Vn();Fd();es();jd();ae();Wd={name:"has_migrated_from_v8",default:()=>!1,where:"local"},Hn={name:"privacy_accept",default:()=>"unknown",where:"local"},Gd={name:"http_media_download_strategy",default:()=>"coapp",where:"local"},Bi={name:"debugger_enabled",default:()=>!1,where:"local"},Fn={name:"debugger_logs",default:()=>[],where:"session"},rs={name:"use_sidebar",default:()=>!1,where:"local"},Xt={name:"last_advanced_download",default:()=>0,where:"local"},Vi={name:"download_directory",default:()=>"dwhelper",where:"local"},Qd={name:"concurrent_downloads_max",default:()=>6,where:"local"},zd={name:"show_thumbnail_in_notification",default:()=>!0,where:"local"},$d={name:"show_success_notification",default:()=>!0,where:"local"},Jd={name:"show_success_notification_for_icognito",default:()=>!1,where:"local"},_r={name:"view_options",default:()=>structuredClone(Hd),where:"local"},is={name:"show_context_menu",default:()=>!0,where:"local"},Kd={name:"forget_media_on_tab_close",default:()=>!0,where:"local"},ns={name:"default_action",default:()=>"download",where:"local"},Yd={name:"yt_warning",default:()=>!0,where:"local"},Hi={name:"use_legacy_ui",default:()=>!1,where:"local"},os={name:"never_show_no_incognito_msg_again",default:()=>!1,where:"local"},as={name:"auto_hide_downloaded_message_has_been_displayed",default:()=>!0,where:"local"},ss={name:"valid_license_message_has_been_displayed",default:()=>!1,where:"local"},Ln={name:"successfull_dl",default:()=>0,where:"local"},ls={name:"never_show_successfull_dl_message",default:()=>!1,where:"local"},Zd={name:"record_download_history",default:()=>!1,where:"local"},ec={name:"history_limit_in_days",default:()=>30,where:"local"},Yr={name:"view_options",default:()=>({}),where:"session"},tc={name:"license",default:()=>"",where:"local",hooks:{setter:()=>{throw"License handled by V8 but setter called"},getter:e=>e}},us={name:"blacklist",default:()=>[],where:"local",hooks:{setter:e=>e.filter(t=>t.length>0),getter:e=>e}},ds={name:"last_download_directory",default:()=>D,where:"local",hooks:{setter:e=>we(e).unwrap(),getter:(e,t)=>ve(e).unwrapOr(t.default())}},Fi={name:"media_user_pref",where:"local",default:()=>Ka(),hooks:{setter:e=>qd(e),getter:e=>Bd(e)}},Li={name:"download_history",where:"local",default:()=>new Map,hooks:{setter:e=>we(e).unwrap(),getter:(e,t)=>ve(e).unwrapOr(t.default())}},Ud={name:"smartnaming",where:"local",default:()=>new Map([["*",Za()]]),hooks:{setter:e=>we(e).unwrap(),getter:(e,t)=>ve(e).unwrapOr(t.default())}},cs={name:"database",where:"session",default:()=>({yt_bulk:D,user_messages:new Set,coapp_status:"checking",license_status:{checking:!0},current_tab_id:0,current_window_id:0,downloadable:new Map,downloading:new Map,downloaded:new Map,download_errors:new Map}),hooks:{setter:e=>we(e).unwrap(),getter:(e,t)=>ve(e).unwrapOr(t.default())}}});var nc=y((B2,ic)=>{var H_=typeof global=="object"&&global&&global.Object===Object&&global;ic.exports=H_});var ac=y((V2,oc)=>{var F_=nc(),L_=typeof self=="object"&&self&&self.Object===Object&&self,U_=F_||L_||Function("return this")();oc.exports=U_});var ps=y((H2,sc)=>{var j_=ac(),W_=j_.Symbol;sc.exports=W_});var cc=y((F2,dc)=>{var lc=ps(),uc=Object.prototype,X_=uc.hasOwnProperty,G_=uc.toString,Ui=lc?lc.toStringTag:void 0;function Q_(e){var t=X_.call(e,Ui),r=e[Ui];try{e[Ui]=void 0;var i=!0}catch{}var n=G_.call(e);return i&&(t?e[Ui]=r:delete e[Ui]),n}dc.exports=Q_});var fc=y((L2,pc)=>{var z_=Object.prototype,$_=z_.toString;function J_(e){return $_.call(e)}pc.exports=J_});var _c=y((U2,hc)=>{var mc=ps(),K_=cc(),Y_=fc(),Z_="[object Null]",eb="[object Undefined]",gc=mc?mc.toStringTag:void 0;function tb(e){return e==null?e===void 0?eb:Z_:gc&&gc in Object(e)?K_(e):Y_(e)}hc.exports=tb});var yc=y((j2,bc)=>{function rb(e,t){return function(r){return e(t(r))}}bc.exports=rb});var wc=y((W2,vc)=>{var ib=yc(),nb=ib(Object.getPrototypeOf,Object);vc.exports=nb});var xc=y((X2,Ac)=>{function ob(e){return e!=null&&typeof e=="object"}Ac.exports=ob});var fs=y((G2,Ec)=>{var ab=_c(),sb=wc(),lb=xc(),ub="[object Object]",db=Function.prototype,cb=Object.prototype,Tc=db.toString,pb=cb.hasOwnProperty,fb=Tc.call(Object);function mb(e){if(!lb(e)||ab(e)!=ub)return!1;var t=sb(e);if(t===null)return!0;var r=pb.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Tc.call(r)==fb}Ec.exports=mb});var Sc=y(ms=>{"use strict";Object.defineProperty(ms,"__esModule",{value:!0});ms.default=gb;function gb(e){var t,r=e.Symbol;return typeof r=="function"?r.observable?t=r.observable:(t=r("observable"),r.observable=t):t="@@observable",t}});var Dc=y((hs,gs)=>{"use strict";Object.defineProperty(hs,"__esModule",{value:!0});var hb=Sc(),_b=bb(hb);function bb(e){return e&&e.__esModule?e:{default:e}}var Zr;typeof self<"u"?Zr=self:typeof window<"u"?Zr=window:typeof global<"u"?Zr=global:typeof gs<"u"?Zr=gs:Zr=Function("return this")();var yb=(0,_b.default)(Zr);hs.default=yb});var _s=y(ji=>{"use strict";ji.__esModule=!0;ji.ActionTypes=void 0;ji.default=Rc;var vb=fs(),wb=Pc(vb),Ab=Dc(),Oc=Pc(Ab);function Pc(e){return e&&e.__esModule?e:{default:e}}var Mc=ji.ActionTypes={INIT:"@@redux/INIT"};function Rc(e,t,r){var i;if(typeof t=="function"&&typeof r>"u"&&(r=t,t=void 0),typeof r<"u"){if(typeof r!="function")throw new Error("Expected the enhancer to be a function.");return r(Rc)(e,t)}if(typeof e!="function")throw new Error("Expected the reducer to be a function.");var n=e,o=t,s=[],a=s,l=!1;function u(){a===s&&(a=s.slice())}function d(){return o}function c(A){if(typeof A!="function")throw new Error("Expected listener to be a function.");var h=!0;return u(),a.push(A),function(){if(h){h=!1,u();var g=a.indexOf(A);a.splice(g,1)}}}function m(A){if(!(0,wb.default)(A))throw new Error("Actions must be plain objects. Use custom middleware for async actions.");if(typeof A.type>"u")throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(l)throw new Error("Reducers may not dispatch actions.");try{l=!0,o=n(o,A)}finally{l=!1}for(var h=s=a,p=0;p<h.length;p++){var g=h[p];g()}return A}function T(A){if(typeof A!="function")throw new Error("Expected the nextReducer to be a function.");n=A,m({type:Mc.INIT})}function f(){var A,h=c;return A={subscribe:function(g){if(typeof g!="object")throw new TypeError("Expected the observer to be an object.");function x(){g.next&&g.next(d())}x();var w=h(x);return{unsubscribe:w}}},A[Oc.default]=function(){return this},A}return m({type:Mc.INIT}),i={dispatch:m,subscribe:c,getState:d,replaceReducer:T},i[Oc.default]=f,i}});var ys=y(bs=>{"use strict";bs.__esModule=!0;bs.default=xb;function xb(e){typeof console<"u"&&typeof console.error=="function"&&console.error(e);try{throw new Error(e)}catch{}}});var kc=y(vs=>{"use strict";vs.__esModule=!0;vs.default=Ob;var Ic=_s(),Tb=fs(),J2=Nc(Tb),Eb=ys(),K2=Nc(Eb);function Nc(e){return e&&e.__esModule?e:{default:e}}function Sb(e,t){var r=t&&t.type,i=r&&'"'+r.toString()+'"'||"an action";return"Given action "+i+', reducer "'+e+'" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.'}function Db(e){Object.keys(e).forEach(function(t){var r=e[t],i=r(void 0,{type:Ic.ActionTypes.INIT});if(typeof i>"u")throw new Error('Reducer "'+t+`" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);var n="@@redux/PROBE_UNKNOWN_ACTION_"+Math.random().toString(36).substring(7).split("").join(".");if(typeof r(void 0,{type:n})>"u")throw new Error('Reducer "'+t+'" returned undefined when probed with a random type. '+("Don't try to handle "+Ic.ActionTypes.INIT+' or other actions in "redux/*" ')+"namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.")})}function Ob(e){for(var t=Object.keys(e),r={},i=0;i<t.length;i++){var n=t[i];typeof e[n]=="function"&&(r[n]=e[n])}var o=Object.keys(r),s=void 0,a=void 0;try{Db(r)}catch(l){a=l}return function(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},d=arguments[1];if(a)throw a;if(0)var c;for(var m=!1,T={},f=0;f<o.length;f++){var A=o[f],h=r[A],p=u[A],g=h(p,d);if(typeof g>"u"){var x=Sb(A,d);throw new Error(x)}T[A]=g,m=m||g!==p}return m?T:u}}});var qc=y(ws=>{"use strict";ws.__esModule=!0;ws.default=Mb;function Cc(e,t){return function(){return t(e.apply(void 0,arguments))}}function Mb(e,t){if(typeof e=="function")return Cc(e,t);if(typeof e!="object"||e===null)throw new Error("bindActionCreators expected an object or a function, instead received "+(e===null?"null":typeof e)+'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?');for(var r=Object.keys(e),i={},n=0;n<r.length;n++){var o=r[n],s=e[o];typeof s=="function"&&(i[o]=Cc(s,t))}return i}});var xs=y(As=>{"use strict";As.__esModule=!0;As.default=Pb;function Pb(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.length===0?function(i){return i}:t.length===1?t[0]:t.reduce(function(i,n){return function(){return i(n.apply(void 0,arguments))}})}});var Bc=y(Ts=>{"use strict";Ts.__esModule=!0;var Rb=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e};Ts.default=Cb;var Ib=xs(),Nb=kb(Ib);function kb(e){return e&&e.__esModule?e:{default:e}}function Cb(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(i){return function(n,o,s){var a=i(n,o,s),l=a.dispatch,u=[],d={getState:a.getState,dispatch:function(m){return l(m)}};return u=t.map(function(c){return c(d)}),l=Nb.default.apply(void 0,u)(a.dispatch),Rb({},a,{dispatch:l})}}}});var Vc=y(Be=>{"use strict";Be.__esModule=!0;Be.compose=Be.applyMiddleware=Be.bindActionCreators=Be.combineReducers=Be.createStore=void 0;var qb=_s(),Bb=ei(qb),Vb=kc(),Hb=ei(Vb),Fb=qc(),Lb=ei(Fb),Ub=Bc(),jb=ei(Ub),Wb=xs(),Xb=ei(Wb),Gb=ys(),rT=ei(Gb);function ei(e){return e&&e.__esModule?e:{default:e}}Be.createStore=Bb.default;Be.combineReducers=Hb.default;Be.bindActionCreators=Lb.default;Be.applyMiddleware=jb.default;Be.compose=Xb.default});var Fc=y((Un,Hc)=>{(function(e,t){typeof Un=="object"&&typeof Hc<"u"?t(Un):typeof define=="function"&&define.amd?define(["exports"],t):t(e.reduxLogger=e.reduxLogger||{})})(Un,function(e){"use strict";function t(b,E){b.super_=E,b.prototype=Object.create(E.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}})}function r(b,E){Object.defineProperty(this,"kind",{value:b,enumerable:!0}),E&&E.length&&Object.defineProperty(this,"path",{value:E,enumerable:!0})}function i(b,E,v){i.super_.call(this,"E",b),Object.defineProperty(this,"lhs",{value:E,enumerable:!0}),Object.defineProperty(this,"rhs",{value:v,enumerable:!0})}function n(b,E){n.super_.call(this,"N",b),Object.defineProperty(this,"rhs",{value:E,enumerable:!0})}function o(b,E){o.super_.call(this,"D",b),Object.defineProperty(this,"lhs",{value:E,enumerable:!0})}function s(b,E,v){s.super_.call(this,"A",b),Object.defineProperty(this,"index",{value:E,enumerable:!0}),Object.defineProperty(this,"item",{value:v,enumerable:!0})}function a(b,E,v){var R=b.slice((v||E)+1||b.length);return b.length=E<0?b.length+E:E,b.push.apply(b,R),b}function l(b){var E=typeof b>"u"?"undefined":O(b);return E!=="object"?E:b===Math?"math":b===null?"null":Array.isArray(b)?"array":Object.prototype.toString.call(b)==="[object Date]"?"date":typeof b.toString=="function"&&/^\/.*\//.test(b.toString())?"regexp":"object"}function u(b,E,v,R,F,X,G){F=F||[],G=G||[];var Q=F.slice(0);if(typeof X<"u"){if(R){if(typeof R=="function"&&R(Q,X))return;if((typeof R>"u"?"undefined":O(R))==="object"){if(R.prefilter&&R.prefilter(Q,X))return;if(R.normalize){var ke=R.normalize(Q,X,b,E);ke&&(b=ke[0],E=ke[1])}}}Q.push(X)}l(b)==="regexp"&&l(E)==="regexp"&&(b=b.toString(),E=E.toString());var Fe=typeof b>"u"?"undefined":O(b),Me=typeof E>"u"?"undefined":O(E),me=Fe!=="undefined"||G&&G[G.length-1].lhs&&G[G.length-1].lhs.hasOwnProperty(X),Le=Me!=="undefined"||G&&G[G.length-1].rhs&&G[G.length-1].rhs.hasOwnProperty(X);if(!me&&Le)v(new n(Q,E));else if(!Le&&me)v(new o(Q,b));else if(l(b)!==l(E))v(new i(Q,b,E));else if(l(b)==="date"&&b-E!==0)v(new i(Q,b,E));else if(Fe==="object"&&b!==null&&E!==null)if(G.filter(function(ie){return ie.lhs===b}).length)b!==E&&v(new i(Q,b,E));else{if(G.push({lhs:b,rhs:E}),Array.isArray(b)){var Z;for(b.length,Z=0;Z<b.length;Z++)Z>=E.length?v(new s(Q,Z,new o(void 0,b[Z]))):u(b[Z],E[Z],v,R,Q,Z,G);for(;Z<E.length;)v(new s(Q,Z,new n(void 0,E[Z++])))}else{var sr=Object.keys(b),bt=Object.keys(E);sr.forEach(function(ie,Lr){var wi=bt.indexOf(ie);wi>=0?(u(b[ie],E[ie],v,R,Q,ie,G),bt=a(bt,wi)):u(b[ie],void 0,v,R,Q,ie,G)}),bt.forEach(function(ie){u(void 0,E[ie],v,R,Q,ie,G)})}G.length=G.length-1}else b!==E&&(Fe==="number"&&isNaN(b)&&isNaN(E)||v(new i(Q,b,E)))}function d(b,E,v,R){return R=R||[],u(b,E,function(F){F&&R.push(F)},v),R.length?R:void 0}function c(b,E,v){if(v.path&&v.path.length){var R,F=b[E],X=v.path.length-1;for(R=0;R<X;R++)F=F[v.path[R]];switch(v.kind){case"A":c(F[v.path[R]],v.index,v.item);break;case"D":delete F[v.path[R]];break;case"E":case"N":F[v.path[R]]=v.rhs}}else switch(v.kind){case"A":c(b[E],v.index,v.item);break;case"D":b=a(b,E);break;case"E":case"N":b[E]=v.rhs}return b}function m(b,E,v){if(b&&E&&v&&v.kind){for(var R=b,F=-1,X=v.path?v.path.length-1:0;++F<X;)typeof R[v.path[F]]>"u"&&(R[v.path[F]]=typeof v.path[F]=="number"?[]:{}),R=R[v.path[F]];switch(v.kind){case"A":c(v.path?R[v.path[F]]:R,v.index,v.item);break;case"D":delete R[v.path[F]];break;case"E":case"N":R[v.path[F]]=v.rhs}}}function T(b,E,v){if(v.path&&v.path.length){var R,F=b[E],X=v.path.length-1;for(R=0;R<X;R++)F=F[v.path[R]];switch(v.kind){case"A":T(F[v.path[R]],v.index,v.item);break;case"D":F[v.path[R]]=v.lhs;break;case"E":F[v.path[R]]=v.lhs;break;case"N":delete F[v.path[R]]}}else switch(v.kind){case"A":T(b[E],v.index,v.item);break;case"D":b[E]=v.lhs;break;case"E":b[E]=v.lhs;break;case"N":b=a(b,E)}return b}function f(b,E,v){if(b&&E&&v&&v.kind){var R,F,X=b;for(F=v.path.length-1,R=0;R<F;R++)typeof X[v.path[R]]>"u"&&(X[v.path[R]]={}),X=X[v.path[R]];switch(v.kind){case"A":T(X[v.path[R]],v.index,v.item);break;case"D":X[v.path[R]]=v.lhs;break;case"E":X[v.path[R]]=v.lhs;break;case"N":delete X[v.path[R]]}}}function A(b,E,v){if(b&&E){var R=function(F){v&&!v(b,E,F)||m(b,E,F)};u(b,E,R)}}function h(b){return"color: "+J[b].color+"; font-weight: bold"}function p(b){var E=b.kind,v=b.path,R=b.lhs,F=b.rhs,X=b.index,G=b.item;switch(E){case"E":return[v.join("."),R,"\u2192",F];case"N":return[v.join("."),F];case"D":return[v.join(".")];case"A":return[v.join(".")+"["+X+"]",G];default:return[]}}function g(b,E,v,R){var F=d(b,E);try{R?v.groupCollapsed("diff"):v.group("diff")}catch{v.log("diff")}F?F.forEach(function(X){var G=X.kind,Q=p(X);v.log.apply(v,["%c "+J[G].text,h(G)].concat(k(Q)))}):v.log("\u2014\u2014 no diff \u2014\u2014");try{v.groupEnd()}catch{v.log("\u2014\u2014 diff end \u2014\u2014 ")}}function x(b,E,v,R){switch(typeof b>"u"?"undefined":O(b)){case"object":return typeof b[R]=="function"?b[R].apply(b,k(v)):b[R];case"function":return b(E);default:return b}}function w(b){var E=b.timestamp,v=b.duration;return function(R,F,X){var G=["action"];return G.push("%c"+String(R.type)),E&&G.push("%c@ "+F),v&&G.push("%c(in "+X.toFixed(2)+" ms)"),G.join(" ")}}function _(b,E){var v=E.logger,R=E.actionTransformer,F=E.titleFormatter,X=F===void 0?w(E):F,G=E.collapsed,Q=E.colors,ke=E.level,Fe=E.diff,Me=typeof E.titleFormatter>"u";b.forEach(function(me,Le){var Z=me.started,sr=me.startedTime,bt=me.action,ie=me.prevState,Lr=me.error,wi=me.took,lr=me.nextState,ha=b[Le+1];ha&&(lr=ha.prevState,wi=ha.started-Z);var rt=R(bt),Ru=typeof G=="function"?G(function(){return lr},bt,me):G,Bh=q(sr),Vh=Q.title?"color: "+Q.title(rt)+";":"",Ai=["color: gray; font-weight: lighter;"];Ai.push(Vh),E.timestamp&&Ai.push("color: gray; font-weight: lighter;"),E.duration&&Ai.push("color: gray; font-weight: lighter;");var xi=X(rt,Bh,wi);try{Ru?Q.title&&Me?v.groupCollapsed.apply(v,["%c "+xi].concat(Ai)):v.groupCollapsed(xi):Q.title&&Me?v.group.apply(v,["%c "+xi].concat(Ai)):v.group(xi)}catch{v.log(xi)}var _a=x(ke,rt,[ie],"prevState"),ba=x(ke,rt,[rt],"action"),ya=x(ke,rt,[Lr,ie],"error"),va=x(ke,rt,[lr],"nextState");if(_a)if(Q.prevState){var Hh="color: "+Q.prevState(ie)+"; font-weight: bold";v[_a]("%c prev state",Hh,ie)}else v[_a]("prev state",ie);if(ba)if(Q.action){var Fh="color: "+Q.action(rt)+"; font-weight: bold";v[ba]("%c action    ",Fh,rt)}else v[ba]("action    ",rt);if(Lr&&ya)if(Q.error){var Lh="color: "+Q.error(Lr,ie)+"; font-weight: bold;";v[ya]("%c error     ",Lh,Lr)}else v[ya]("error     ",Lr);if(va)if(Q.nextState){var Uh="color: "+Q.nextState(lr)+"; font-weight: bold";v[va]("%c next state",Uh,lr)}else v[va]("next state",lr);Fe&&g(ie,lr,v,Ru);try{v.groupEnd()}catch{v.log("\u2014\u2014 log end \u2014\u2014")}})}function S(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=Object.assign({},Oe,b),v=E.logger,R=E.stateTransformer,F=E.errorTransformer,X=E.predicate,G=E.logErrors,Q=E.diffPredicate;if(typeof v>"u")return function(){return function(Fe){return function(Me){return Fe(Me)}}};if(b.getState&&b.dispatch)return console.error(`[redux-logger] redux-logger not installed. Make sure to pass logger instance as middleware:
// Logger with default options
import { logger } from 'redux-logger'
const store = createStore(
  reducer,
  applyMiddleware(logger)
)
// Or you can create your own logger with custom options http://bit.ly/redux-logger-options
import createLogger from 'redux-logger'
const logger = createLogger({
  // ...options
});
const store = createStore(
  reducer,
  applyMiddleware(logger)
)
`),function(){return function(Fe){return function(Me){return Fe(Me)}}};var ke=[];return function(Fe){var Me=Fe.getState;return function(me){return function(Le){if(typeof X=="function"&&!X(Me,Le))return me(Le);var Z={};ke.push(Z),Z.started=$.now(),Z.startedTime=new Date,Z.prevState=R(Me()),Z.action=Le;var sr=void 0;if(G)try{sr=me(Le)}catch(ie){Z.error=F(ie)}else sr=me(Le);Z.took=$.now()-Z.started,Z.nextState=R(Me());var bt=E.diff&&typeof Q=="function"?Q(Me,Le):E.diff;if(_(ke,Object.assign({},E,{diff:bt})),ke.length=0,Z.error)throw Z.error;return sr}}}}var M,I,U=function(b,E){return new Array(E+1).join(b)},W=function(b,E){return U("0",E-b.toString().length)+b},q=function(b){return W(b.getHours(),2)+":"+W(b.getMinutes(),2)+":"+W(b.getSeconds(),2)+"."+W(b.getMilliseconds(),3)},$=typeof performance<"u"&&performance!==null&&typeof performance.now=="function"?performance:Date,O=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(b){return typeof b}:function(b){return b&&typeof Symbol=="function"&&b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},k=function(b){if(Array.isArray(b)){for(var E=0,v=Array(b.length);E<b.length;E++)v[E]=b[E];return v}return Array.from(b)},z=[];M=(typeof global>"u"?"undefined":O(global))==="object"&&global?global:typeof window<"u"?window:{},I=M.DeepDiff,I&&z.push(function(){typeof I<"u"&&M.DeepDiff===d&&(M.DeepDiff=I,I=void 0)}),t(i,r),t(n,r),t(o,r),t(s,r),Object.defineProperties(d,{diff:{value:d,enumerable:!0},observableDiff:{value:u,enumerable:!0},applyDiff:{value:A,enumerable:!0},applyChange:{value:m,enumerable:!0},revertChange:{value:f,enumerable:!0},isConflict:{value:function(){return typeof I<"u"},enumerable:!0},noConflict:{value:function(){return z&&(z.forEach(function(b){b()}),z=null),d},enumerable:!0}});var J={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},Oe={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(b){return b},actionTransformer:function(b){return b},errorTransformer:function(b){return b},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},Ne=function(){var b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},E=b.dispatch,v=b.getState;return typeof E=="function"||typeof v=="function"?S()({dispatch:E,getState:v}):void console.error(`
[redux-logger v3] BREAKING CHANGE
[redux-logger v3] Since 3.0.0 redux-logger exports by default logger with default settings.
[redux-logger v3] Change
[redux-logger v3] import createLogger from 'redux-logger'
[redux-logger v3] to
[redux-logger v3] import { createLogger } from 'redux-logger'
`)};e.defaults=Oe,e.createLogger=S,e.logger=Ne,e.default=Ne,Object.defineProperty(e,"__esModule",{value:!0})})});var Uc=y((Lc,jn)=>{(function(e,t){"use strict";typeof jn=="object"&&typeof jn.exports=="object"?jn.exports=t():typeof define=="function"&&define.amd?define([],t):e.objectPath=t()})(Lc,function(){"use strict";var e=Object.prototype.toString;function t(d,c){return d==null?!1:Object.prototype.hasOwnProperty.call(d,c)}function r(d){if(!d||o(d)&&d.length===0)return!0;if(typeof d!="string"){for(var c in d)if(t(d,c))return!1;return!0}return!1}function i(d){return e.call(d)}function n(d){return typeof d=="object"&&i(d)==="[object Object]"}var o=Array.isArray||function(d){return e.call(d)==="[object Array]"};function s(d){return typeof d=="boolean"||i(d)==="[object Boolean]"}function a(d){var c=parseInt(d);return c.toString()===d?c:d}function l(d){d=d||{};var c=function(h){return Object.keys(c).reduce(function(p,g){return g==="create"||typeof c[g]=="function"&&(p[g]=c[g].bind(c,h)),p},{})},m;d.includeInheritedProps?m=function(){return!0}:m=function(h,p){return typeof p=="number"&&Array.isArray(h)||t(h,p)};function T(h,p){if(m(h,p))return h[p]}var f;d.includeInheritedProps?f=function(h,p){typeof p!="string"&&typeof p!="number"&&(p=String(p));var g=T(h,p);if(p==="__proto__"||p==="prototype"||p==="constructor"&&typeof g=="function")throw new Error("For security reasons, object's magic properties cannot be set");return g}:f=function(h,p){return T(h,p)};function A(h,p,g,x){if(typeof p=="number"&&(p=[p]),!p||p.length===0)return h;if(typeof p=="string")return A(h,p.split(".").map(a),g,x);var w=p[0],_=f(h,w);return p.length===1?((_===void 0||!x)&&(h[w]=g),_):(_===void 0&&(typeof p[1]=="number"?h[w]=[]:h[w]={}),A(h[w],p.slice(1),g,x))}return c.has=function(h,p){if(typeof p=="number"?p=[p]:typeof p=="string"&&(p=p.split(".")),!p||p.length===0)return!!h;for(var g=0;g<p.length;g++){var x=a(p[g]);if(typeof x=="number"&&o(h)&&x<h.length||(d.includeInheritedProps?x in Object(h):t(h,x)))h=h[x];else return!1}return!0},c.ensureExists=function(h,p,g){return A(h,p,g,!0)},c.set=function(h,p,g,x){return A(h,p,g,x)},c.insert=function(h,p,g,x){var w=c.get(h,p);x=~~x,o(w)||(w=[],c.set(h,p,w)),w.splice(x,0,g)},c.empty=function(h,p){if(!r(p)&&h!=null){var g,x;if(g=c.get(h,p)){if(typeof g=="string")return c.set(h,p,"");if(s(g))return c.set(h,p,!1);if(typeof g=="number")return c.set(h,p,0);if(o(g))g.length=0;else if(n(g))for(x in g)m(g,x)&&delete g[x];else return c.set(h,p,null)}}},c.push=function(h,p){var g=c.get(h,p);o(g)||(g=[],c.set(h,p,g)),g.push.apply(g,Array.prototype.slice.call(arguments,2))},c.coalesce=function(h,p,g){for(var x,w=0,_=p.length;w<_;w++)if((x=c.get(h,p[w]))!==void 0)return x;return g},c.get=function(h,p,g){if(typeof p=="number"&&(p=[p]),!p||p.length===0)return h;if(h==null)return g;if(typeof p=="string")return c.get(h,p.split("."),g);var x=a(p[0]),w=f(h,x);return w===void 0?g:p.length===1?w:c.get(h[x],p.slice(1),g)},c.del=function(p,g){if(typeof g=="number"&&(g=[g]),p==null||r(g))return p;if(typeof g=="string")return c.del(p,g.split("."));var x=a(g[0]);if(f(p,x),!m(p,x))return p;if(g.length===1)o(p)?p.splice(x,1):delete p[x];else return c.del(p[x],g.slice(1));return p},c}var u=l();return u.create=l,u.withInheritedProps=l({includeInheritedProps:!0}),u})});var Xc=y((nT,Wc)=>{"use strict";var jc=Uc().get;function Qb(e,t){return e===t}function zb(e,t,r){r=r||Qb;var i=jc(e(),t);return function(o){return function(){var s=jc(e(),t);if(!r(i,s)){var a=i;i=s,o(s,a,t)}}}}Wc.exports=zb});var Wn={};ne(Wn,{clear:()=>Qc,error:()=>Yb,getEntry:()=>$c,log:()=>Gc,logDetails:()=>zc,reducer:()=>Kb});function Kb(e=[],t){switch(t.type){case"log.new":e=e.concat([Object.assign({key:++Jb},t.payload)]);break;case"log.clear":e=[];break}return e}function Gc(e,t="log"){if(e instanceof Error){let r="";e.fileName&&e.lineNumber&&(r=e.fileName+":"+e.lineNumber,r.columnNumber&&(r+=":"+e.columnNumber),r+=`
`),e.stack&&(r+=e.stack),e={message:e.message,details:e.details||r||void 0,videoTitle:e.videoTitle||void 0}}else typeof e=="string"?e={message:e}:e={message:e.message||""+e,details:e.details||void 0};Ss.dispatch("log.new",Object.assign(e,{type:t}))}function Yb(e){Gc(e,"error")}function Qc(){Ss.dispatch("log.clear")}function zc(e){Es.rpc.call("main","embed",$b.runtime.getURL("content/logdetails-embed.html?panel=logdetails#"+encodeURIComponent(e)))}function $c(e){let t=null;if(Ss.getLogs().forEach(i=>{i.key==e&&(t=i)}),t)return t;throw new Error("Log entry not found")}var Es,$b,Ss,Jb,Xn=N(()=>{"use strict";Es=te(),$b=Es.browser,Ss=($e(),P(ze)),Jb=0;Es.rpc.listen({clearLogs:Qc,logDetails:zc,getLogEntry:$c})});var Kc=y(Jc=>{"use strict";Object.defineProperty(Jc,"__esModule",{value:!0})});var Wi=y(Gn=>{"use strict";Object.defineProperty(Gn,"__esModule",{value:!0});Gn.THROW_THE_ERROR=void 0;Gn.THROW_THE_ERROR=e=>{throw e}});var Xi=y(br=>{"use strict";var Zb=br&&br.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ey=br&&br.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Zb(t,e,r)};Object.defineProperty(br,"__esModule",{value:!0});ey(Wi(),br)});var Yc=y(Qn=>{"use strict";Object.defineProperty(Qn,"__esModule",{value:!0});Qn.httpStatusCodeFrom=void 0;var ty=zn(),ry=Xi();function iy(e,t=ry.THROW_THE_ERROR){return ty.mustBeHttpStatusCode(e,t),e}Qn.httpStatusCodeFrom=iy});var Zc=y($n=>{"use strict";Object.defineProperty($n,"__esModule",{value:!0});$n.isHttpStatusCode=void 0;function ny(e){return e>=100&&e<=599}$n.isHttpStatusCode=ny});var yr=y(Jn=>{"use strict";Object.defineProperty(Jn,"__esModule",{value:!0});Jn.AppError=void 0;var Ds=class extends Error{constructor(t){super(t.detail),this.details=t,this.name=this.details.packageName+"/"+this.details.errorName}};Jn.AppError=Ds});var Ms=y(Kn=>{"use strict";Object.defineProperty(Kn,"__esModule",{value:!0});Kn.ValueObject=void 0;var Os=class{constructor(t){this.value=t}valueOf(){return this.value}isValue(){return!0}};Kn.ValueObject=Os});var vr=y(Yn=>{"use strict";Object.defineProperty(Yn,"__esModule",{value:!0});Yn.StructuredProblemReport=void 0;var oy=Ms(),Ps=class e extends oy.ValueObject{static from(t){return new e(t)}get detail(){return this.value.template.detail}get errorId(){var t;return(t=this.value.errorId)!==null&&t!==void 0?t:null}get errorName(){return this.value.template.errorName}get extra(){return this.value.extra}get fqErrorName(){return this.packageName+"/"+this.errorName}get packageName(){return this.value.template.packageName}get status(){return this.value.template.status}get template(){return this.value.template}};Yn.StructuredProblemReport=Ps});var wr=y(Pe=>{"use strict";Object.defineProperty(Pe,"__esModule",{value:!0});Pe.ERROR_TABLE=Pe.PackageErrorTable=Pe.PACKAGE_NAME=void 0;Pe.PACKAGE_NAME="@ganbarodigital/ts-lib-error-reporting/lib/v1";var Zn=class{constructor(){this["http-status-code-out-of-range"]={packageName:Pe.PACKAGE_NAME,errorName:"http-status-code-out-of-range",detail:"input falls outside the range of a valid HTTP status code",status:422},this["invalid-package-name"]={packageName:Pe.PACKAGE_NAME,errorName:"invalid-package-name",detail:"package name does not meet spec 'isPackageName()'",status:422},this["not-an-integer"]={packageName:Pe.PACKAGE_NAME,errorName:"not-an-integer",detail:"input must be an integer; was a float",status:422},this["not-implemented"]={packageName:Pe.PACKAGE_NAME,errorName:"not-implemented",detail:"this function or feature has not been implemented",status:500},this["unreachable-code"]={packageName:Pe.PACKAGE_NAME,errorName:"unreachable-code",status:500,detail:"this code should never execute"}}};Pe.PackageErrorTable=Zn;Pe.ERROR_TABLE=new Zn});var Is=y(eo=>{"use strict";Object.defineProperty(eo,"__esModule",{value:!0});eo.HttpStatusCodeOutOfRangeError=void 0;var ay=yr(),sy=vr(),ly=wr(),Rs=class extends ay.AppError{constructor(t){let r={template:ly.ERROR_TABLE["http-status-code-out-of-range"],errorId:t.errorId,extra:{public:t.public}};super(sy.StructuredProblemReport.from(r))}};eo.HttpStatusCodeOutOfRangeError=Rs});var ks=y(to=>{"use strict";Object.defineProperty(to,"__esModule",{value:!0});to.NotAnIntegerError=void 0;var uy=yr(),dy=vr(),cy=wr(),Ns=class extends uy.AppError{constructor(t){let r={template:cy.ERROR_TABLE["not-an-integer"],errorId:t.errorId,extra:{public:t.public}};super(dy.StructuredProblemReport.from(r))}};to.NotAnIntegerError=Ns});var ep=y(ro=>{"use strict";Object.defineProperty(ro,"__esModule",{value:!0});ro.mustBeHttpStatusCode=void 0;var py=zn(),fy=Is(),my=ks(),gy=Xi();function hy(e,t=gy.THROW_THE_ERROR){e>>>0!==e&&t(new my.NotAnIntegerError({public:{input:e}})),py.isHttpStatusCode(e)||t(new fy.HttpStatusCodeOutOfRangeError({public:{input:e}}))}ro.mustBeHttpStatusCode=hy});var zn=y(ot=>{"use strict";var _y=ot&&ot.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),io=ot&&ot.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&_y(t,e,r)};Object.defineProperty(ot,"__esModule",{value:!0});io(Kc(),ot);io(Yc(),ot);io(Zc(),ot);io(ep(),ot)});var tp=y(Gi=>{"use strict";Object.defineProperty(Gi,"__esModule",{value:!0});var Cs=zn();Object.defineProperty(Gi,"isHttpStatusCode",{enumerable:!0,get:function(){return Cs.isHttpStatusCode}});Object.defineProperty(Gi,"mustBeHttpStatusCode",{enumerable:!0,get:function(){return Cs.mustBeHttpStatusCode}});Object.defineProperty(Gi,"httpStatusCodeFrom",{enumerable:!0,get:function(){return Cs.httpStatusCodeFrom}})});var rp=y(Ar=>{"use strict";var by=Ar&&Ar.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),yy=Ar&&Ar.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&by(t,e,r)};Object.defineProperty(Ar,"__esModule",{value:!0});yy(tp(),Ar)});var np=y(ip=>{"use strict";Object.defineProperty(ip,"__esModule",{value:!0})});var ap=y(op=>{"use strict";Object.defineProperty(op,"__esModule",{value:!0})});var sp=y(Tt=>{"use strict";var vy=Tt&&Tt.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),qs=Tt&&Tt.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&vy(t,e,r)};Object.defineProperty(Tt,"__esModule",{value:!0});qs(yr(),Tt);qs(np(),Tt);qs(ap(),Tt)});var up=y(lp=>{"use strict";Object.defineProperty(lp,"__esModule",{value:!0})});var dp=y(xr=>{"use strict";var wy=xr&&xr.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Ay=xr&&xr.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&wy(t,e,r)};Object.defineProperty(xr,"__esModule",{value:!0});Ay(up(),xr)});var pp=y(cp=>{"use strict";Object.defineProperty(cp,"__esModule",{value:!0})});var fp=y(Tr=>{"use strict";var xy=Tr&&Tr.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Ty=Tr&&Tr.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&xy(t,e,r)};Object.defineProperty(Tr,"__esModule",{value:!0});Ty(pp(),Tr)});var mp=y(no=>{"use strict";Object.defineProperty(no,"__esModule",{value:!0});no.InvalidPackageNameError=void 0;var Ey=yr(),Sy=vr(),Dy=wr(),Bs=class extends Ey.AppError{constructor(t){let r={template:Dy.ERROR_TABLE["invalid-package-name"],errorId:t.errorId,extra:{public:t.public}};super(Sy.StructuredProblemReport.from(r))}};no.InvalidPackageNameError=Bs});var gp=y(oo=>{"use strict";Object.defineProperty(oo,"__esModule",{value:!0});oo.NotImplementedError=void 0;var Oy=yr(),My=vr(),Py=wr(),Vs=class extends Oy.AppError{constructor(t={}){let r={template:Py.ERROR_TABLE["not-implemented"],errorId:t.errorId};super(My.StructuredProblemReport.from(r))}};oo.NotImplementedError=Vs});var hp=y(ao=>{"use strict";Object.defineProperty(ao,"__esModule",{value:!0});ao.UnreachableCodeError=void 0;var Ry=yr(),Iy=vr(),Ny=wr(),Hs=class extends Ry.AppError{constructor(t){let r={template:Ny.ERROR_TABLE["unreachable-code"],errorId:t.errorId,extra:{logsOnly:t.logsOnly}};super(Iy.StructuredProblemReport.from(r))}};ao.UnreachableCodeError=Hs});var Fs=y(Er=>{"use strict";Object.defineProperty(Er,"__esModule",{value:!0});var ky=Is();Object.defineProperty(Er,"HttpStatusCodeOutOfRangeError",{enumerable:!0,get:function(){return ky.HttpStatusCodeOutOfRangeError}});var Cy=mp();Object.defineProperty(Er,"InvalidPackageNameError",{enumerable:!0,get:function(){return Cy.InvalidPackageNameError}});var qy=ks();Object.defineProperty(Er,"NotAnIntegerError",{enumerable:!0,get:function(){return qy.NotAnIntegerError}});var By=gp();Object.defineProperty(Er,"NotImplementedError",{enumerable:!0,get:function(){return By.NotImplementedError}});var Vy=hp();Object.defineProperty(Er,"UnreachableCodeError",{enumerable:!0,get:function(){return Vy.UnreachableCodeError}})});var bp=y(_p=>{"use strict";Object.defineProperty(_p,"__esModule",{value:!0})});var vp=y(yp=>{"use strict";Object.defineProperty(yp,"__esModule",{value:!0})});var Ap=y(wp=>{"use strict";Object.defineProperty(wp,"__esModule",{value:!0})});var Tp=y(xp=>{"use strict";Object.defineProperty(xp,"__esModule",{value:!0})});var Sp=y(Ep=>{"use strict";Object.defineProperty(Ep,"__esModule",{value:!0})});var Dp=y(Je=>{"use strict";var Hy=Je&&Je.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Qi=Je&&Je.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Hy(t,e,r)};Object.defineProperty(Je,"__esModule",{value:!0});Qi(bp(),Je);Qi(vp(),Je);Qi(Ap(),Je);Qi(Tp(),Je);Qi(Sp(),Je)});var Op=y(Sr=>{"use strict";Object.defineProperty(Sr,"__esModule",{value:!0});Sr.extractReasonFromCaught=Sr.DEFAULT_ERROR_REASON=void 0;Sr.DEFAULT_ERROR_REASON="no error information available";function Fy(e,{stackTrace:t=!1}={}){let r=Sr.DEFAULT_ERROR_REASON;return e instanceof Error?(t&&e.stack?r=e.stack:r=e.toString(),r):(e===null||e===void 0||typeof e=="number"&&isNaN(e)||typeof e=="boolean"||e.toString!==void 0&&typeof e.toString=="function"&&e.toString!==Object.prototype.toString&&(r=e.toString()),r)}Sr.extractReasonFromCaught=Fy});var Mp=y(so=>{"use strict";Object.defineProperty(so,"__esModule",{value:!0});so.extractStackFromCaught=void 0;function Ly(e){return e instanceof Error?e.stack.substring(e.stack.indexOf(`
`)+1):""}so.extractStackFromCaught=Ly});var Rp=y(Gt=>{"use strict";var Uy=Gt&&Gt.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Pp=Gt&&Gt.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Uy(t,e,r)};Object.defineProperty(Gt,"__esModule",{value:!0});Pp(Op(),Gt);Pp(Mp(),Gt)});var Np=y(Ip=>{"use strict";Object.defineProperty(Ip,"__esModule",{value:!0})});var Cp=y(kp=>{"use strict";Object.defineProperty(kp,"__esModule",{value:!0})});var qp=y(Et=>{"use strict";var jy=Et&&Et.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Ls=Et&&Et.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&jy(t,e,r)};Object.defineProperty(Et,"__esModule",{value:!0});Ls(vr(),Et);Ls(Np(),Et);Ls(Cp(),Et)});var xe=y(Ae=>{"use strict";var Wy=Ae&&Ae.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Qt=Ae&&Ae.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Wy(t,e,r)};Object.defineProperty(Ae,"__esModule",{value:!0});Qt(sp(),Ae);Qt(dp(),Ae);Qt(fp(),Ae);Qt(Fs(),Ae);Qt(Dp(),Ae);Qt(Rp(),Ae);Qt(Xi(),Ae);Qt(qp(),Ae)});var Bp=y(lo=>{"use strict";Object.defineProperty(lo,"__esModule",{value:!0});var Xy=wr();Object.defineProperty(lo,"PackageErrorTable",{enumerable:!0,get:function(){return Xy.PackageErrorTable}});var Gy=xe();Object.defineProperty(lo,"InvalidPackageNameError",{enumerable:!0,get:function(){return Gy.InvalidPackageNameError}})});var Hp=y(Vp=>{"use strict";Object.defineProperty(Vp,"__esModule",{value:!0})});var Us=y(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.isPackageNameData=Dr.PackageNameDataRegex=void 0;Dr.PackageNameDataRegex=new RegExp("^(?:@[a-z0-9-*~][a-z0-9-*._~]*/)?[a-z0-9-~][a-z0-9-._~]+(/[A-Za-z0-9-~][A-Za-z0-9-._~]+)*$");function Qy(e){return Dr.PackageNameDataRegex.test(e)}Dr.isPackageNameData=Qy});var js=y(uo=>{"use strict";Object.defineProperty(uo,"__esModule",{value:!0});uo.mustBePackageNameData=void 0;var zy=Fs(),$y=Wi(),Jy=Us();function Ky(e,t=$y.THROW_THE_ERROR){Jy.isPackageNameData(e)||t(new zy.InvalidPackageNameError({public:{packageName:e}}))}uo.mustBePackageNameData=Ky});var Fp=y(co=>{"use strict";Object.defineProperty(co,"__esModule",{value:!0});co.packageNameFrom=void 0;var Yy=Xi(),Zy=js();function ev(e,t=Yy.THROW_THE_ERROR){return Zy.mustBePackageNameData(e,t),e}co.packageNameFrom=ev});var Lp=y(at=>{"use strict";var tv=at&&at.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),po=at&&at.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&tv(t,e,r)};Object.defineProperty(at,"__esModule",{value:!0});po(Hp(),at);po(Us(),at);po(js(),at);po(Fp(),at)});var Up=y(Or=>{"use strict";var rv=Or&&Or.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),iv=Or&&Or.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&rv(t,e,r)};Object.defineProperty(Or,"__esModule",{value:!0});iv(Lp(),Or)});var Wp=y(zt=>{"use strict";var nv=zt&&zt.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),jp=zt&&zt.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&nv(t,e,r)};Object.defineProperty(zt,"__esModule",{value:!0});jp(Bp(),zt);jp(Up(),zt)});var Ws=y(ti=>{"use strict";Object.defineProperty(ti,"__esModule",{value:!0});ti.ERROR_TABLE=ti.PackageErrorTable=void 0;var fo=rp(),ov=Wp(),mo=ov.packageNameFrom("@ganbarodigital/ts-lib-mediatypes"),go=class{constructor(){this["mediatypematchregex-is-broken"]={packageName:mo,errorName:"mediatypematchregex-is-broken",detail:"the MediaTypeMatchRegex no longer returns the expected named groups",status:fo.httpStatusCodeFrom(500)},this["not-a-content-type"]={packageName:mo,errorName:"not-a-content-type",detail:"the given string does not have the structure of a ContentType",status:fo.httpStatusCodeFrom(422)},this["not-a-media-type"]={packageName:mo,errorName:"not-a-media-type",detail:"the given string does not have the structure of a MediaType",status:fo.httpStatusCodeFrom(422)},this["unexpected-content-type"]={packageName:mo,errorName:"unexpected-content-type",detail:"the given MediaType does not match any of the expected content types",status:fo.httpStatusCodeFrom(422)}}};ti.PackageErrorTable=go;ti.ERROR_TABLE=new go});var Gp=y(ho=>{"use strict";Object.defineProperty(ho,"__esModule",{value:!0});ho.NotAContentTypeError=void 0;var Xp=xe(),av=st(),Xs=class extends Xp.AppError{constructor(t){let r={template:av.ERROR_TABLE["not-a-content-type"],errorId:t.errorId,extra:{public:t.public}};super(Xp.StructuredProblemReport.from(r))}};ho.NotAContentTypeError=Xs});var zp=y(_o=>{"use strict";Object.defineProperty(_o,"__esModule",{value:!0});_o.NotAMediaTypeError=void 0;var Qp=xe(),sv=st(),Gs=class extends Qp.AppError{constructor(t){let r={template:sv.ERROR_TABLE["not-a-media-type"],errorId:t.errorId,extra:{public:t.public}};super(Qp.StructuredProblemReport.from(r))}};_o.NotAMediaTypeError=Gs});var yo=y(bo=>{"use strict";Object.defineProperty(bo,"__esModule",{value:!0});bo.MediaTypeMatchRegexIsBrokenError=void 0;var $p=xe(),lv=Ws(),Qs=class extends $p.AppError{constructor(t){let r={template:lv.ERROR_TABLE["mediatypematchregex-is-broken"],errorId:t.errorId,extra:null};super($p.StructuredProblemReport.from(r))}};bo.MediaTypeMatchRegexIsBrokenError=Qs});var $s=y(vo=>{"use strict";Object.defineProperty(vo,"__esModule",{value:!0});vo.UnexpectedContentTypeError=void 0;var Jp=xe(),uv=st(),zs=class extends Jp.AppError{constructor(t){let r={template:uv.ERROR_TABLE["unexpected-content-type"],errorId:t.errorId,extra:{public:t.public}};super(Jp.StructuredProblemReport.from(r))}};vo.UnexpectedContentTypeError=zs});var st=y(Mr=>{"use strict";Object.defineProperty(Mr,"__esModule",{value:!0});var dv=Ws();Object.defineProperty(Mr,"ERROR_TABLE",{enumerable:!0,get:function(){return dv.ERROR_TABLE}});var cv=Gp();Object.defineProperty(Mr,"NotAContentTypeError",{enumerable:!0,get:function(){return cv.NotAContentTypeError}});var pv=zp();Object.defineProperty(Mr,"NotAMediaTypeError",{enumerable:!0,get:function(){return pv.NotAMediaTypeError}});var fv=yo();Object.defineProperty(Mr,"MediaTypeMatchRegexIsBrokenError",{enumerable:!0,get:function(){return fv.MediaTypeMatchRegexIsBrokenError}});var mv=$s();Object.defineProperty(Mr,"UnexpectedContentTypeError",{enumerable:!0,get:function(){return mv.UnexpectedContentTypeError}})});var Yp=y(Kp=>{"use strict";Object.defineProperty(Kp,"__esModule",{value:!0})});var ri=y($t=>{"use strict";Object.defineProperty($t,"__esModule",{value:!0});$t.MediaTypeParamRegex=$t.MediaTypeMatchRegex=$t.ContentTypeMatchRegex=void 0;$t.ContentTypeMatchRegex=/^(?<contentType>(?<type>[A-Za-z0-9][-\w!#$&^]*)\/((?<tree>[A-Za-z0-9][\w\d-!#$&^]*)\.){0,1}(?<subtype>[^+()<>@,;:\\/"[\]?=+]+)(\+(?<suffix>[\w\d]+)){0,1})$/;$t.MediaTypeMatchRegex=/^(?<contentType>(?<type>[A-Za-z0-9][-\w!#$&^]*)\/((?<tree>[A-Za-z0-9][\w\d-!#$&^]*)\.){0,1}(?<subtype>[^+()<>@,;:\\/"[\]?=+]+)(\+(?<suffix>[\w\d]+)){0,1})(;[\s]+(?<parameter>[\w\d]+=([^+()<>@,;:\\/"[\]?=]+|"[^"]*\")))*$/;$t.MediaTypeParamRegex=/(;[\s]+((?<parameterName>[\w\d]+)=((?<parameterValueA>[^+()<>@,;:\\/"[\]?=+]+)|"(?<parameterValueB>[^"]*)")))/g});var Js=y(wo=>{"use strict";Object.defineProperty(wo,"__esModule",{value:!0});wo.isContentType=void 0;var gv=ri();function hv(e){return gv.ContentTypeMatchRegex.test(e)}wo.isContentType=hv});var Ks=y(Ao=>{"use strict";Object.defineProperty(Ao,"__esModule",{value:!0});Ao.mustBeContentType=void 0;var _v=xe(),bv=st(),yv=Js();function vv(e,t=_v.THROW_THE_ERROR){yv.isContentType(e)||t(new bv.NotAContentTypeError({public:{input:e}}))}Ao.mustBeContentType=vv});var xo=y(ii=>{"use strict";Object.defineProperty(ii,"__esModule",{value:!0});ii._contentTypeFrom=ii.contentTypeFrom=void 0;var wv=xe(),Av=Ks();ii.contentTypeFrom=Zp.bind(null,e=>e.toLowerCase());function Zp(e,t,r=wv.THROW_THE_ERROR){return Av.mustBeContentType(t,r),e(t)}ii._contentTypeFrom=Zp});var tf=y(ni=>{"use strict";Object.defineProperty(ni,"__esModule",{value:!0});ni._contentTypeFromMediaType=ni.contentTypeFromMediaType=void 0;var xv=xe(),Tv=st(),Ev=ri(),Sv=yo(),Dv=xo();ni.contentTypeFromMediaType=ef.bind(null,Ev.MediaTypeMatchRegex,e=>e.toLowerCase());function ef(e,t,r,i=xv.THROW_THE_ERROR){let n=r.valueOf(),o=e.exec(n);if(o===null)throw i(new Tv.NotAMediaTypeError({public:{input:n}}));if(o.groups===void 0)throw i(new Sv.MediaTypeMatchRegexIsBrokenError({}));return Dv._contentTypeFrom(t,o.groups.contentType)}ni._contentTypeFromMediaType=ef});var Zs=y(Ke=>{"use strict";var Ov=Ke&&Ke.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Ys=Ke&&Ke.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Ov(t,e,r)};Object.defineProperty(Ke,"__esModule",{value:!0});Ys(Yp(),Ke);var Mv=xo();Object.defineProperty(Ke,"contentTypeFrom",{enumerable:!0,get:function(){return Mv.contentTypeFrom}});var Pv=tf();Object.defineProperty(Ke,"contentTypeFromMediaType",{enumerable:!0,get:function(){return Pv.contentTypeFromMediaType}});Ys(Js(),Ke);Ys(Ks(),Ke)});var el=y(To=>{"use strict";Object.defineProperty(To,"__esModule",{value:!0});To.isMediaType=void 0;var Rv=ri();function Iv(e){return Rv.MediaTypeMatchRegex.test(e)}To.isMediaType=Iv});var nf=y(rf=>{"use strict";Object.defineProperty(rf,"__esModule",{value:!0})});var of=y(Eo=>{"use strict";Object.defineProperty(Eo,"__esModule",{value:!0});Eo.resolveToContentType=void 0;var Nv=So(),kv=Zs();function Cv(e){return e instanceof Nv.MediaType?kv.contentTypeFromMediaType(e):e}Eo.resolveToContentType=Cv});var sf=y(Do=>{"use strict";Object.defineProperty(Do,"__esModule",{value:!0});Do.resolveToMediaType=void 0;var af=So();function qv(e){return e instanceof af.MediaType?e:new af.MediaType(e)}Do.resolveToMediaType=qv});var Oo=y(St=>{"use strict";var Bv=St&&St.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),tl=St&&St.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Bv(t,e,r)};Object.defineProperty(St,"__esModule",{value:!0});tl(nf(),St);tl(of(),St);tl(sf(),St)});var rl=y(Mo=>{"use strict";Object.defineProperty(Mo,"__esModule",{value:!0});Mo.matchesContentType=void 0;var lf=Oo();function Vv(e,t){let r=lf.resolveToContentType(e);return t.some(i=>{let n=lf.resolveToContentType(i);return r===n})}Mo.matchesContentType=Vv});var il=y(Po=>{"use strict";Object.defineProperty(Po,"__esModule",{value:!0});Po.mustBeMediaType=void 0;var Hv=xe(),Fv=st(),Lv=el();function Uv(e,t=Hv.THROW_THE_ERROR){Lv.isMediaType(e)||t(new Fv.NotAMediaTypeError({public:{input:e}}))}Po.mustBeMediaType=Uv});var df=y(Ro=>{"use strict";Object.defineProperty(Ro,"__esModule",{value:!0});Ro.mustMatchContentType=void 0;var jv=xe(),Wv=$s(),Xv=rl(),uf=Oo();function Gv(e,t,r=jv.THROW_THE_ERROR){if(Xv.matchesContentType(e,t))return;let i=t.map(n=>uf.resolveToContentType(n));r(new Wv.UnexpectedContentTypeError({public:{input:uf.resolveToContentType(e),required:{anyOf:i}}}))}Ro.mustMatchContentType=Gv});var pf=y(cf=>{"use strict";Object.defineProperty(cf,"__esModule",{value:!0})});var mf=y(ff=>{"use strict";Object.defineProperty(ff,"__esModule",{value:!0})});var hf=y(gf=>{"use strict";Object.defineProperty(gf,"__esModule",{value:!0})});var _f=y(Io=>{"use strict";Object.defineProperty(Io,"__esModule",{value:!0});Io.EntityObject=void 0;var nl=class{constructor(t){this.value=t}valueOf(){return this.value}isEntity(){return!0}};Io.EntityObject=nl});var yf=y(bf=>{"use strict";Object.defineProperty(bf,"__esModule",{value:!0})});var wf=y(vf=>{"use strict";Object.defineProperty(vf,"__esModule",{value:!0})});var xf=y(Af=>{"use strict";Object.defineProperty(Af,"__esModule",{value:!0})});var Tf=y(Dt=>{"use strict";var Qv=Dt&&Dt.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ol=Dt&&Dt.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Qv(t,e,r)};Object.defineProperty(Dt,"__esModule",{value:!0});ol(wf(),Dt);ol(xf(),Dt);ol(Ms(),Dt)});var al=y(Ve=>{"use strict";var zv=Ve&&Ve.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),zi=Ve&&Ve.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&zv(t,e,r)};Object.defineProperty(Ve,"__esModule",{value:!0});zi(pf(),Ve);zi(mf(),Ve);zi(hf(),Ve);zi(_f(),Ve);zi(yf(),Ve);var $v=Tf();Object.defineProperty(Ve,"ValueObject",{enumerable:!0,get:function(){return $v.ValueObject}})});var ll=y(No=>{"use strict";Object.defineProperty(No,"__esModule",{value:!0});No.RefinedType=void 0;var Jv=al(),sl=class extends Jv.ValueObject{constructor(t,r,i){r(t,i),super(t)}};No.RefinedType=sl});var Co=y(ko=>{"use strict";Object.defineProperty(ko,"__esModule",{value:!0});ko.RefinedPrimitive=void 0;var Kv=ll(),ul=class extends Kv.RefinedType{};ko.RefinedPrimitive=ul});var Ef=y(qo=>{"use strict";Object.defineProperty(qo,"__esModule",{value:!0});qo.RefinedNumber=void 0;var Yv=Co(),dl=class extends Yv.RefinedPrimitive{[Symbol.toPrimitive](t){return t==="string"?this.value.toString():this.value}};qo.RefinedNumber=dl});var Sf=y(Bo=>{"use strict";Object.defineProperty(Bo,"__esModule",{value:!0});Bo.RefinedString=void 0;var Zv=Co(),cl=class extends Zv.RefinedPrimitive{[Symbol.toPrimitive](t){return t==="number"?null:this.value}};Bo.RefinedString=cl});var Df=y(lt=>{"use strict";var ew=lt&&lt.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Vo=lt&&lt.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&ew(t,e,r)};Object.defineProperty(lt,"__esModule",{value:!0});Vo(Ef(),lt);Vo(Co(),lt);Vo(Sf(),lt);Vo(ll(),lt)});var Mf=y(Of=>{"use strict";Object.defineProperty(Of,"__esModule",{value:!0})});var Pf=y(Ho=>{"use strict";Object.defineProperty(Ho,"__esModule",{value:!0});Ho.makeRefinedTypeFactory=void 0;var tw=Wi();Ho.makeRefinedTypeFactory=(e,t=tw.THROW_THE_ERROR)=>(r,i=t)=>(e(r,i),r)});var Rf=y(Fo=>{"use strict";Object.defineProperty(Fo,"__esModule",{value:!0});Fo.makeRefinedTypeFactoryWithFormatter=void 0;var rw=Wi();Fo.makeRefinedTypeFactoryWithFormatter=(e,t,r=rw.THROW_THE_ERROR)=>(i,n=r)=>(e(i,n),t(i))});var Nf=y(If=>{"use strict";Object.defineProperty(If,"__esModule",{value:!0})});var kf=y(ut=>{"use strict";var iw=ut&&ut.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Lo=ut&&ut.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&iw(t,e,r)};Object.defineProperty(ut,"__esModule",{value:!0});Lo(Mf(),ut);Lo(Pf(),ut);Lo(Rf(),ut);Lo(Nf(),ut)});var Cf=y(Pr=>{"use strict";var nw=Pr&&Pr.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ow=Pr&&Pr.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&nw(t,e,r)};Object.defineProperty(Pr,"__esModule",{value:!0});ow(kf(),Pr)});var qf=y(Ot=>{"use strict";var aw=Ot&&Ot.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),pl=Ot&&Ot.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&aw(t,e,r)};Object.defineProperty(Ot,"__esModule",{value:!0});pl(Df(),Ot);pl(Cf(),Ot);pl(al(),Ot)});var fl=y(oi=>{"use strict";Object.defineProperty(oi,"__esModule",{value:!0});oi._parseContentType=oi.parseContentType=void 0;var sw=xe(),Bf=st(),lw=ri(),uw=xo();oi.parseContentType=Vf.bind(null,lw.MediaTypeMatchRegex,e=>e.toLowerCase());function Vf(e,t,r,i=sw.THROW_THE_ERROR){let n=e.exec(r);if(n===null)throw i(new Bf.NotAMediaTypeError({public:{input:r}}));if(n.groups===void 0)throw i(new Bf.MediaTypeMatchRegexIsBrokenError({}));return uw._contentTypeFrom(t,n.groups.contentType)}oi._parseContentType=Vf});var ml=y(ai=>{"use strict";Object.defineProperty(ai,"__esModule",{value:!0});ai.parseMediaTypeUnbound=ai.parseMediaType=void 0;var dw=xe(),cw=st(),pw=yo(),Hf=ri();ai.parseMediaType=Ff.bind(null,Hf.MediaTypeMatchRegex,Hf.MediaTypeParamRegex);function Ff(e,t,r,i=dw.THROW_THE_ERROR,n=o=>o.toLocaleLowerCase()){let o=e.exec(r);if(o===null)throw i(new cw.NotAMediaTypeError({public:{input:r}}));if(o.groups===void 0)throw i(new pw.MediaTypeMatchRegexIsBrokenError({}));let s={type:n(o.groups.type),subtype:n(o.groups.subtype)};o.groups.tree&&(s.tree=n(o.groups.tree)),o.groups.suffix&&(s.suffix=n(o.groups.suffix));let a=t.exec(r);if(a!==null)for(s.parameters={};a!==null&&a.groups!==void 0;){let l=n(a.groups.parameterName);s.parameters[l]=a.groups.parameterValueA||a.groups.parameterValueB,a=t.exec(r)}return s}ai.parseMediaTypeUnbound=Ff});var _l=y(Jt=>{"use strict";var Uo=Jt&&Jt.__classPrivateFieldGet||function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)},Lf=Jt&&Jt.__classPrivateFieldSet||function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,r),r},$i,Ji;Object.defineProperty(Jt,"__esModule",{value:!0});Jt.MediaType=void 0;var gl=xe(),fw=qf(),mw=il(),gw=fl(),hw=ml(),hl=class e extends fw.RefinedString{constructor(t,r=gl.THROW_THE_ERROR){super(t,mw.mustBeMediaType,r),$i.set(this,void 0),Ji.set(this,void 0)}static from(t,r=gl.THROW_THE_ERROR){return new e(t,r)}getContentType(){return Uo(this,$i)||Lf(this,$i,gw.parseContentType(this.valueOf())),Uo(this,$i)}parse(){return Uo(this,Ji)||Lf(this,Ji,hw.parseMediaType(this.value,gl.THROW_THE_ERROR)),Uo(this,Ji)}};Jt.MediaType=hl;$i=new WeakMap,Ji=new WeakMap});var Uf=y(jo=>{"use strict";Object.defineProperty(jo,"__esModule",{value:!0});jo.mediaTypeFrom=void 0;var _w=_l();jo.mediaTypeFrom=_w.MediaType.from});var Wf=y(jf=>{"use strict";Object.defineProperty(jf,"__esModule",{value:!0})});var So=y(_e=>{"use strict";var bw=_e&&_e.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Mt=_e&&_e.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&bw(t,e,r)};Object.defineProperty(_e,"__esModule",{value:!0});Mt(el(),_e);Mt(rl(),_e);Mt(il(),_e);Mt(df(),_e);Mt(Uf(),_e);Mt(_l(),_e);Mt(Wf(),_e);Mt(fl(),_e);Mt(ml(),_e)});var Xf=y(dt=>{"use strict";var yw=dt&&dt.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Wo=dt&&dt.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&yw(t,e,r)};Object.defineProperty(dt,"__esModule",{value:!0});Wo(st(),dt);Wo(Zs(),dt);Wo(So(),dt);Wo(Oo(),dt)});function bl(e,t,r){let i=t.andThen(s=>s.defacto_codecs.audio),n=t.andThen(s=>s.defacto_codecs.video),o=e.split(",");if(o.length>1){let s=Gr().find(m=>m.mimetype.test(o[0])),a=Gr().find(m=>m.mimetype.test(o[1])),l=Qr().find(m=>m.mimetype.test(o[0])),u=Qr().find(m=>m.mimetype.test(o[1])),d=i.unwrapOr("unknown"),c=n.unwrapOr("unknown");return{audio:l.or(u).map(m=>m.name).unwrapOr(d),video:s.or(a).map(m=>m.name).unwrapOr(c)}}else{let s=Gr().find(u=>u.mimetype.test(o[0])).map(u=>u.name),a=Qr().find(u=>u.mimetype.test(o[0])).map(u=>u.name);return a.isSome()?{video:!1,audio:a.unwrap()}:s.isSome()?{video:s.unwrap(),audio:!1}:r.map(u=>u==="audio").unwrapOr(!1)?{video:!1,audio:a.or(i).unwrapOr("unknown")}:{video:s.or(n).unwrapOr("unknown"),audio:!1}}}function si(e){let t;try{t=(0,Gf.mediaTypeFrom)(e).parse()}catch(a){return j("parse error:"+a)}let r=D;t.type=="video"&&(r=C("video")),t.type=="audio"&&(r=C("audio"));let i=$a().find(a=>a.mimetype.test(t.subtype));if(i.isNone())return j(`Unknown container (parsed from ${t.subtype})`);let n=t.parameters?.codecs??"",o=bl(n,i,r),s=i.unwrap();return L({container:s.name,av_codecs:o})}var Gf,Ki=N(()=>{"use strict";Gf=yt(Xf(),1);ae();Ge();xt();je()});function Qf(e){return Xo({mimeType:e.mime_type,approxDurationMs:e.approx_duration_ms,bitrate:e.bitrate,fps:e.fps,height:e.height,width:e.width,qualityLabel:e.quality_label},"dash")}function Xo(e,t){if(!gr(e,"mimeType"))return j("Missing mimeType");let r=si(e.mimeType);if(r.isErr())return r;let i=r.unwrap(),n=ce(i.container),o="unknown";if(gr(e,"approxDurationMs")){let l=parseInt(e.approxDurationMs)/1e3;l&&(o=l)}let s=D;Jr(e,"bitrate")&&(s=C(e.bitrate));let a=Qe(i.av_codecs,l=>({codec:Xe(l),bitrate:s}),l=>{let u=We(l),d=D,c=D,m=D;return Jr(e,"fps")&&(d=C(e.fps)),Jr(e,"width")&&Jr(e,"height")&&(c=C({height:e.height,width:e.width})),gr(e,"qualityLabel")&&(m=Ed(e.qualityLabel)),m.isNone()&&c.isSome()&&(m=C(zr(c.unwrap().height))),{codec:u,bitrate:s,fps:d,dimensions:c,quality:m}});return L({builder:"YoutubeFormat",protocol:t,content_length:D,duration:o,container:n,av:a})}var zf=N(()=>{"use strict";ae();Ki();xt();Ge();Bn();fr();$r()});function $f(e){return e?e.replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll('"',"&quot;").replaceAll("'","&#039;"):""}var Jf=N(()=>{"use strict"});function Pt(e,t,r){let i=()=>(console.error(`Requesting unknown i18n string ${e}`),e);t=t.map(n=>n.toString()).map($f);try{if(e in r){let n=r[e],o=1;for(let s=0;s<t.length;s++)n=n.replace(`$${o}`,t[s]);return n}else{let n=Kf.default.i18n.getMessage(e,t);return n||i()}}catch{return i()}}var Kf,Go=N(()=>{"use strict";Kf=yt(Ft(),1);Jf()});function Yf(e,t){let{size:r,spread:i,radius:n,greyed:o,channel:s}=e,a=JSON.stringify({instructions:e}),l=t.get(a);if(l)return l;let d=new OffscreenCanvas(r,r).getContext("2d");d.lineCap="round";let c=r/2-5,m=r/2,T=i*Math.sin(Math.PI/6),f=i*Math.cos(Math.PI/6),A=(S,M,I,U,W)=>{d.save(),d.globalAlpha=S,d.beginPath(),d.arc(M,I,W,0,2*Math.PI,!1),d.fillStyle=U,d.fill(),d.closePath(),d.restore()},h=d.createLinearGradient(r,r*.5,r*.5,r*.5);h.addColorStop(.1652,"#A6DEEF"),h.addColorStop(.3949,"#6CC5F0"),h.addColorStop(.8805,"#355BAA");let p=d.createLinearGradient(r*.3,r*.25,r*.6,r*.6);p.addColorStop(0,"#FFF200"),p.addColorStop(1,"#FFCE07");let g=d.createLinearGradient(r*.5,r*.5,r*.5,r);g.addColorStop(0,"#EC223B"),g.addColorStop(.2577,"#E42339"),g.addColorStop(.492,"#D42634"),g.addColorStop(.7172,"#BD292C"),g.addColorStop(.9354,"#9E2B22"),g.addColorStop(1,"#942B1F");let x=.2,w="#666";if(s=="beta"&&(w="green"),s=="dev"&&(w="#F06"),A(1,c+i,m,w,n*(1+x)),A(1,c-T,m-f,w,n*(1+x)),A(1,c-T,m+f,w,n*(1+x)),d.globalCompositeOperation="destination-out",A(1,c+i,m,"#FFF",n*(1-x)),A(1,c-T,m-f,"#FFF",n*(1-x)),A(1,c-T,m+f,"#FFF",n*(1-x)),d.globalCompositeOperation="source-over",!o)A(1,c+i,m,"white",n),A(1,c-T,m-f,"white",n),A(1,c-T,m+f,"white",n),A(1,c+i,m,h,n),A(.9,c-T,m-f,p,n),A(.85,c-T,m+f,g,n);else{let S=d.createLinearGradient(0,0,r,r);S.addColorStop(0,"#333"),S.addColorStop(1,"#CCC"),A(1,c+i,m,"white",n),A(1,c-T,m-f,"white",n),A(1,c-T,m+f,"white",n),A(.2,c+i,m,S,n),A(.2,c-T,m-f,S,n),A(.2,c-T,m+f,S,n)}let _=d.getImageData(0,0,r,r);return t.set(a,_),_}var Zf=N(()=>{"use strict"});var yl,pe,Qo=N(()=>{yl="stable",pe="google"});function tm(){pe=="mozilla"?(ct.default.browserAction.setPopup({popup:"/content/popup.html?panel=main"}),ct.default.sidebarAction.setPanel({panel:null})):(chrome.action.setPopup({popup:"/content/popup.html?panel=main"}),chrome.sidePanel&&chrome.sidePanel.setPanelBehavior&&(chrome.sidePanel.setOptions({enabled:!1}),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:!1})))}function vl(e){pe=="mozilla"?ct.default.browserAction.setPopup({popup:"/content2/popup.html"}):chrome.action.setPopup({popup:"/content2/popup.html"}),wl(e,0,!1)}function wl(e,t,r){let i=pe=="mozilla";if(!i&&(chrome.sidePanel&&chrome.sidePanel.setPanelBehavior?(chrome.sidePanel.setOptions({enabled:e}),chrome.sidePanel.setPanelBehavior({openPanelOnActionClick:e})):e=!1,r))if(e)t!=0&&chrome.sidePanel?.open?.({windowId:t});else try{chrome.action.openPopup()}catch{}i&&!e&&(ct.default.browserAction.setPopup({popup:"/content2/popup.html"}),ct.default.sidebarAction.setPanel({panel:null}),r&&ct.default.sidebarAction.close()),i&&e&&(ct.default.browserAction.setPopup({popup:null}),ct.default.sidebarAction.setPanel({panel:"/content2/sidebar.html"}),r&&ct.default.sidebarAction.open())}var ct,rm=N(()=>{"use strict";ct=yt(Ft(),1);Qo()});function im(e){let t=Math.floor(e/3600);e-=t*3600;let r=Math.floor(e/60);e-=r*60;let i=Math.round(e),n=("0"+t+":").slice(-3),o=("0"+r+":").slice(-3),s=("0"+i).slice(-2);return n=="00:"&&(n=""),n+o+s}var nm=N(()=>{"use strict";Go()});function pt(e,t=0){let r=3735928559^t,i=1103547991^t;for(let n=0,o;n<e.length;n++)o=e.charCodeAt(n),r=Math.imul(r^o,2654435761),i=Math.imul(i^o,1597334677);return r=Math.imul(r^r>>>16,2246822507),r^=Math.imul(i^i>>>13,3266489909),i=Math.imul(i^i>>>16,2246822507),i^=Math.imul(r^r>>>13,3266489909),4294967296*(2097151&i)+(r>>>0)}var zo=N(()=>{"use strict"});var $o,Al=N(()=>{"use strict";mr();$o=[{mutateDownloadable:e=>{e.page_url.includes("missav.com")&&e.headers.push({name:"Pragma",value:"no-cache"})}},{mutateDownloadable:e=>{if(e.page_url.includes("://himado.in/"))for(let t of e.variants.values())t.manifest_url.endsWith("audio.mp3")&&(t.core_media.av.video=Wt(),e.is_low_quality=!1)}},{canHandleHLS:(e,t,r)=>!!(e.includes("/api/playlist/master/")&&t.includes("text"))},{canHandleHLS:(e,t,r)=>!!(e.includes("hls2.vcdnx.com")&&!e.includes("?ts=")&&t.includes("text"))}]});var Yi={};ne(Yi,{alert:()=>am,dialog:()=>Tl,fileDialog:()=>li,saveAs:()=>Tw,selectConvertFiles:()=>Ew,selectDirectory:()=>sm,selectMergeAudioFile:()=>Dw,selectMergeVideoFile:()=>Sw});async function om(){let e="2.0.17";if((await se.prefs).use_native_filepicker){let{status:r,info:i}=await xl.check();if(r){let n=i.version;if(Aw.isMinimumVersion(n,e))return!0}}return!1}function Tl(e){let t=Promise.resolve();e.type==="tab"&&(t=t.then(()=>vw.tabs.query({active:!0,lastFocusedWindow:!0}).then(i=>{i.length>0&&ww.setTransientTab("<next-tab>",i[0].id)})));let r="dialog"+ ++xw;return t=t.then(()=>{se.ui.open(r,e)}).then(()=>se.wait(r)),t.__dialogName=r,t}async function am(e){let t={autoResize:!0},r=await se.prefs;return r.alertDialogType=="tab"&&(t={bodyClass:"dialog-in-tab",autoResize:!1}),Tl({url:"content/alert.html",type:r.alertDialogType,height:e.height||200,autoClose:r.dialogAutoClose,initData:Object.assign(t,e)})}async function li(e){let t=await se.prefs,r=Tl({type:t.fileDialogType,url:"content/file-dialog.html",height:500,width:750,autoClose:t.dialogAutoClose,initData:Object.assign({filename:null,directory:null,uniqueFilename:!0,titleText:"",noSizeColumn:!1,dirOnly:!1,upDir:!0,editFileInput:!0,readonlyDir:!1,showDir:!0,okText:"OK",confirmOverwrite:!1,newDir:!1,createDir:!0},e)});return r.then(i=>(se.ui.close(r.__dialogName),i)).catch(i=>(se.ui.close(r.__dialogName),null))}async function Tw(e,t,r={}){let i=se._("save_file_as");if(await om()){let o=(await xl.call("filepicker","save_file",t,i,e)).split(`
`),s=o[0],a=o[1];return s&&t?{filePath:s,directory:a}:null}return li(Object.assign({filename:e,directory:t,uniqueFilename:!0,titleText:i,noSizeColumn:!1,dirOnly:!1,upDir:!0,editFileInput:!0,readonlyDir:!1,showDir:!0,okText:se._("save"),confirmOverwrite:!0,newDir:!0,createDir:!0},r))}async function sm(e,t={}){let r=se._("weh_prefs_label_lastDownloadDirectory");return await om()?{directory:(await xl.call("filepicker","pick_folder","~",r)).split(`
`)[0]}:li(Object.assign({directory:e,uniqueFilename:!1,titleText:r,noSizeColumn:!0,dirOnly:!0,upDir:!0,editFileInput:!1,readonlyDir:!0,showDir:!1,okText:se._("ok"),confirmOverwrite:!1,newDir:!0,createDir:!1},t))}function Ew(e,t={}){return li(Object.assign({directory:e,uniqueFilename:!1,titleText:se._("select_files_to_convert"),noSizeColumn:!1,dirOnly:!1,upDir:!0,readonlyDir:!0,editFileInput:!1,showDir:!1,okText:se._("convert"),confirmOverwrite:!1,newDir:!1,createDir:!1,selectMultiple:!0,outputConfigs:!0},t))}function Sw(e,t={}){return li(Object.assign({directory:e,uniqueFilename:!1,titleText:se._("select_video_file_to_merge"),noSizeColumn:!1,dirOnly:!1,upDir:!0,readonlyDir:!0,editFileInput:!1,showDir:!1,okText:se._("next"),confirmOverwrite:!1,newDir:!1,createDir:!1,selectMultiple:!1,outputConfigs:!1},t))}function Dw(e,t={}){return li(Object.assign({directory:e,uniqueFilename:!1,titleText:se._("select_audio_file_to_merge"),noSizeColumn:!1,dirOnly:!1,upDir:!0,readonlyDir:!0,editFileInput:!1,showDir:!1,okText:se._("next"),confirmOverwrite:!1,newDir:!1,createDir:!1,selectMultiple:!1,outputConfigs:!1},t))}var se,vw,ww,xl,Aw,xw,Zi=N(()=>{"use strict";se=te(),vw=se.browser,ww=(Ir(),P(Rr)),xl=(mt(),P(ft)),Aw=(he(),P(ge)),xw=0;se.rpc.listen({alert:am,selectDirectory:sm})});var Kt={};ne(Kt,{alertAudioNeedsReg:()=>Iw,checkLicense:()=>dm,setLicense:()=>cm,validateLicense:()=>Ol});function Pw(e){return e&&e.substring(0,1).toUpperCase()+e.substring(1)||""}function um(e,t){let r=new TextEncoder("utf-8").encode(t+e.key+e.email);return crypto.subtle.digest("SHA-256",r).then(i=>Sl.bufferToHex(i))}async function Ol(e){let t=await lm.check();if(!t.status)return{key:e,last:Date.now(),status:"nocoapp"};let r=t.info.home,i;try{i=await Sl.request({url:"https://www.downloadhelper.net/license-check.json",content:"key="+encodeURIComponent(e)+"&product=converthelper",headers:{"Content-type":"application/x-www-form-urlencoded"},method:"POST"})}catch{throw new Error(Nr._("network_error_no_response"))}if(!i.ok)throw new Error(Nr._("network_error_status",i.status+" "+i.statusText));let n=await i.json(),o={key:e,last:Date.now(),remoteStatus:n.status,status:n.status,name:n.name,email:n.email},s=Pw(ui);if((n.target=="fx"||n.target=="firefox")&&ui!="firefox"?(o.status="mismatch",o.brExt=s,o.brLicense="Firefox"):n.target=="edge"&&ui!="edge"?(o.status="mismatch",o.brExt=s,o.brLicense="Edge"):(n.target=="crx"||n.target=="chrome")&&ui!="chrome"&&(o.status="mismatch",o.brExt=s,o.brLicense="Chrome"),o.status=="accepted"){let a=await um(o,r);o.sign=a}return await Dl.set(o),o}function dm(){return new Promise((e,t)=>{El.runtime.getPlatformInfo().then(r=>{if(r.os=="linux"&&!Mw)return e({status:"unneeded"});Rw().then(i=>{if(i===null)return e({status:"unset"});let n={status:"unset"};if(i.email&&(n.email=i.email),i.key&&(n.key=i.key),i.name&&(n.name=i.name),i.status=="mismatch")return n.status="mismatch",n.brLicense=i.brLicense,n.brExt=i.brExt,e(n);lm.check().then(o=>o.status?um(i,o.info.home):(n.status="nocoapp",e(n),null)).then(o=>{if(o)return!i.remoteStatus&&n.key?new Promise((s,a)=>{Ol(n.key).then(l=>{i=l,s(o)}).catch(l=>{a(l)})}):o}).then(o=>{o&&(i.remoteStatus=="accepted"&&o===i.sign?n.status="accepted":i.remoteStatus=="blocked"?n.status="blocked":i.remoteStatus=="locked"&&(n.status="locked"),e(n))}).catch(t)}).catch(t)}).catch(t)})}function cm(e){return Dl.set(e)}function Iw(){Ow.alert({title:Nr._("converter_needs_reg"),text:Nr._("converter_reg_audio"),buttons:[{text:Nr._("get_conversion_license"),className:"btn-success",rpcMethod:"goto",rpcArgs:["https://www.downloadhelper.net/convert"+(ui?"?browser="+encodeURIComponent(ui):"")]}]})}var Nr,El,lm,Sl,Ow,ui,Mw,Dl,Rw,Yt=N(()=>{"use strict";Nr=te(),El=Nr.browser,lm=(mt(),P(ft)),Sl=(he(),P(ge)),Ow=(Zi(),P(Yi)),{browser:ui,linuxlic:Mw}=ur().buildOptions;Dl=new Sl.Cache(()=>El.storage.local.get("license").then(e=>e.license||null),e=>El.storage.local.set({license:e}));Rw=Dl.get();Nr.rpc.listen({checkLicense:dm,validateLicense:Ol,setLicense:cm})});var pm,fm=N(()=>{"use strict";pm="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"});var di={};ne(di,{convert2:()=>Hw,convert3:()=>Vw,defaultOutputConfigs:()=>tn,getCodecs:()=>bm,getFormats:()=>_m,getOutputConfigs:()=>Il,info:()=>kw,makeUniqueFileName:()=>Bw,open:()=>qw,play:()=>Cw,resetOutputConfigs:()=>hm,setOutputConfigs:()=>gm,sideDownload:()=>ym,sideDownloadAbort:()=>Lw,sideDownloadMPD:()=>Uw,updateHit:()=>Nw});function Nw(e){console.warn("TODO converter.updateHit")}function Pl(e){(e||[]).forEach(t=>{t.name=="Accept-Encoding"&&(t.value=t.value.split(",").map(r=>r.trim()).filter(r=>r!="br"&&r!="zstd").join(", "))})}function kw(e,t=!1,r=[]){Pl(r),kr&&console.log("probe",e,t,r);let i=fe.call("probe",e,t,r);return t?i.then(n=>JSON.parse(n)):i}function Cw(e){return fe.call("play",e)}function qw(e){return fe.call("open",e)}function Bw(e){return fe.call("makeUniqueFileName",e)}async function Vw(e,t,r,i){let n=["-i",e,"-y",t],o=++Jo;rn[o]=r;let s=++Ko;Rt[s]=i;try{let a=await fe.call("convert",n,{progressTime:""+o,startHandler:""+s});if(a.exitCode!=0)throw kr&&(console.warn("exitCode|convert3",a.exitCode),console.warn(a.stderr)),new en.DetailsError("Convert3 error: ",a.stderr);return t}finally{delete o[o],delete Rt[s]}}async function Hw(e,t,r,i,n){let o=[];o.push("-i",e);let s=tn[r];for(let u in s.params){let d=s.params[u];d!==null&&(typeof d!="string"||d.length>0)&&(o.push("-"+u),o.push(""+d))}if(s.extra){let u=/^\s*(.*?)\s*$/.exec(s.extra)[1].split(/\s+/);for(let d of u)o.push(d)}if(s.audioonly&&o.push("-vn"),!t){let u=e.split(".");u[u.length-1]==s.ext&&(u[u.length-2]+="-converted"),u[u.length-1]=s.ext,t=u.join(".")}o.push("-y",t);let a=++Jo;rn[a]=i;let l=++Ko;Rt[l]=n;try{let u=await fe.call("convert",o,{progressTime:""+a,startHandler:""+l});if(u.exitCode!=0)throw kr&&(console.warn("exitCode|convert2",u.exitCode),console.warn(u.stderr)),new en.DetailsError("Convert2 error: ",u.stderr);return t}finally{delete a[a],delete Rt[l]}}function gm(e){return Rl.set(Object.assign({},tn,e))}function hm(){return Il().then(e=>{let t=Object.assign({},e);return Object.keys(t).forEach(r=>{t[r].readonly||delete t[r]}),Rl.set(t)})}function _m(){return fe.call("formats")}function bm(){return fe.call("codecs")}function Fw(e,t,r){let i=rn[e];i&&i(t,r)}function Lw(e){return fe.call("abortConvert",e)}async function Uw(e,t,r,i){let n=[],o=[];Pl(i.headers),i.headers&&i.headers.length&&(n.push("-headers"),n.push(i.headers.map(l=>l.name+": "+l.value).join(`\r
`)),o.push("-headers"),o.push("$'"+i.headers.map(l=>`${l.name}: ${l.value}\\r\\n`).join("")+"'")),n.push("-analyzeduration","10M"),o.push("-analyzeduration","10M"),n.push("-i",e),o.push("-i",`'${e}'`),t&&(n.push("-map",`0:${t}`),o.push("-map",`0:${t}`)),r&&(n.push("-map",`0:${r}`),o.push("-map",`0:${r}`)),t&&(n.push("-codec","copy"),o.push("-codec","copy")),n.push("-y"),n.push(i.filePath),o.push("-y"),o.push(i.filePath);let s=++Jo;rn[s]=i.on_progress;let a=++Ko;Rt[a]=i.on_start,kr&&console.log(o.join(" "));try{let l=await fe.call("convert",n,{progressTime:""+s,startHandler:""+a});if(l.exitCode!=0)throw kr&&(console.warn("exitCode|sideDownloadMPD",l.exitCode),console.warn(l.stderr)),new en.DetailsError("SideDownload error: ",l.stderr)}finally{delete s[s],delete Rt[a]}}async function ym(e,t,r,i=!1){let n=[],o=[],s;if(r.qr_code_needed){let{path:u,fd:d}=await fe.call("tmp.file",{prefix:"vdh-wm-",postfix:".gif"});await fe.call("fs.write2",d,pm),await fe.call("fs.close",d),s=u}if(n.push("-analyzeduration","10M"),o.push("-analyzeduration","10M"),r.qr_code_needed||(n.push("-reconnect","1"),o.push("-reconnect","1"),n.push("-icy","0"),o.push("-icy","0")),r.qr_code_needed&&(n.push("-i",s),o.push("-i",s)),r.headers&&r.headers.length>0){Pl(r.headers);let u=r.headers.map(d=>`${d.name}: ${d.value}\r
`).join("");n.push("-headers"),n.push(u),o.push("-headers"),o.push("$'"+r.headers.map(d=>`${d.name}: ${d.value}\\r\\n`).join("")+"'")}if(i&&(n.push("-f","hls"),o.push("-f","hls")),e&&(n.push("-i",e),o.push("-i",`'${e}'`)),t&&(n.push("-i",t),o.push("-i",`'${t}'`)),r.qr_code_needed&&e){n.push("-filter_complex","[0][1]scale2ref=w=oh*mdar:h=ih*0.4[logo][video];[video][logo]overlay=5:H-h-5"),o.push("-filter_complex","[0][1]scale2ref=w=oh*mdar:h=ih*0.4[logo][video];[video][logo]overlay=5:H-h-5");let u="h264";r.filePath.endsWith("webm")&&(u="libvpx-vp9"),n.push(...`-c:v ${u} -preset superfast`.split(" ")),o.push(...`-c:v ${u} -preset superfast`.split(" "))}else r.merge&&(n.push("-map","0:v:0","-map","1:a:0"),o.push("-map","0:v:0","-map","1:a:0")),e&&(n.push("-c","copy"),o.push("-c","copy"));n.push("-y",r.filePath),o.push("-y",r.filePath),kr&&console.log(o.join(" "));let a=++Jo;rn[a]=r.on_progress;let l=++Ko;Rt[l]=r.on_start;try{let u=await fe.call("convert",n,{progressTime:""+a,startHandler:""+l});if(u.exitCode!=0){if(kr&&(console.warn("exitCode|sideDownload",u.exitCode),console.warn(u.stderr)),!i)return console.warn("Re-trying with forceHls"),ym(e,t,r,!0);throw new en.DetailsError("SideDownload error: ",u.stderr)}}finally{if(s)try{await fe.call("fs.unlink",s)}catch{}delete a[a],delete Rt[l]}}function jw(e,t){let r=Rt[e];if(r)try{r(t)}catch(i){console.error("start handler error",i)}}var Ml,mm,fe,en,kr,tn,Jo,rn,Ko,Rt,Rl,Il,ci=N(()=>{"use strict";fm();Ml=te(),mm=Ml.browser,fe=(mt(),P(ft)),en=(he(),P(ge)),kr=!ur().prod;Jo=0,rn={},Ko=0,Rt={};Rl=new en.Cache(()=>mm.storage.local.get("outputConfigs").then(e=>e.outputConfigs||tn),e=>mm.storage.local.set({outputConfigs:e})),Il=Rl.get();Ml.rpc.listen({getOutputConfigs:Il,setOutputConfigs:gm,resetOutputConfigs:hm,editConverterConfigs:e=>{Ml.ui.open("convoutput"+(e?"#"+e:""),{type:"tab",url:"content/convoutput.html"})},getFormats:_m,getCodecs:bm,convertStartNotification:jw});fe.listen({convertOutput:Fw});tn={"e6587753-4ca5-4d2e-b7ba-beaf1e7f191c":{title:"Re-encoded MP4 (h264/aac)",ext:"mp4",params:{"c:a":"aac",f:"mp4","c:v":"h264"},audioonly:!1,readonly:!0},"249a7d34-3640-4ac3-8300-13827811d2cf":{title:"MPEG (mpeg1+mp2)",ext:"mpg",params:{"c:a":"mp2",f:"mpeg",r:24,"c:v":"mpeg1video"},extra:"-mbd rd -trellis 2 -cmp 2 -subcmp 2 -g 100",audioonly:!1,readonly:!0},"6de4f5ce-8cfe-4f0f-8246-bacb7b0d7624":{title:"WMV 500Kb (Windows Media Player)",ext:"wmv",params:{"c:a":"wmav2",f:"asf","c:v":"wmv2","b:v":"500k"},extra:null,audioonly:!1,readonly:!0},"21a19146-e116-4460-8356-a8eab9cf61ce":{title:"WMV 1Mb (Windows Media Player)",ext:"wmv",params:{"c:a":"wmav2",f:"asf","c:v":"wmv2","b:v":"1000k"},extra:null,audioonly:!1,readonly:!0},"933b1b41-6862-4ce0-9605-10fa5e4b310c":{title:"WMV 2Mb (Windows Media Player)",ext:"wmv",params:{"c:a":"wmav2",f:"asf","c:v":"wmv2","b:v":"2000k"},extra:null,audioonly:!1,readonly:!0},"90195ab2-d891-443c-a164-8f0953ec8975":{title:"WMV 4Mb (Windows Media Player)",ext:"wmv",params:{"c:a":"wmav2",f:"asf","c:v":"wmv2","b:v":"4000k"},extra:null,audioonly:!1,readonly:!0},"3a4cc0a6-6eb0-4cff-90fb-fdf8eb6a9571":{title:"AVI 500Kb (mpeg4/mp3)",ext:"avi",params:{"c:a":"mp3",f:"avi","c:v":"mpeg4","b:v":"500k","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"ebdbb895-7a1e-43e2-bef4-be6e62cb8507":{title:"AVI 1Mb (mpeg4/mp3)",ext:"avi",params:{"c:a":"mp3",f:"avi","c:v":"mpeg4","b:v":"1000k","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"0b6280d3-f8f2-4cb6-8235-a5a4b91488f7":{title:"AVI 2Mb (mpeg4/mp3)",ext:"avi",params:{"c:a":"mp3",f:"avi","c:v":"mpeg4","b:v":"2000k","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"9ea8a22b-5738-4d0f-8494-3037ec568191":{title:"AVI 4Mb (mpeg4/mp3)",ext:"avi",params:{"c:a":"mp3",f:"avi","c:v":"mpeg4","b:v":"4000k","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"4174b9dd-c2a0-409d-801d-c84f96be0b76":{title:"MP3",ext:"mp3",params:{"b:a":"128k","c:a":"mp3",f:"mp3"},extra:null,audioonly:!0,readonly:!0},"05cb6b27-9167-4d83-833d-218a107d0376":{title:"MP3 HQ",ext:"mp3",params:{"b:a":"256k","c:a":"mp3",f:"mp3"},extra:null,audioonly:!0,readonly:!0},"69397f64-54f2-4ee4-b47a-b4fc42ee2ec1":{title:"MP4 500Kb",ext:"mp4",params:{"c:v":"mpeg4","c:a":"aac",f:"mp4","b:v":"500k","b:a":"128k",ac:2},extra:"-mbd rd -flags +mv4+aic -trellis 2 -cmp 2 -subcmp 2 -g 300",audioonly:!1,readonly:!0},"16044db3-3b75-4155-b549-c0ba19c18887":{title:"MP4 1Mb",ext:"mp4",params:{"c:v":"mpeg4","c:a":"aac",f:"mp4","b:v":"1000k","b:a":"128k",ac:2},extra:"-mbd rd -flags +mv4+aic -trellis 2 -cmp 2 -subcmp 2 -g 300",audioonly:!1,readonly:!0},"b5535083-bf16-4ae0-a21f-7c637ce0617f":{title:"MP4 2Mb",ext:"mp4",params:{"c:v":"mpeg4","c:a":"aac",f:"mp4","b:v":"2000k","b:a":"128k",ac:2},extra:"-mbd rd -flags +mv4+aic -trellis 2 -cmp 2 -subcmp 2 -g 300",audioonly:!1,readonly:!0},"dfbed97f-46c9-4db8-b5d1-4d19901bc236":{title:"MP4 4Mb",ext:"mp4",params:{"c:v":"mpeg4","c:a":"aac",f:"mp4","b:v":"4000k","b:a":"128k",ac:2},extra:"-mbd rd -flags +mv4+aic -trellis 2 -cmp 2 -subcmp 2 -g 300",audioonly:!1,readonly:!0},"912806c1-6c43-44ad-ac6e-05f105bade55":{title:"iPhone",ext:"m4v",params:{"c:v":"mpeg4","c:a":"aac",s:"480x320","b:v":"800k",f:"mp4",r:"24","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"2416dcbf-146d-4ca4-b948-f6f702fb043c":{title:"iPod",ext:"m4v",params:{"c:v":"mpeg4","c:a":"aac",s:"320x240","b:v":"500k",f:"mp4",r:"24","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"42fb9cf9-94f9-45c1-954f-1c5879f3d372":{title:"Galaxy Tab",ext:"mp4",params:{"c:a":"aac","b:a":"160k",ac:"2","c:v":"h264",f:"mp4"},extra:"-crf 22",audioonly:!1,readonly:!0},"edf545c2-88fc-4354-b91d-83e2f31d3c14":{title:"MOV (QuickTime player)",ext:"mov",params:{f:"mov","c:v":"h264",preset:"fast","profile:v":"baseline","c:a":"aac","b:a":"128k"},extra:null,audioonly:!1,readonly:!0},"f31ac68e-db3b-4b17-95d7-04456cbc3c26":{title:"Mobile 3GP (Qcif)",ext:"3gp",params:{f:"3gp","c:v":"h263","c:a":"aac","b:a":"12k",s:"176x144","b:v":"64k",ar:"8000",r:"24"},extra:null,audioonly:!1,readonly:!0},"85cd71a0-fb61-45a4-9fed-6f2e6e405bc3":{title:"MPEG-2 DVD (PAL)",ext:"mpeg",params:{f:"mpeg2video",target:"pal-dvd"},extra:null,audioonly:!1,readonly:!0},"47b9b2eb-8fd4-4e10-8993-f7d467ed1928":{title:"MPEG-2 DVD (NTSC)",ext:"mpeg",params:{f:"mpeg2video",target:"ntsc-dvd"},extra:null,audioonly:!1,readonly:!0}}});async function Gw(e,t,r,{audio_only:i,ask_for_destination:n,convert_to:o},s){let a=pe=="google",l=pe=="mozilla",u=pe=="microsoft",d=r.downloadable.variants.get(r.variant_id),c=d.core_media,m=Date.now(),T=!1;{let O=r.downloadable.page_url,k=d.manifest_url;if((O.includes("youtube.")||k.includes("youtube.")||O.includes("googlevideo.")||k.includes("googlevideo."))&&(T=!0),T&&pe=="google")return j({error:"noyt",id:"noyt",downloadable_id:r.downloadable.id,report_status:"unreported"})}let f,A;{let{status:O,info:k}=await It.check();f=O,f&&(A=k.version)}let h=await B(Hn);if(l&&f&&h!==!0)return j({error:"no_privacy_accept",id:"no_privacy_accept",downloadable_id:r.downloadable.id,report_status:"unreported"});let p;if(c.builder=="YoutubeBulk")p="youtube_bulk";else if(c.builder=="Hls")p="hls";else if(c.builder=="RawHls")p="hls";else if(c.builder=="MPD")p="mpd";else if(c.builder=="HTTPMedia"){let O=await B(Gd);O=="ask"&&(O="coapp"),!f||O=="inbrowser"?p="file_inbrowser":p="file_coapp"}else if(c.builder=="YoutubeFormat")p="youtube_format";else if(c.builder=="LocalFile")p="convert_local";else{if(c.builder=="JsonMPD")throw new Error("No download strategy for builder: "+c.builder);if(c.builder=="Test")throw new Error("No download strategy for builder: "+c.builder);c.builder}if(i&&p!="hls"&&p!="mpd"&&p!="file_coapp"&&p!="youtube_bulk")return j({error:"cant_download_audio",id:"cant_download_audio",downloadable_id:r.downloadable.id,report_status:"unreported"});if(n&&!f)return j({error:"nocoapp",id:"nocoapp",downloadable_id:r.downloadable.id,report_status:"unreported"});if(p!="file_inbrowser"){if(!f)return j({error:"nocoapp",id:"nocoapp",downloadable_id:r.downloadable.id,report_status:"unreported"});let O=Xw;if(!vm.isMinimumVersion(A,O)){try{await It.call("quit")}catch{}await new Promise(J=>setTimeout(J,2e3));let{status:k,info:z}=await It.check();if(!k||!vm.isMinimumVersion(z.version,O))return It.call("quit"),j({error:"coapp_too_old",id:"coapp_too_old",downloadable_id:r.downloadable.id,report_status:"unreported"});A=z.version}}let g=!1;{let O=!1;{let{status:k}=await Ww.checkLicense();O=k=="accepted"||k=="unneeded"}if(!O){let J=await B(Xt),Oe=m-J;if(o)return j({error:"invalid_license",id:"invalid_license",downloadable_id:r.downloadable.id,report_status:"unreported"});if(i&&(p=="mpd"||p=="hls"||T))return j({error:"invalid_license_for_audio",id:"invalid_license_for_audio",downloadable_id:r.downloadable.id,report_status:"unreported"});if((a||u)&&(p=="mpd"||p=="hls"||T)&&Oe<72e5)return j({error:"download_limit",id:"download_limit",downloadable_id:r.downloadable.id,report_status:"unreported"});l&&T&&!i&&(g=!0)}}let x,w;if(w=c.container.extension,(i||!c.av.video)&&(w=c.container.audio_only_extension),o&&(w=ce(o).extension),x=await Ld(r.downloadable),x=`${x}.${w}`,p=="youtube_bulk",p=="file_inbrowser"){let O={url:"",saveAs:n,filename:x};pe=="mozilla"&&(O.incognito=r.downloadable.incognito),d.sources.video?O.url=d.sources.video:O.url=d.sources.audio;let k=await pi.default.downloads.download(O);e.set(r.downloadable.id,{inbrowser:k});let z=0,J=Date.now();for(;;){let Oe=await pi.default.downloads.search({id:k});if(Oe.length>0){let Ne=Oe[0],b=Ne.bytesReceived-z;z=Ne.bytesReceived;let E=Ne.bytesReceived/Ne.totalBytes;if(s({bitrate_bs:b,progress:E,duration_since_start:Date.now()-J}),Ne.error){console.error("No download item found");break}if(Ne.state=="complete")break}else break;await new Promise(Ne=>setTimeout(Ne,1e3))}return L({inbrowser:!0,download_id:k,filename:x})}let _=await B(Vi),S;if(n){let O=await B(ds);O.isNone()?_==="dwhelper"&&(_="~/dwhelper"):_=O.unwrap();let k=await It.call("filepicker","save_file",_,"Download media as",x),z=k.split(`
`);if(S=z[0],_=z[1],await ee(ds,C(_)),x=z[2],!S||!_||!x)return j({error:"filepicker_error",details:"no files selected. "+k,id:"filepicker_error",downloadable_id:r.downloadable.id,report_status:"unreported"});S.endsWith(w)||(S+="."+w,x+="."+w)}else{_=await wm(_);{let O=await It.call("makeUniqueFileName",_,x);S=O.filePath,x=O.fileName}_=await wm(_)}{let O=await B(Qd);if(e.size>=O){for(t.push(r.downloadable.id);;)if(await new Promise(k=>setTimeout(k,2e3)),e.size<O&&t[0]==r.downloadable.id){t.shift();break}}e.set(r.downloadable.id,{})}let M=0,I=Date.now(),U=Date.now(),W=(O,k)=>{let z=parseFloat(k.total_size),Oe=(Date.now()-I)/1e3,b=~~((z-M)/Oe),E="unknown";if(typeof c.duration=="number"){O<0&&(O=0);let v=O/c.duration;v>=0&&v<1&&(E=v)}else if(d.core_media.content_length.isSome()){let v=d.core_media.content_length.unwrap();v>0&&(E=k.total_size/v)}s({bitrate_bs:b,progress:E,duration_since_start:Date.now()-U})},q=O=>{e.set(r.downloadable.id,{ffmpeg_pid:O}),s("starting")};if(p=="hls"){let O={filePath:S,qr_code_needed:g,headers:r.downloadable.headers,on_progress:W,on_start:q};try{if(i||!c.av.video){let k=d.sources.audio||d.sources.video;await Te.sideDownload(null,k,O)}else d.sources.audio&&d.sources.video?await Te.sideDownload(d.sources.video,d.sources.audio,O):d.sources.video?await Te.sideDownload(d.sources.video,null,O):await Te.sideDownload(null,d.sources.audio,O)}catch(k){return j({error:"coapp_failure",details:k.toString(),id:`coapp_failure_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"})}}if(p=="mpd"){let O={filePath:S,qr_code_needed:g,headers:r.downloadable.headers,on_progress:W,on_start:q};try{if(i){let k=d.sources.audio||d.sources.video;await Te.sideDownloadMPD(d.manifest_url,null,k,O)}else d.sources.audio&&d.sources.video?await Te.sideDownloadMPD(d.manifest_url,d.sources.video,d.sources.audio,O):d.sources.video?await Te.sideDownloadMPD(d.manifest_url,d.sources.video,null,O):await Te.sideDownloadMPD(d.manifest_url,null,d.sources.audio,O)}catch(k){return j({error:"coapp_failure",details:k.toString(),id:`coapp_failure_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"})}}if(p=="convert_local"){let O=d.manifest_url.replace("file://",""),k=O.split(".");k.pop(),k.push(w);let z=k.join(".");try{await Te.convert3(O,z,W,q)}catch(J){return j({error:"coapp_failure",details:J.toString(),id:`coapp_failure_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"})}}if(p=="file_coapp"){let O={filePath:S,qr_code_needed:g,headers:r.downloadable.headers,on_progress:W,on_start:q};try{i?await Te.sideDownload(null,d.manifest_url,O):await Te.sideDownload(d.manifest_url,null,O)}catch(k){return j({error:"coapp_failure",details:k.toString(),id:`coapp_failure_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"})}}if(p=="youtube_format"){let O,k;if(d.sources.video)k=new URL(d.sources.video);else return j({error:"unknown",details:"Missing Video from YoutubeFormat",id:`unknown_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"});d.sources.audio&&(O=new URL(d.sources.audio));let z={filePath:S,qr_code_needed:g,headers:r.downloadable.headers,on_progress:W,on_start:q};try{if(i){let J=(O||k).href;await Te.sideDownload(null,J,z)}else O&&k?await Te.sideDownload(k.href,O.href,z):await Te.sideDownload(k.href,null,z)}catch(J){return j({error:"coapp_failure",details:J.toString(),id:`coapp_failure_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"})}}let $;if(r.downloadable.incognito?$=await B(Jd):$=await B($d),$){let O=await B(zd),k=r.downloadable.thumbnail_url;k=="/content/images/no-thumbnail.png"&&(k=pi.default.runtime.getURL(k));let z=await B(Yr),J={type:"basic",title:Pt("v9_vdh_notification",[],z),message:Pt("v9_file_ready",[x],z)};O&&(J.iconUrl=k),pi.default.notifications.create(r.downloadable.id,J)}{let O=await B(Xt);O<2?await ee(Xt,O+1):await ee(Xt,m)}return L({inbrowser:!1,filepath:S,filename:x,filedir:_,qrcode:g})}async function Am(e,t,r,i,n){try{let o=await Gw(e,t,r,i,n);return e.delete(r.downloadable.id),o}catch(o){return e.delete(r.downloadable.id),j({error:"unknown",details:o.toString(),id:`unknown_${crypto.randomUUID()}`,downloadable_id:r.downloadable.id,report_status:"unreported"})}}async function xm(e){e.inbrowser&&pi.default.downloads.cancel(e.inbrowser),e.ffmpeg_pid&&It.call("abortConvert",e.ffmpeg_pid)}var pi,It,vm,Ww,Te,Xw,wm,Tm=N(()=>{"use strict";Qo();Go();ae();pi=yt(Ft(),1);Ge();es();Kr();It=(mt(),P(ft)),vm=(he(),P(ge)),Ww=(Yt(),P(Kt)),Te=(ci(),P(di)),Xw="2.0.19",wm=async e=>{try{e=await It.call("path.homeJoin",e),await It.call("fs.mkdirp",e)}catch(t){console.error("mkdir error",t,e)}return e}});function Em(e,t){for(let r of e.downloadable.values())if(r.tab_id==e.current_tab_id)return!0;for(let r of e.downloading.values())if(r.downloadable.tab_id==e.current_tab_id)return!0;if(!t.hide_downloaded){for(let r of e.downloaded.values())if(r.downloadable.tab_id==e.current_tab_id)return!0}return!1}function Nl(e,t){let r=kl(e,t);for(let[i,n]of r)if(n.reach==="downloadable"&&n.is_visible&&n.is_current_tab)return C(i);return D}function kl(e,t){let r=new Map,i=[];{for(let l of e.downloadable.values()){r.has(l.tab_id)||r.set(l.tab_id,{downloadables:[],filter_out_low_quality:!1});let u=r.get(l.tab_id);u.downloadables.push(l),l.is_low_quality||(u.filter_out_low_quality=!0)}for(let l of[...e.downloading.values(),...e.downloaded.values()]){let u=r.get(l.downloadable.tab_id);u&&!l.downloadable.is_low_quality&&(u.filter_out_low_quality=!0)}for(let[l,u]of r.entries()){let d=l==e.current_tab_id,c=d||l=="none"||t.all_tabs;for(let m of u.downloadables){let T={order:0,is_current_tab:d,id:m.id,timestamp:m.timestamp,reach:"downloadable",is_visible:c};u.filter_out_low_quality&&m.is_low_quality&&!t.low_quality&&(T.is_visible=!1),i.push([m,T])}}}let n=[...e.downloading.values()].map(l=>[l,{order:0,id:l.downloadable.id,is_current_tab:l.downloadable.tab_id==e.current_tab_id,timestamp:l.downloadable.timestamp,reach:"downloading",is_visible:!0}]),o=[...e.downloaded.values()].map(l=>[l,{order:0,id:l.downloadable.id,is_current_tab:l.downloadable.tab_id==e.current_tab_id,timestamp:l.downloadable.timestamp,reach:"downloaded",is_visible:!t.hide_downloaded}]),s;{let l=([,u],[,d])=>d.timestamp-u.timestamp;t.sort_by_status?(i.sort(l),n.sort(l),o.sort(l),s=[...i,...n,...o]):(s=[...i,...n,...o],s.sort(l)),t.sort_reverse&&s.reverse()}let a=0;for(let l of s)l[1].is_visible&&(l[1].order=a++);return s}var Sm=N(()=>{"use strict";ae()});async function Im(){ql=void 0;let t=[...await B(Fn),...Yo].slice(-500);await ee(Fn,t),Yo.length=0}function de(e){Cl&&(Yo.push({timestamp:Date.now(),message:e}),ql||(ql=setTimeout(Im,500)))}function km(e){V.default.runtime.sendMessage(e).catch(t=>{})}function Jw(e,t){V.default.tabs.sendMessage(e,t).catch(r=>{})}async function Kw(e,t){if(!e.inbrowser&&await B(Zd)){let r=Date.now(),i={download_result:structuredClone(e),page_url:t.page_url,timestamp:r},n=await B(Li);n.set(t.id,i);{let s=await B(ec)*1e3*60*60*24;for(let[a,l]of n.entries())r-l.timestamp>s&&n.delete(a)}await ee(Li,n)}}async function Fl(e){if(!await B(is)){V.default.contextMenus.update("vdh-top",{visible:!1});return}V.default.contextMenus.update("vdh-top",{visible:!0});let t=[];if(e.downloadable.size>0){let r=await B(_r),i=kl(e,r);for(let[n,o]of i){if(o.reach!="downloadable"||!o.is_visible)continue;let s=n;t.push({title:s.title,enabled:!1});for(let a of s.variants.values()){let l=a.core_media.container.name+" - "+a.core_media.builder;if(a.core_media.av.video&&a.core_media.av.video.dimensions.isSome()){let u=a.core_media.av.video.dimensions.unwrap();l+=" - "+u.width.toString()+"x"+u.height.toString()}typeof a.core_media.duration=="number"&&(l+=" - "+im(a.core_media.duration)),t.push({title:l,enabled:!0,onclick:()=>{let u={downloadable_id:s.id,variant_id:a.id,audio_only:!1,ask_for_destination:!1,convert_to:void 0};nn(e,u)}})}}}t.length==0&&t.push({title:"no media",enabled:!1});for(let r=0;r<Rm;r++)r<t.length?V.default.contextMenus.update("vdh-sub-"+r,{...t[r],visible:!0}):V.default.contextMenus.update("vdh-sub-"+r,{visible:!1});V.default.contextMenus.update("vdh-blacklist",{onclick:()=>{V.default.tabs.create({url:"/content2/blacklist.html"})}}),V.default.contextMenus.update("vdh-smartnaming",{onclick:async()=>{let r=await B(_r),i=Nl(e,r);if(i.isSome()){let n=i.unwrap().id;V.default.tabs.create({url:`/content2/smartnaming_editor.html?id=${n}`})}}})}async function Ll(e){let t=await B(_r),i={size:150,radius:40,spread:30,greyed:!Em(e,t),channel:yl};e.download_errors.size>0?(V.default.action.setBadgeText({text:e.download_errors.size.toString()}),V.default.action.setBadgeBackgroundColor({color:[255,0,0,190]}),V.default.action.setBadgeTextColor({color:"white"})):e.downloading.size>0?(V.default.action.setBadgeText({text:e.downloading.size.toString()}),V.default.action.setBadgeBackgroundColor({color:"#0284c7"}),V.default.action.setBadgeTextColor({color:"white"})):V.default.action.setBadgeText({text:""});let n=Yf(i,$w);V.default.action.setIcon({imageData:n})}function ue(e){fi&&(clearTimeout(fi),fi=void 0),ee(cs,e),Ll(e),Fl(e)}function Nt(e){typeof fi>"u"&&(fi=setTimeout(()=>{fi=void 0,ue(e)},200))}async function Zt(e,t){if(!be)return;if(e.is_low_quality){let a=Hl.get(e.page_url)??0;if(a>30)return;Hl.set(e.page_url,a+1)}for(let a of $o)a.mutateDownloadable&&a.mutateDownloadable(e);let r=[...e.variants.values()];for(let a of Vl)if(e.page_url.match(a)||r[0].manifest_url.match(a))return;if(be.downloadable.has(e.id)||be.downloading.has(e.id)||be.downloaded.has(e.id))return;let n=new Map;for(let a of r){let l=a.core_media.container.name;n.has(l)||n.set(l,[]),n.get(l).push(a)}let o=await B(Fi),s=[...n.values()];s.sort((a,l)=>{let u=a[0],d=l[0];return Ci(u.core_media,d.core_media,o)}),e.variants=new Map;for(let a of s){a.sort((l,u)=>Ci(l.core_media,u.core_media,o)),a=a.slice(0,o.max_variants);for(let l of a)e.variants.set(l.id,l)}if(be.downloadable.set(e.id,e),t){let a=e.variants.values().next().value.id;nn(be,{downloadable_id:e.id,variant_id:a,audio_only:!1,ask_for_destination:!1,convert_to:void 0})}Nt(be)}async function Zo(e,t){e.license_status={checking:!0},ue(e);let r;if(!t||t.length==0?r=await Om.checkLicense():r=await Om.validateLicense(t),r.status=="accepted"?(e.license_status={accepted:!0,email:r.email,key:r.key},!await B(ss)&&(e.user_messages.add("license_now_valid"),await ee(ss,!0)),e.download_errors.delete("invalid_license"),e.download_errors.delete("invalid_license_for_audio"),e.download_errors.delete("download_limit")):e.user_messages.delete("license_now_valid"),r.status=="invalid"&&(e.license_status={invalid:!0,key:r.key}),r.status=="unneeded"&&(e.license_status={unneeded:!0}),r.status=="nocoapp"&&(e.license_status={nocoapp:!0}),r.status=="mismatch"){let i=r.brExt,n=r.brLicense;e.license_status={mismatch:!0,key:r.key,other_browser:n,this_browser:i}}r.status=="unset"&&(e.license_status={unset:!0}),r.status=="blocked"&&(e.license_status={blocked:!0,key:r.key}),r.status=="locked"&&(e.license_status={locked:!0,key:r.key}),Nt(e)}async function Cm(e,t){let r=structuredClone(e.coapp_status);e.coapp_status="checking",ue(e),t&&await new Promise(a=>setTimeout(a,1e3));let{status:i,info:n,error:o}=await Cr.check();if(i){let a;Pi(n.version,Dm)<0?a=Dm:a=!1,e.download_errors.delete("nocoapp"),e.coapp_status={found:!0,path:n.binary,version:n.version,new_version:a}}else e.coapp_status={found:!1,error:o};r!="checking"&&r.found!=e.coapp_status.found?Zo(e):Nt(e)}async function nn(e,t){de("Service:HandleDownloadMessage");let r=t.downloadable_id,i=t.variant_id,n=e.downloadable.get(r);if(!n){de("DB inconsistency"),console.error("DB inconsistency",n,r);return}e.downloadable.delete(r);let o={downloadable:n,variant_id:i,progress:"queued"};e.downloading.set(r,o),ue(e),de("downloading set - to call do_download");let s=await Am(Nm,zw,o,t,a=>{typeof a!="string"&&typeof a.progress=="number"&&(a.progress>1||a.progress<0)&&(a.progress="unknown"),o.progress=a,km({progress_changed:{progress:a,downloadable_id:r}}),Ll(e)});if(e.downloading.delete(r),s.isOk()){let a=await B(Ln);await ee(Ln,a+1),a>100&&(await ee(Ln,0),await B(ls)||e.user_messages.add("one_hundred_downloads"));let l=s.unwrap(),u=await B(Yd);if(!l.inbrowser&&l.qrcode&&u){let m={id:"qrcode",downloadable_id:r,error:"qrcode",report_status:"unreported"};e.download_errors.set(m.id,m)}!await B(as)&&(e.user_messages.add("auto_hide_downloaded"),ee(as,!0));let c={downloadable:n,variant_id:i,download_result:l};e.downloaded.set(r,c),await Kw(l,n)}else{e.downloadable.set(n.id,n);let a=s.unwrapErr();a.error=="filepicker_error"||(a.error=="no_privacy_accept"?e.user_messages.add("privacy_accept"):e.download_errors.set(a.id,a))}ue(e)}async function Yw(e){de("StartListeners"),V.default.commands.onCommand.addListener(async t=>{if(t=="default-action"){let r=await B(_r),i=Nl(e,r);if(i.isNone())return;let n=await B(ns),o=i.unwrap(),s=o.variants.values().next().value;if(n=="copy")pe=="mozilla"?navigator.clipboard.writeText(s.to_copy):V.default.scripting.executeScript({target:{tabId:o.tab_id},func:a=>navigator.clipboard.writeText(a),args:[s.to_copy]});else{let a={downloadable_id:o.id,variant_id:s.id,audio_only:n=="download_audio",ask_for_destination:n=="download_as",convert_to:void 0};nn(e,a)}}}),V.default.browserAction?.onClicked?.addListener(()=>{Bl?V.default.sidebarAction.toggle():V.default.browserAction.openPopup()}),V.default.runtime.onSuspend.addListener(()=>{de("OnSuspend"),Cr.call("quit")}),V.default.tabs.onUpdated.addListener((t,r,i)=>{if("url"in r){let n=Date.now();for(let o of e.downloadable.values())o.tab_id==t&&n-o.timestamp>2e3&&(e.downloadable.delete(o.id),Hl.delete(o.page_url),Nt(e))}}),V.default.tabs.onActivated.addListener(t=>{e.current_tab_id=t.tabId,e.current_window_id=t.windowId,ue(e)}),V.default.windows.onFocusChanged.addListener(async t=>{let i=(await V.default.tabs.query({active:!0,currentWindow:!0}))[0];i&&(e.current_tab_id=i.id??-1,e.current_window_id=i.windowId??-1,ue(e))}),V.default.tabs.onRemoved.addListener(async t=>{let r=!1;if(e.yt_bulk.isSome()&&e.yt_bulk.unwrap().tab_id==t&&(r=!0,e.yt_bulk=D,e.user_messages.delete("yt_bulk_detected")),await B(Kd))for(let i of e.downloadable.values())i.tab_id==t&&(e.downloadable.delete(i.id),r=!0);r&&ue(e)}),V.default.runtime.onMessage.addListener(async(t,r)=>{let i=typeof t=="object"&&"debugger_new_logs"in t;i=i||typeof t=="string"&&t=="debugger_request_logs",i||de(`Service:onMessage - ${JSON.stringify(t)}`);let n=t;if(typeof n=="string"){if(n=="request_license_status"&&r.tab?.id&&Jw(r.tab.id,{license_status:e.license_status}),n=="select_download_directory"){let o=await B(Yr),s=Pt("v9_filepicker_select_download_dir",[],o),l=(await Cr.call("filepicker","pick_folder","~",s)).split(`
`)[0];typeof l=="string"&&l.length>0&&ee(Vi,l);return}if(n=="incognito_check"&&(await V.default.extension.isAllowedIncognitoAccess()?e.user_messages.delete("no_incognito"):await B(os)||(await ee(os,!0),e.user_messages.add("no_incognito"),Nt(e))),n=="leave_review"&&(e.user_messages.delete("one_hundred_downloads"),pe=="mozilla"?V.default.tabs.create({url:"https://addons.mozilla.org/firefox/addon/video-downloadhelper"}):pe=="google"?V.default.tabs.create({url:"https://chrome.google.com/webstore/detail/video-downloadhelper/lmjnegcaeklhafolokijcfjliaokphfk"}):pe=="microsoft"&&V.default.tabs.create({url:"https://microsoftedge.microsoft.com/addons/detail/video-downloadhelper/jmkaglaafmhbcpleggkmaliipiilhldn"}),ue(e)),n=="never_ask_for_review"&&(e.user_messages.delete("one_hundred_downloads"),await ee(ls,!0),ue(e)),n=="bulk_download"&&(e.user_messages.delete("yt_bulk_detected"),e.yt_bulk.isSome())){let o=e.yt_bulk.unwrap();for(let s of o.ids){let a=`https://www.youtube.com/watch?v=${s}&vdh-bulk=1`,l=await V.default.tabs.create({url:a,active:!1});V.default.tabs.update(l.id,{muted:!0}).then(async()=>{for(let u=0;u<30;u++){await new Promise(d=>setTimeout(d,2e3));for(let d of e.downloading.values())d.downloadable.tab_id==l.id&&V.default.tabs.remove(l.id)}})}}}else{if("yt_selection"in n&&pe!="google"){let o=r.tab?.id;o&&(e.yt_bulk=C({ids:n.yt_selection,tab_id:o}),e.user_messages.add("yt_bulk_detected"),Nt(e));return}if("coapp_check"in n){Cm(e,n.coapp_check);return}if("rm_error"in n){e.download_errors.delete(n.rm_error),ue(e);return}if("privacy_accept"in n&&(await ee(Hn,n.privacy_accept),e.user_messages.delete("privacy_accept"),ue(e)),"rm_user_message"in n){e.user_messages.delete(n.rm_user_message),ue(e);return}if("clean"in n){let o=n.clean;e.downloadable.clear(),o&&e.downloaded.clear(),ue(e);return}if("license_check"in n){Zo(e,n.license_check);return}if("report_error"in n){let o=n.report_error,s=e.download_errors.get(o);if(s&&s.details){s.report_status="reporting",ue(e);try{let a=await V.default.runtime.getPlatformInfo(),l=V.default.runtime.getManifest(),u=l.version_name??l.version,d=e.downloadable.get(s.downloadable_id);if(d){let c=nt(d);delete c.headers;let m={"vdh-bug-report":!0,dable:c,channel:yl,target:pe,ua:navigator.userAgent,platform:{arg:a.arch,os:a.os},version:u,lang:V.default.i18n.getUILanguage(),details:s.details};await fetch("https://api.downloadhelper.net/v1/reports",{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},redirect:"follow",referrerPolicy:"no-referrer",body:JSON.stringify(m)})}s.report_status="reported"}catch{s.report_status="reported"}Nt(e)}}if("forget"in n){let o=n.forget,s=e.downloadable.get(o)||e.downloaded.get(o)?.downloadable;if(e.downloadable.delete(o),e.downloaded.delete(o),s&&!s.is_low_quality&&!(await B(_r)).low_quality)for(let l of e.downloadable.values())s.tab_id==l.tab_id&&l.is_low_quality&&e.downloadable.delete(l.id);ue(e)}if("stop"in n){let o=n.stop,s=Nm.get(o),a=e.downloading.get(o);if(!s||!a){de("Can't find abordable download");return}xm(s),a.progress="stopping",km({progress_changed:{progress:a.progress,downloadable_id:o}})}if("retry"in n){let o=n.retry,a={...e.downloaded.get(o).downloadable};a.timestamp=Date.now(),a.id=`downloadable_${crypto.randomUUID()}`,a.tab_id="none",a.is_low_quality=!1,await Zt(a),ue(e)}if("convert_local_to"in n){let o=n.convert_local_to,s=await B(Yr),a=Pt("v9_filepicker_select_file",[],s),u=(await Cr.call("filepicker","pick_file","~",a)).split(`
`),d=u[0];if(d){let c=u[2],m=`downloadable_${pt(d)}`,T=`variant_${pt(d)}`,f=d.split(".").pop(),A=Ni(f);if(A.isSome()){let[h,p]=A.unwrap(),g={content_length:D,builder:"LocalFile",protocol:"unknown",duration:"unknown",container:h,av:{audio:!1,video:Wt()}},x={id:T,manifest_url:"file://"+d,core_media:g,sources:{audio:!1,video:"file://"+d},to_copy:d},w={id:m,tab_id:"none",timestamp:Date.now(),incognito:!1,page_url:"about:blank",page_title:c,title:c,favicon_url:"about:blank",thumbnail_url:"/content/images/no-thumbnail.png",headers:[],is_low_quality:!1,variants:new Map([[T,x]])};e.downloadable.set(m,w);let _={downloadable_id:m,variant_id:T,audio_only:p=="audio_only",ask_for_destination:!1,convert_to:o};await nn(e,_)}}}if("rm"in n){let o=n.rm,s=e.downloaded.get(o).download_result;s.inbrowser?(await V.default.downloads.removeFile(s.download_id),await V.default.downloads.erase({id:s.download_id})):Cr.call("fs.unlink",s.filepath),e.downloaded.delete(o),ue(e);let a=await B(Li);a.delete(o),await ee(Li,a)}if("play"in n){let o=n.play,s=e.downloaded.get(o).download_result;s.inbrowser||Cr.call("open",s.filepath)}if("show_dir"in n){let o=n.show_dir,s=e.downloaded.get(o).download_result;s.inbrowser?V.default.downloads.show(s.download_id):Cr.call("open",s.filedir)}if("download"in n){let o=n.download;await nn(e,o)}}})}async function Zw(e){await V.default.contextMenus.removeAll();let t=i=>new Promise(n=>V.default.contextMenus.create(i,n));await t({id:"vdh-top",title:"Video DownloadHelper",contexts:["page"]});for(let i=0;i<Rm;i++)await t({parentId:"vdh-top",title:"---",id:"vdh-sub-"+i});await t({parentId:"vdh-top",id:"vdh-separator",type:"separator"});let r=await B(Yr);await t({parentId:"vdh-top",id:"vdh-blacklist",title:Pt("v9_menu_item_blacklist",[],r)}),await t({parentId:"vdh-top",id:"vdh-smartnaming",title:Pt("v9_menu_item_smartnaming",[],r)}),hr(is,()=>Fl(e)),hr(_r,()=>{Ll(e),Fl(e)})}function eA(){if(!be)return;let e=Date.now();for(let t of be.downloadable.values())e-t.timestamp>Qw&&(be.downloadable.delete(t.id),Nt(be))}function Mm(e){e=["https://www.youtube.com/s/search/audio/*.mp3","https://*.xvideos-cdn.com/videos/videopreview/*.mp4","https://*.phncdn.com/videos/*.webm*","https://ev-ph.rdtcdn.com/videos/*.mp4*","https://ev-ph.ypncdn.com/videos/*.mp4*","https://thumb-*.xhcdn.com/*","https://*.sacdnssedge.com/*",...e],Vl=[];for(let t of e)if(t.length!=0){t=t.replace(/[.+?^${}()|[\]\\]/g,"\\$&"),t=t.replaceAll("*",".*");try{Vl.push(new RegExp(t))}catch{de("Failed to compile regex: "+t)}}}function Pm(e){Bl=e,wl(Bl,be.current_window_id,!1)}async function tA(){Cl=await B(Bi),V.default.runtime.onMessage.addListener(async(e,t)=>{let r=e;if(typeof r=="string"){if(r=="debugger_toggle"){let i=await B(Bi);ee(Bi,!i)}else if(r=="debugger_restart_addon")V.default.runtime.reload();else if(r=="debugger_request_logs"&&t.tab?.id){let i=await B(Bi),n="";if(!i)n="debugger disabled";else if(!Cl)n="restart required";else if(be){let o=V.default.runtime.lastError?.toString()||"(lastError empty)",s=nt(be);"license_status"in s&&s.license_status&&typeof s.license_status=="object"&&("key"in s.license_status&&delete s.license_status.key,"email"in s.license_status&&delete s.license_status.email);let a=JSON.stringify(s,null,2),l=(await B(Fn)).sort((u,d)=>u.timestamp-d.timestamp).map(u=>`${u.timestamp} : ${u.message}`).join(`
`);n=o+`
`+l+`
`+a}V.default.tabs.sendMessage(t.tab.id,{all_logs:n})}}else"debugger_new_logs"in r&&(Yo.push(...r.debugger_new_logs),Im())})}var V,Dm,Rm,Qw,Cr,Om,Yo,Cl,ql,Nm,zw,$w,Bl,fi,Vl,Hl,be,Ul=N(()=>{"use strict";V=yt(Ft(),1);Ge();mr();Go();Zf();Ii();rm();Fa();nm();ae();Qo();zo();Vn();Al();Kr();Tm();Sm();Dm="2.0.19",Rm=30,Qw=30*60*1e3,Cr=(mt(),P(ft)),Om=(Yt(),P(Kt)),Yo=[],Cl=!0;Nm=new Map,zw=[],$w=new Map,Bl=!1,Vl=[],Hl=new Map;(async()=>{try{await tA(),de("Main()");try{await rc()}catch(r){de("Main() - storage_migrate failed - "+r.toString())}let e=await B(rs);try{let r=await B(Hi);if(Xd(Hi,()=>V.default.runtime.reload()),r){tm();return}else vl(e)}catch(r){de("Main() - handle legacy UI failed - "+r.toString()),vl(e),de("Main() - fallback to UseNewUI")}let t=await B(cs);be=t;try{await Zw(t)}catch(r){de("Main() - RegisterContextMenus failed - "+r.toString())}Yw(t),Cm(t,!1),Zo(t);try{Pm(e)}catch(r){de("Main() - OnSidebarChanged failed - "+r.toString())}hr(rs,Pm);try{Mm(await B(us))}catch(r){de("Main() - CompileBlacklist failed - "+r.toString())}hr(us,Mm),hr(tc,()=>Zo(t));try{let i=(await V.default.tabs.query({active:!0,currentWindow:!0}))[0];i&&(t.current_tab_id=i.id??-1,t.current_window_id=i.windowId??-1)}catch(r){de("Main() - Getting initial tab failed - "+r.toString())}setInterval(eA,60*1e3),Nt(t),de("Main() - End")}catch(e){de("Main() - Failed - "+e.toString())}})()});var on={};ne(on,{defineInPage:()=>nA,getFilenameFromTitle:()=>aA,getSpecs:()=>oA,set:()=>iA});async function ea(){let e=await qr;await jl.storage.local.set({smartname:e})}async function iA(e){qr=Promise.resolve(e),ea()}async function nA(){let e=await jl.tabs.query({active:!0,currentWindow:!0});if(e.length===0)throw new Error("Can't find current tab");rA.executeScriptWithGlobal({tabId:e[0].id},{},"/injected/smartname.js")}async function oA(e){let t=await qr,r=new URL(e).hostname.split(".");for(let i=0;i<r.length-1;i++){let n=t[r.slice(i).join(".")];if(n)return n}return null}async function aA(e,t=null){e=e.trim(),t&&e.endsWith(`.${t}`)&&(t=null);let r={keep:" ",remove:"",hyphen:"-",underscore:"_"},i=await Ye.prefs;t&&(t=t.replace(qm,""),t=t.replace(Bm,r[i.smartnamerFnameSpaces])),e=e.replace(qm,""),e=e.replace(Bm,r[i.smartnamerFnameSpaces]);let n=i.smartnamerFnameMaxlen;return t?(e.length+t.length+1>n&&(e=e.substr(0,n-t.length-1)),e+"."+t):(e.length>n&&(e=e.substr(0,n)),e)}var Ye,rA,jl,qr,qm,Bm,an=N(()=>{"use strict";Ye=te(),rA=(he(),P(ge)),jl=Ye.browser,qr=jl.storage.local.get({smartname:{}}).then(e=>e.smartname);qm=new RegExp('[/?<>\\:*|":]|[\0-\x80-\x9F]|\\\\',"g"),Bm=new RegExp(" +","g");Ye.rpc.listen({openSmartNameDefiner:async()=>{let e=await Ye.ui.open("smartname-definer",{url:"content/smartname-define.html",type:"panel",width:600,height:400});return await Ye.wait("smartname-definer"),e},closeSmartNameDefiner:()=>Ye.ui.close("smartname-definer"),closedSmartNameDefiner:e=>Ye.rpc.call(e,"close"),setSmartNameData:e=>Ye.rpc.call("smartname-definer","setData",e),evaluateSmartName:(e,t)=>Ye.rpc.call(e,"evaluate",t),addSmartNameRule:async e=>{let t=await qr;t[e.domain]=e,ea()},selectSmartNameXPath:(e,t)=>Ye.rpc.call(e,"select",t),setSmartName:async e=>{qr=Promise.resolve({}),ea()},getSmartNameRules:async()=>qr,editSmartName:()=>{Ye.ui.open("smartname-edit",{type:"tab",url:"content/smartname-edit.html"})},removeFromSmartName:async e=>{let t=await qr;delete t[e],ea()}})});var ta=y(kt=>{"use strict";var Vm=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",sA=Vm+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",Hm="["+Vm+"]["+sA+"]*",lA=new RegExp("^"+Hm+"$"),uA=function(e,t){let r=[],i=t.exec(e);for(;i;){let n=[];n.startIndex=t.lastIndex-i[0].length;let o=i.length;for(let s=0;s<o;s++)n.push(i[s]);r.push(n),i=t.exec(e)}return r},dA=function(e){let t=lA.exec(e);return!(t===null||typeof t>"u")};kt.isExist=function(e){return typeof e<"u"};kt.isEmptyObject=function(e){return Object.keys(e).length===0};kt.merge=function(e,t,r){if(t){let i=Object.keys(t),n=i.length;for(let o=0;o<n;o++)r==="strict"?e[i[o]]=[t[i[o]]]:e[i[o]]=t[i[o]]}};kt.getValue=function(e){return kt.isExist(e)?e:""};kt.isName=dA;kt.getAllMatches=uA;kt.nameRegexp=Hm});var Xl=y(Wm=>{"use strict";var Wl=ta(),cA={allowBooleanAttributes:!1,unpairedTags:[]};Wm.validate=function(e,t){t=Object.assign({},cA,t);let r=[],i=!1,n=!1;e[0]==="\uFEFF"&&(e=e.substr(1));for(let o=0;o<e.length;o++)if(e[o]==="<"&&e[o+1]==="?"){if(o+=2,o=Lm(e,o),o.err)return o}else if(e[o]==="<"){let s=o;if(o++,e[o]==="!"){o=Um(e,o);continue}else{let a=!1;e[o]==="/"&&(a=!0,o++);let l="";for(;o<e.length&&e[o]!==">"&&e[o]!==" "&&e[o]!=="	"&&e[o]!==`
`&&e[o]!=="\r";o++)l+=e[o];if(l=l.trim(),l[l.length-1]==="/"&&(l=l.substring(0,l.length-1),o--),!yA(l)){let c;return l.trim().length===0?c="Invalid space after '<'.":c="Tag '"+l+"' is an invalid name.",oe("InvalidTag",c,Ee(e,o))}let u=mA(e,o);if(u===!1)return oe("InvalidAttr","Attributes for '"+l+"' have open quote.",Ee(e,o));let d=u.value;if(o=u.index,d[d.length-1]==="/"){let c=o-d.length;d=d.substring(0,d.length-1);let m=jm(d,t);if(m===!0)i=!0;else return oe(m.err.code,m.err.msg,Ee(e,c+m.err.line))}else if(a)if(u.tagClosed){if(d.trim().length>0)return oe("InvalidTag","Closing tag '"+l+"' can't have attributes or invalid starting.",Ee(e,s));{let c=r.pop();if(l!==c.tagName){let m=Ee(e,c.tagStartPos);return oe("InvalidTag","Expected closing tag '"+c.tagName+"' (opened in line "+m.line+", col "+m.col+") instead of closing tag '"+l+"'.",Ee(e,s))}r.length==0&&(n=!0)}}else return oe("InvalidTag","Closing tag '"+l+"' doesn't have proper closing.",Ee(e,o));else{let c=jm(d,t);if(c!==!0)return oe(c.err.code,c.err.msg,Ee(e,o-d.length+c.err.line));if(n===!0)return oe("InvalidXml","Multiple possible root nodes found.",Ee(e,o));t.unpairedTags.indexOf(l)!==-1||r.push({tagName:l,tagStartPos:s}),i=!0}for(o++;o<e.length;o++)if(e[o]==="<")if(e[o+1]==="!"){o++,o=Um(e,o);continue}else if(e[o+1]==="?"){if(o=Lm(e,++o),o.err)return o}else break;else if(e[o]==="&"){let c=_A(e,o);if(c==-1)return oe("InvalidChar","char '&' is not expected.",Ee(e,o));o=c}else if(n===!0&&!Fm(e[o]))return oe("InvalidXml","Extra text at the end",Ee(e,o));e[o]==="<"&&o--}}else{if(Fm(e[o]))continue;return oe("InvalidChar","char '"+e[o]+"' is not expected.",Ee(e,o))}if(i){if(r.length==1)return oe("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",Ee(e,r[0].tagStartPos));if(r.length>0)return oe("InvalidXml","Invalid '"+JSON.stringify(r.map(o=>o.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1})}else return oe("InvalidXml","Start tag expected.",1);return!0};function Fm(e){return e===" "||e==="	"||e===`
`||e==="\r"}function Lm(e,t){let r=t;for(;t<e.length;t++)if(e[t]=="?"||e[t]==" "){let i=e.substr(r,t-r);if(t>5&&i==="xml")return oe("InvalidXml","XML declaration allowed only at the start of the document.",Ee(e,t));if(e[t]=="?"&&e[t+1]==">"){t++;break}else continue}return t}function Um(e,t){if(e.length>t+5&&e[t+1]==="-"&&e[t+2]==="-"){for(t+=3;t<e.length;t++)if(e[t]==="-"&&e[t+1]==="-"&&e[t+2]===">"){t+=2;break}}else if(e.length>t+8&&e[t+1]==="D"&&e[t+2]==="O"&&e[t+3]==="C"&&e[t+4]==="T"&&e[t+5]==="Y"&&e[t+6]==="P"&&e[t+7]==="E"){let r=1;for(t+=8;t<e.length;t++)if(e[t]==="<")r++;else if(e[t]===">"&&(r--,r===0))break}else if(e.length>t+9&&e[t+1]==="["&&e[t+2]==="C"&&e[t+3]==="D"&&e[t+4]==="A"&&e[t+5]==="T"&&e[t+6]==="A"&&e[t+7]==="["){for(t+=8;t<e.length;t++)if(e[t]==="]"&&e[t+1]==="]"&&e[t+2]===">"){t+=2;break}}return t}var pA='"',fA="'";function mA(e,t){let r="",i="",n=!1;for(;t<e.length;t++){if(e[t]===pA||e[t]===fA)i===""?i=e[t]:i!==e[t]||(i="");else if(e[t]===">"&&i===""){n=!0;break}r+=e[t]}return i!==""?!1:{value:r,index:t,tagClosed:n}}var gA=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function jm(e,t){let r=Wl.getAllMatches(e,gA),i={};for(let n=0;n<r.length;n++){if(r[n][1].length===0)return oe("InvalidAttr","Attribute '"+r[n][2]+"' has no space in starting.",sn(r[n]));if(r[n][3]!==void 0&&r[n][4]===void 0)return oe("InvalidAttr","Attribute '"+r[n][2]+"' is without value.",sn(r[n]));if(r[n][3]===void 0&&!t.allowBooleanAttributes)return oe("InvalidAttr","boolean attribute '"+r[n][2]+"' is not allowed.",sn(r[n]));let o=r[n][2];if(!bA(o))return oe("InvalidAttr","Attribute '"+o+"' is an invalid name.",sn(r[n]));if(!i.hasOwnProperty(o))i[o]=1;else return oe("InvalidAttr","Attribute '"+o+"' is repeated.",sn(r[n]))}return!0}function hA(e,t){let r=/\d/;for(e[t]==="x"&&(t++,r=/[\da-fA-F]/);t<e.length;t++){if(e[t]===";")return t;if(!e[t].match(r))break}return-1}function _A(e,t){if(t++,e[t]===";")return-1;if(e[t]==="#")return t++,hA(e,t);let r=0;for(;t<e.length;t++,r++)if(!(e[t].match(/\w/)&&r<20)){if(e[t]===";")break;return-1}return t}function oe(e,t,r){return{err:{code:e,msg:t,line:r.line||r,col:r.col}}}function bA(e){return Wl.isName(e)}function yA(e){return Wl.isName(e)}function Ee(e,t){let r=e.substring(0,t).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function sn(e){return e.startIndex+e[1].length}});var Gm=y(Gl=>{var Xm={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,r){return e}},vA=function(e){return Object.assign({},Xm,e)};Gl.buildOptions=vA;Gl.defaultOptions=Xm});var zm=y((TS,Qm)=>{"use strict";var Ql=class{constructor(t){this.tagname=t,this.child=[],this[":@"]={}}add(t,r){t==="__proto__"&&(t="#__proto__"),this.child.push({[t]:r})}addChild(t){t.tagname==="__proto__"&&(t.tagname="#__proto__"),t[":@"]&&Object.keys(t[":@"]).length>0?this.child.push({[t.tagname]:t.child,":@":t[":@"]}):this.child.push({[t.tagname]:t.child})}};Qm.exports=Ql});var Jm=y((ES,$m)=>{var wA=ta();function AA(e,t){let r={};if(e[t+3]==="O"&&e[t+4]==="C"&&e[t+5]==="T"&&e[t+6]==="Y"&&e[t+7]==="P"&&e[t+8]==="E"){t=t+9;let i=1,n=!1,o=!1,s="";for(;t<e.length;t++)if(e[t]==="<"&&!o){if(n&&EA(e,t))t+=7,[entityName,val,t]=xA(e,t+1),val.indexOf("&")===-1&&(r[MA(entityName)]={regx:RegExp(`&${entityName};`,"g"),val});else if(n&&SA(e,t))t+=8;else if(n&&DA(e,t))t+=8;else if(n&&OA(e,t))t+=9;else if(TA)o=!0;else throw new Error("Invalid DOCTYPE");i++,s=""}else if(e[t]===">"){if(o?e[t-1]==="-"&&e[t-2]==="-"&&(o=!1,i--):i--,i===0)break}else e[t]==="["?n=!0:s+=e[t];if(i!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:r,i:t}}function xA(e,t){let r="";for(;t<e.length&&e[t]!=="'"&&e[t]!=='"';t++)r+=e[t];if(r=r.trim(),r.indexOf(" ")!==-1)throw new Error("External entites are not supported");let i=e[t++],n="";for(;t<e.length&&e[t]!==i;t++)n+=e[t];return[r,n,t]}function TA(e,t){return e[t+1]==="!"&&e[t+2]==="-"&&e[t+3]==="-"}function EA(e,t){return e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="N"&&e[t+4]==="T"&&e[t+5]==="I"&&e[t+6]==="T"&&e[t+7]==="Y"}function SA(e,t){return e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="L"&&e[t+4]==="E"&&e[t+5]==="M"&&e[t+6]==="E"&&e[t+7]==="N"&&e[t+8]==="T"}function DA(e,t){return e[t+1]==="!"&&e[t+2]==="A"&&e[t+3]==="T"&&e[t+4]==="T"&&e[t+5]==="L"&&e[t+6]==="I"&&e[t+7]==="S"&&e[t+8]==="T"}function OA(e,t){return e[t+1]==="!"&&e[t+2]==="N"&&e[t+3]==="O"&&e[t+4]==="T"&&e[t+5]==="A"&&e[t+6]==="T"&&e[t+7]==="I"&&e[t+8]==="O"&&e[t+9]==="N"}function MA(e){if(wA.isName(e))return e;throw new Error(`Invalid entity name ${e}`)}$m.exports=AA});var Ym=y((SS,Km)=>{var PA=/^[-+]?0x[a-fA-F0-9]+$/,RA=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;!Number.parseInt&&window.parseInt&&(Number.parseInt=window.parseInt);!Number.parseFloat&&window.parseFloat&&(Number.parseFloat=window.parseFloat);var IA={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function NA(e,t={}){if(t=Object.assign({},IA,t),!e||typeof e!="string")return e;let r=e.trim();if(t.skipLike!==void 0&&t.skipLike.test(r))return e;if(t.hex&&PA.test(r))return Number.parseInt(r,16);{let i=RA.exec(r);if(i){let n=i[1],o=i[2],s=kA(i[3]),a=i[4]||i[6];if(!t.leadingZeros&&o.length>0&&n&&r[2]!==".")return e;if(!t.leadingZeros&&o.length>0&&!n&&r[1]!==".")return e;{let l=Number(r),u=""+l;return u.search(/[eE]/)!==-1||a?t.eNotation?l:e:r.indexOf(".")!==-1?u==="0"&&s===""||u===s||n&&u==="-"+s?l:e:o?s===u||n+s===u?l:e:r===u||r===n+u?l:e}}else return e}}function kA(e){return e&&e.indexOf(".")!==-1&&(e=e.replace(/0+$/,""),e==="."?e="0":e[0]==="."?e="0"+e:e[e.length-1]==="."&&(e=e.substr(0,e.length-1))),e}Km.exports=NA});var eg=y((OS,Zm)=>{"use strict";var Kl=ta(),ln=zm(),CA=Jm(),qA=Ym(),DS="<((!\\[CDATA\\[([\\s\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\/)(NAME)\\s*>))([^<]*)".replace(/NAME/g,Kl.nameRegexp),zl=class{constructor(t){this.options=t,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xA2"},pound:{regex:/&(pound|#163);/g,val:"\xA3"},yen:{regex:/&(yen|#165);/g,val:"\xA5"},euro:{regex:/&(euro|#8364);/g,val:"\u20AC"},copyright:{regex:/&(copy|#169);/g,val:"\xA9"},reg:{regex:/&(reg|#174);/g,val:"\xAE"},inr:{regex:/&(inr|#8377);/g,val:"\u20B9"}},this.addExternalEntities=BA,this.parseXml=UA,this.parseTextData=VA,this.resolveNameSpace=HA,this.buildAttributesMap=LA,this.isItStopNode=GA,this.replaceEntitiesValue=WA,this.readStopNodeData=zA,this.saveTextToParentTag=XA,this.addChild=jA}};function BA(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let i=t[r];this.lastEntities[i]={regex:new RegExp("&"+i+";","g"),val:e[i]}}}function VA(e,t,r,i,n,o,s){if(e!==void 0&&(this.options.trimValues&&!i&&(e=e.trim()),e.length>0)){s||(e=this.replaceEntitiesValue(e));let a=this.options.tagValueProcessor(t,e,r,n,o);return a==null?e:typeof a!=typeof e||a!==e?a:this.options.trimValues?Jl(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?Jl(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function HA(e){if(this.options.removeNSPrefix){let t=e.split(":"),r=e.charAt(0)==="/"?"/":"";if(t[0]==="xmlns")return"";t.length===2&&(e=r+t[1])}return e}var FA=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function LA(e,t,r){if(!this.options.ignoreAttributes&&typeof e=="string"){let i=Kl.getAllMatches(e,FA),n=i.length,o={};for(let s=0;s<n;s++){let a=this.resolveNameSpace(i[s][1]),l=i[s][4],u=this.options.attributeNamePrefix+a;if(a.length)if(this.options.transformAttributeName&&(u=this.options.transformAttributeName(u)),u==="__proto__"&&(u="#__proto__"),l!==void 0){this.options.trimValues&&(l=l.trim()),l=this.replaceEntitiesValue(l);let d=this.options.attributeValueProcessor(a,l,t);d==null?o[u]=l:typeof d!=typeof l||d!==l?o[u]=d:o[u]=Jl(l,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(o[u]=!0)}if(!Object.keys(o).length)return;if(this.options.attributesGroupName){let s={};return s[this.options.attributesGroupName]=o,s}return o}}var UA=function(e){e=e.replace(/\r\n?/g,`
`);let t=new ln("!xml"),r=t,i="",n="";for(let o=0;o<e.length;o++)if(e[o]==="<")if(e[o+1]==="/"){let a=Br(e,">",o,"Closing Tag is not closed."),l=e.substring(o+2,a).trim();if(this.options.removeNSPrefix){let c=l.indexOf(":");c!==-1&&(l=l.substr(c+1))}this.options.transformTagName&&(l=this.options.transformTagName(l)),r&&(i=this.saveTextToParentTag(i,r,n));let u=n.substring(n.lastIndexOf(".")+1);if(l&&this.options.unpairedTags.indexOf(l)!==-1)throw new Error(`Unpaired tag can not be used as closing tag: </${l}>`);let d=0;u&&this.options.unpairedTags.indexOf(u)!==-1?(d=n.lastIndexOf(".",n.lastIndexOf(".")-1),this.tagsNodeStack.pop()):d=n.lastIndexOf("."),n=n.substring(0,d),r=this.tagsNodeStack.pop(),i="",o=a}else if(e[o+1]==="?"){let a=$l(e,o,!1,"?>");if(!a)throw new Error("Pi Tag is not closed.");if(i=this.saveTextToParentTag(i,r,n),!(this.options.ignoreDeclaration&&a.tagName==="?xml"||this.options.ignorePiTags)){let l=new ln(a.tagName);l.add(this.options.textNodeName,""),a.tagName!==a.tagExp&&a.attrExpPresent&&(l[":@"]=this.buildAttributesMap(a.tagExp,n,a.tagName)),this.addChild(r,l,n)}o=a.closeIndex+1}else if(e.substr(o+1,3)==="!--"){let a=Br(e,"-->",o+4,"Comment is not closed.");if(this.options.commentPropName){let l=e.substring(o+4,a-2);i=this.saveTextToParentTag(i,r,n),r.add(this.options.commentPropName,[{[this.options.textNodeName]:l}])}o=a}else if(e.substr(o+1,2)==="!D"){let a=CA(e,o);this.docTypeEntities=a.entities,o=a.i}else if(e.substr(o+1,2)==="!["){let a=Br(e,"]]>",o,"CDATA is not closed.")-2,l=e.substring(o+9,a);if(i=this.saveTextToParentTag(i,r,n),this.options.cdataPropName)r.add(this.options.cdataPropName,[{[this.options.textNodeName]:l}]);else{let u=this.parseTextData(l,r.tagname,n,!0,!1,!0);u==null&&(u=""),r.add(this.options.textNodeName,u)}o=a+2}else{let a=$l(e,o,this.options.removeNSPrefix),l=a.tagName,u=a.rawTagName,d=a.tagExp,c=a.attrExpPresent,m=a.closeIndex;this.options.transformTagName&&(l=this.options.transformTagName(l)),r&&i&&r.tagname!=="!xml"&&(i=this.saveTextToParentTag(i,r,n,!1));let T=r;if(T&&this.options.unpairedTags.indexOf(T.tagname)!==-1&&(r=this.tagsNodeStack.pop(),n=n.substring(0,n.lastIndexOf("."))),l!==t.tagname&&(n+=n?"."+l:l),this.isItStopNode(this.options.stopNodes,n,l)){let f="";if(d.length>0&&d.lastIndexOf("/")===d.length-1)o=a.closeIndex;else if(this.options.unpairedTags.indexOf(l)!==-1)o=a.closeIndex;else{let h=this.readStopNodeData(e,u,m+1);if(!h)throw new Error(`Unexpected end of ${u}`);o=h.i,f=h.tagContent}let A=new ln(l);l!==d&&c&&(A[":@"]=this.buildAttributesMap(d,n,l)),f&&(f=this.parseTextData(f,l,n,!0,c,!0,!0)),n=n.substr(0,n.lastIndexOf(".")),A.add(this.options.textNodeName,f),this.addChild(r,A,n)}else{if(d.length>0&&d.lastIndexOf("/")===d.length-1){l[l.length-1]==="/"?(l=l.substr(0,l.length-1),n=n.substr(0,n.length-1),d=l):d=d.substr(0,d.length-1),this.options.transformTagName&&(l=this.options.transformTagName(l));let f=new ln(l);l!==d&&c&&(f[":@"]=this.buildAttributesMap(d,n,l)),this.addChild(r,f,n),n=n.substr(0,n.lastIndexOf("."))}else{let f=new ln(l);this.tagsNodeStack.push(r),l!==d&&c&&(f[":@"]=this.buildAttributesMap(d,n,l)),this.addChild(r,f,n),r=f}i="",o=m}}else i+=e[o];return t.child};function jA(e,t,r){let i=this.options.updateTag(t.tagname,r,t[":@"]);i===!1||(typeof i=="string"&&(t.tagname=i),e.addChild(t))}var WA=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){let r=this.docTypeEntities[t];e=e.replace(r.regx,r.val)}for(let t in this.lastEntities){let r=this.lastEntities[t];e=e.replace(r.regex,r.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){let r=this.htmlEntities[t];e=e.replace(r.regex,r.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function XA(e,t,r,i){return e&&(i===void 0&&(i=Object.keys(t.child).length===0),e=this.parseTextData(e,t.tagname,r,!1,t[":@"]?Object.keys(t[":@"]).length!==0:!1,i),e!==void 0&&e!==""&&t.add(this.options.textNodeName,e),e=""),e}function GA(e,t,r){let i="*."+r;for(let n in e){let o=e[n];if(i===o||t===o)return!0}return!1}function QA(e,t,r=">"){let i,n="";for(let o=t;o<e.length;o++){let s=e[o];if(i)s===i&&(i="");else if(s==='"'||s==="'")i=s;else if(s===r[0])if(r[1]){if(e[o+1]===r[1])return{data:n,index:o}}else return{data:n,index:o};else s==="	"&&(s=" ");n+=s}}function Br(e,t,r,i){let n=e.indexOf(t,r);if(n===-1)throw new Error(i);return n+t.length-1}function $l(e,t,r,i=">"){let n=QA(e,t+1,i);if(!n)return;let o=n.data,s=n.index,a=o.search(/\s/),l=o,u=!0;a!==-1&&(l=o.substr(0,a).replace(/\s\s*$/,""),o=o.substr(a+1));let d=l;if(r){let c=l.indexOf(":");c!==-1&&(l=l.substr(c+1),u=l!==n.data.substr(c+1))}return{tagName:l,tagExp:o,closeIndex:s,attrExpPresent:u,rawTagName:d}}function zA(e,t,r){let i=r,n=1;for(;r<e.length;r++)if(e[r]==="<")if(e[r+1]==="/"){let o=Br(e,">",r,`${t} is not closed`);if(e.substring(r+2,o).trim()===t&&(n--,n===0))return{tagContent:e.substring(i,r),i:o};r=o}else if(e[r+1]==="?")r=Br(e,"?>",r+1,"StopNode is not closed.");else if(e.substr(r+1,3)==="!--")r=Br(e,"-->",r+3,"StopNode is not closed.");else if(e.substr(r+1,2)==="![")r=Br(e,"]]>",r,"StopNode is not closed.")-2;else{let o=$l(e,r,">");o&&((o&&o.tagName)===t&&o.tagExp[o.tagExp.length-1]!=="/"&&n++,r=o.closeIndex)}}function Jl(e,t,r){if(t&&typeof e=="string"){let i=e.trim();return i==="true"?!0:i==="false"?!1:qA(e,r)}else return Kl.isExist(e)?e:""}Zm.exports=zl});var ig=y(rg=>{"use strict";function $A(e,t){return tg(e,t)}function tg(e,t,r){let i,n={};for(let o=0;o<e.length;o++){let s=e[o],a=JA(s),l="";if(r===void 0?l=a:l=r+"."+a,a===t.textNodeName)i===void 0?i=s[a]:i+=""+s[a];else{if(a===void 0)continue;if(s[a]){let u=tg(s[a],t,l),d=YA(u,t);s[":@"]?KA(u,s[":@"],l,t):Object.keys(u).length===1&&u[t.textNodeName]!==void 0&&!t.alwaysCreateTextNode?u=u[t.textNodeName]:Object.keys(u).length===0&&(t.alwaysCreateTextNode?u[t.textNodeName]="":u=""),n[a]!==void 0&&n.hasOwnProperty(a)?(Array.isArray(n[a])||(n[a]=[n[a]]),n[a].push(u)):t.isArray(a,l,d)?n[a]=[u]:n[a]=u}}}return typeof i=="string"?i.length>0&&(n[t.textNodeName]=i):i!==void 0&&(n[t.textNodeName]=i),n}function JA(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let i=t[r];if(i!==":@")return i}}function KA(e,t,r,i){if(t){let n=Object.keys(t),o=n.length;for(let s=0;s<o;s++){let a=n[s];i.isArray(a,r+"."+a,!0,!0)?e[a]=[t[a]]:e[a]=t[a]}}}function YA(e,t){let{textNodeName:r}=t,i=Object.keys(e).length;return!!(i===0||i===1&&(e[r]||typeof e[r]=="boolean"||e[r]===0))}rg.prettify=$A});var og=y((PS,ng)=>{var{buildOptions:ZA}=Gm(),e0=eg(),{prettify:t0}=ig(),r0=Xl(),Yl=class{constructor(t){this.externalEntities={},this.options=ZA(t)}parse(t,r){if(typeof t!="string")if(t.toString)t=t.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(r){r===!0&&(r={});let o=r0.validate(t,r);if(o!==!0)throw Error(`${o.err.msg}:${o.err.line}:${o.err.col}`)}let i=new e0(this.options);i.addExternalEntities(this.externalEntities);let n=i.parseXml(t);return this.options.preserveOrder||n===void 0?n:t0(n,this.options)}addEntity(t,r){if(r.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");if(t.indexOf("&")!==-1||t.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if(r==="&")throw new Error("An entity with value '&' is not permitted");this.externalEntities[t]=r}};ng.exports=Yl});var dg=y((RS,ug)=>{var i0=`
`;function n0(e,t){let r="";return t.format&&t.indentBy.length>0&&(r=i0),sg(e,t,"",r)}function sg(e,t,r,i){let n="",o=!1;for(let s=0;s<e.length;s++){let a=e[s],l=o0(a);if(l===void 0)continue;let u="";if(r.length===0?u=l:u=`${r}.${l}`,l===t.textNodeName){let f=a[l];a0(u,t)||(f=t.tagValueProcessor(l,f),f=lg(f,t)),o&&(n+=i),n+=f,o=!1;continue}else if(l===t.cdataPropName){o&&(n+=i),n+=`<![CDATA[${a[l][0][t.textNodeName]}]]>`,o=!1;continue}else if(l===t.commentPropName){n+=i+`<!--${a[l][0][t.textNodeName]}-->`,o=!0;continue}else if(l[0]==="?"){let f=ag(a[":@"],t),A=l==="?xml"?"":i,h=a[l][0][t.textNodeName];h=h.length!==0?" "+h:"",n+=A+`<${l}${h}${f}?>`,o=!0;continue}let d=i;d!==""&&(d+=t.indentBy);let c=ag(a[":@"],t),m=i+`<${l}${c}`,T=sg(a[l],t,u,d);t.unpairedTags.indexOf(l)!==-1?t.suppressUnpairedNode?n+=m+">":n+=m+"/>":(!T||T.length===0)&&t.suppressEmptyNode?n+=m+"/>":T&&T.endsWith(">")?n+=m+`>${T}${i}</${l}>`:(n+=m+">",T&&i!==""&&(T.includes("/>")||T.includes("</"))?n+=i+t.indentBy+T+i:n+=T,n+=`</${l}>`),o=!0}return n}function o0(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let i=t[r];if(e.hasOwnProperty(i)&&i!==":@")return i}}function ag(e,t){let r="";if(e&&!t.ignoreAttributes)for(let i in e){if(!e.hasOwnProperty(i))continue;let n=t.attributeValueProcessor(i,e[i]);n=lg(n,t),n===!0&&t.suppressBooleanAttributes?r+=` ${i.substr(t.attributeNamePrefix.length)}`:r+=` ${i.substr(t.attributeNamePrefix.length)}="${n}"`}return r}function a0(e,t){e=e.substr(0,e.length-t.textNodeName.length-1);let r=e.substr(e.lastIndexOf(".")+1);for(let i in t.stopNodes)if(t.stopNodes[i]===e||t.stopNodes[i]==="*."+r)return!0;return!1}function lg(e,t){if(e&&e.length>0&&t.processEntities)for(let r=0;r<t.entities.length;r++){let i=t.entities[r];e=e.replace(i.regex,i.val)}return e}ug.exports=n0});var pg=y((IS,cg)=>{"use strict";var s0=dg(),l0={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function er(e){this.options=Object.assign({},l0,e),this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=c0),this.processTextOrObjNode=u0,this.options.format?(this.indentate=d0,this.tagEndChar=`>
`,this.newLine=`
`):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}er.prototype.build=function(e){return this.options.preserveOrder?s0(e,this.options):(Array.isArray(e)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(e={[this.options.arrayNodeName]:e}),this.j2x(e,0).val)};er.prototype.j2x=function(e,t){let r="",i="";for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n))if(typeof e[n]>"u")this.isAttribute(n)&&(i+="");else if(e[n]===null)this.isAttribute(n)?i+="":n[0]==="?"?i+=this.indentate(t)+"<"+n+"?"+this.tagEndChar:i+=this.indentate(t)+"<"+n+"/"+this.tagEndChar;else if(e[n]instanceof Date)i+=this.buildTextValNode(e[n],n,"",t);else if(typeof e[n]!="object"){let o=this.isAttribute(n);if(o)r+=this.buildAttrPairStr(o,""+e[n]);else if(n===this.options.textNodeName){let s=this.options.tagValueProcessor(n,""+e[n]);i+=this.replaceEntitiesValue(s)}else i+=this.buildTextValNode(e[n],n,"",t)}else if(Array.isArray(e[n])){let o=e[n].length,s="";for(let a=0;a<o;a++){let l=e[n][a];typeof l>"u"||(l===null?n[0]==="?"?i+=this.indentate(t)+"<"+n+"?"+this.tagEndChar:i+=this.indentate(t)+"<"+n+"/"+this.tagEndChar:typeof l=="object"?this.options.oneListGroup?s+=this.j2x(l,t+1).val:s+=this.processTextOrObjNode(l,n,t):s+=this.buildTextValNode(l,n,"",t))}this.options.oneListGroup&&(s=this.buildObjectNode(s,n,"",t)),i+=s}else if(this.options.attributesGroupName&&n===this.options.attributesGroupName){let o=Object.keys(e[n]),s=o.length;for(let a=0;a<s;a++)r+=this.buildAttrPairStr(o[a],""+e[n][o[a]])}else i+=this.processTextOrObjNode(e[n],n,t);return{attrStr:r,val:i}};er.prototype.buildAttrPairStr=function(e,t){return t=this.options.attributeValueProcessor(e,""+t),t=this.replaceEntitiesValue(t),this.options.suppressBooleanAttributes&&t==="true"?" "+e:" "+e+'="'+t+'"'};function u0(e,t,r){let i=this.j2x(e,r+1);return e[this.options.textNodeName]!==void 0&&Object.keys(e).length===1?this.buildTextValNode(e[this.options.textNodeName],t,i.attrStr,r):this.buildObjectNode(i.val,t,i.attrStr,r)}er.prototype.buildObjectNode=function(e,t,r,i){if(e==="")return t[0]==="?"?this.indentate(i)+"<"+t+r+"?"+this.tagEndChar:this.indentate(i)+"<"+t+r+this.closeTag(t)+this.tagEndChar;{let n="</"+t+this.tagEndChar,o="";return t[0]==="?"&&(o="?",n=""),(r||r==="")&&e.indexOf("<")===-1?this.indentate(i)+"<"+t+r+o+">"+e+n:this.options.commentPropName!==!1&&t===this.options.commentPropName&&o.length===0?this.indentate(i)+`<!--${e}-->`+this.newLine:this.indentate(i)+"<"+t+r+o+this.tagEndChar+e+this.indentate(i)+n}};er.prototype.closeTag=function(e){let t="";return this.options.unpairedTags.indexOf(e)!==-1?this.options.suppressUnpairedNode||(t="/"):this.options.suppressEmptyNode?t="/":t=`></${e}`,t};er.prototype.buildTextValNode=function(e,t,r,i){if(this.options.cdataPropName!==!1&&t===this.options.cdataPropName)return this.indentate(i)+`<![CDATA[${e}]]>`+this.newLine;if(this.options.commentPropName!==!1&&t===this.options.commentPropName)return this.indentate(i)+`<!--${e}-->`+this.newLine;if(t[0]==="?")return this.indentate(i)+"<"+t+r+"?"+this.tagEndChar;{let n=this.options.tagValueProcessor(t,e);return n=this.replaceEntitiesValue(n),n===""?this.indentate(i)+"<"+t+r+this.closeTag(t)+this.tagEndChar:this.indentate(i)+"<"+t+r+">"+n+"</"+t+this.tagEndChar}};er.prototype.replaceEntitiesValue=function(e){if(e&&e.length>0&&this.options.processEntities)for(let t=0;t<this.options.entities.length;t++){let r=this.options.entities[t];e=e.replace(r.regex,r.val)}return e};function d0(e){return this.options.indentBy.repeat(e)}function c0(e){return e.startsWith(this.options.attributeNamePrefix)&&e!==this.options.textNodeName?e.substr(this.attrPrefixLen):!1}cg.exports=er});var mg=y((NS,fg)=>{"use strict";var p0=Xl(),f0=og(),m0=pg();fg.exports={XMLParser:f0,XMLValidator:p0,XMLBuilder:m0}});function hg(e){let r=new gg.XMLParser({attributesGroupName:"@_",ignoreDeclaration:!0,parseAttributeValue:!0,ignoreAttributes:!1,removeNSPrefix:!0,trimValues:!0,isArray:a=>a==="adaptationset"||a==="representation",transformTagName:a=>a.toLowerCase(),transformAttributeName:a=>a.toLowerCase()}).parse(e),i=r.mpd?.period?.adaptationset;if(!Array.isArray(i))return j("Invalid MPD XML");let n="unknown";{let a=r.mpd?.["@_"]?.["@_mediapresentationduration"];if(typeof a=="string"){let l=/\d+(\.\d+)?S/,u=/\d+M/,d=l.exec(a),c=u.exec(a);(d||c)&&(n=0,d&&d.length>0&&(n=parseFloat(d[0])),c&&c.length>0&&(n+=60*(parseFloat(c[0])||0)))}}let o=0,s=[];for(let a of i){let l=[a];"contentcomponent"in a&&"@_"in a.contentcomponent&&l.push(a.contentcomponent),"segmenttemplate"in a&&"@_"in a.segmenttemplate&&l.push(a.segmenttemplate);let u={bitrate:D,content_type:void 0,mime_type:void 0,codecs:void 0,width:void 0,height:void 0,framerate:D},d=(m,T)=>{let f={...T};for(let A of m){let h=A["@_"]??[];for(let p of Object.keys(h)){if(p==="@_bandwidth"){let g=h["@_bandwidth"];typeof g=="number"&&(f.bitrate=C(g))}if(p==="@_contenttype"){let g=h["@_contenttype"];typeof g=="string"&&(f.content_type=g)}if(p==="@_mimetype"){let g=h["@_mimetype"];typeof g=="string"&&(f.mime_type=g)}if(p==="@_codecs"){let g=h["@_codecs"];typeof g=="string"&&(f.codecs=g)}if(p==="@_width"){let g=h["@_width"];typeof g=="number"&&(f.width=g)}if(p==="@_height"){let g=h["@_height"];typeof g=="number"&&(f.height=g)}if(p==="@_framerate"){let g=h["@_framerate"];typeof g=="number"&&(f.framerate=C(g))}}}return f};u=d(l,u);let c=a.representation;if(!Array.isArray(c))break;for(let m of c){let T=o.toString();o++;let f=d([m],u),{codecs:A,mime_type:h,bitrate:p,width:g,height:x,framerate:w}=f,_=j("Invalid mimetype/codecs");if(typeof h=="string"&&typeof A=="string"){let W=`${h}; codecs="${A}"`;_=si(W)}if(_.isErr()){console.warn("Failed to parse mimetype from",h,A);continue}let{av_codecs:S,container:M}=_.unwrap(),I=Qe(S,W=>({codec:Xe(W),bitrate:p}),W=>{let q=We(W),$=w,O=D,k=D;return typeof g=="number"&&typeof x=="number"&&(k=C(zr(x)),O=C({width:g,height:x})),{codec:q,bitrate:p,fps:$,dimensions:O,quality:k}}),U={builder:"MPD",protocol:"dash",content_length:D,duration:n,container:ce(M),av:I};s.push([U,T])}}return L(s)}var gg,_g=N(()=>{"use strict";ae();Ki();xt();Ge();fr();Bn();gg=yt(mg(),1);$r();je()});function yg(e,t){let r=e.video?.thumbs?.["640"]||e.video?.thumbs?.base;r&&(t.thumbnailUrl2=r.toString());let i=e.request?.files?.hls?.cdns,n=e.request?.files?.hls?.default_cdn;if(n in i){new tr(i[n].url,[]).onHitDataAvailable(t);return}let o=e.request?.files?.dash?.cdns;if(i)for(let s in i){let a=i[s]?.url;a&&new tr(a,[]).onHitDataAvailable(t)}if(o)for(let s in o){let a=o[s]?.url.replace("master.json","master.mpd");a&&new Vr(a,[]).onHitDataAvailable(t)}}var g0,bg,un,dn,vg=N(()=>{"use strict";ra();g0=(he(),P(ge)),bg=te().browser;un=class{constructor(t,r){}static canHandle(t,r,i){return!!(t.startsWith("https://player.vimeo.com/video/")&&r=="text/html; charset=utf-8")}async onHitDataAvailable(t){let r={tabId:t.tabId};t.frameId&&(r.frameIds=[t.frameId]);for(let i=0;i<5;i++){try{let n=await bg.scripting.executeScript({target:r,world:bg.scripting.ExecutionWorld.MAIN,func:()=>window.playerConfig??window.wrappedJSObject?.playerConfig});if(n[0]?.result){yg(n[0].result,t);return}}catch{}await new Promise(n=>setTimeout(n,2e3))}console.warn("Couldn't get vimeo player config")}},dn=class{constructor(t,r){this.headers=r,this.config_url=t}static canHandle(t,r,i){let n=i.find(o=>o.name=="Origin");return n?.value?.includes("vimeo.com")||n?.value?.includes("vhx.tv")?t.includes("config?"):!1}async onHitDataAvailable(t){let r=await g0.request({url:this.config_url,headers:this.headers});if(!r.ok){console.warn("Failed to fetch Vimeo Config content");return}let i=await r.json();yg(i,t)}}});var ia,wg=N(()=>{ia=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(i,n){this.listeners[i]||(this.listeners[i]=[]),this.listeners[i].push(n)},t.off=function(i,n){if(!this.listeners[i])return!1;var o=this.listeners[i].indexOf(n);return this.listeners[i]=this.listeners[i].slice(0),this.listeners[i].splice(o,1),o>-1},t.trigger=function(i){var n=this.listeners[i];if(n)if(arguments.length===2)for(var o=n.length,s=0;s<o;++s)n[s].call(this,arguments[1]);else for(var a=Array.prototype.slice.call(arguments,1),l=n.length,u=0;u<l;++u)n[u].apply(this,a)},t.dispose=function(){this.listeners={}},t.pipe=function(i){this.on("data",function(n){i.push(n)})},e}()});function rr(){return rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},rr.apply(this,arguments)}var Ag=N(()=>{});var Tg=y((YS,xg)=>{var cn;typeof window<"u"?cn=window:typeof global<"u"?cn=global:typeof self<"u"?cn=self:cn={};xg.exports=cn});function eu(e){for(var t=h0(e),r=new Uint8Array(t.length),i=0;i<t.length;i++)r[i]=t.charCodeAt(i);return r}var Zl,h0,Eg=N(()=>{Zl=yt(Tg()),h0=function(t){return Zl.default.atob?Zl.default.atob(t):Buffer.from(t,"base64").toString("binary")}});var iu,_0,tu,b0,Re,nu,y0,ir,ru,pn,Sg=N(()=>{wg();Ag();Eg();iu=class extends ia{constructor(){super(),this.buffer=""}push(t){let r;for(this.buffer+=t,r=this.buffer.indexOf(`
`);r>-1;r=this.buffer.indexOf(`
`))this.trigger("data",this.buffer.substring(0,r)),this.buffer=this.buffer.substring(r+1)}},_0="	",tu=function(e){let t=/([0-9.]*)?@?([0-9.]*)?/.exec(e||""),r={};return t[1]&&(r.length=parseInt(t[1],10)),t[2]&&(r.offset=parseInt(t[2],10)),r},b0=function(){let r="(?:"+"[^=]*"+")=(?:"+'"[^"]*"|[^,]*'+")";return new RegExp("(?:^|,)("+r+")")},Re=function(e){let t={};if(!e)return t;let r=e.split(b0()),i=r.length,n;for(;i--;)r[i]!==""&&(n=/([^=]*)=(.*)/.exec(r[i]).slice(1),n[0]=n[0].replace(/^\s+|\s+$/g,""),n[1]=n[1].replace(/^\s+|\s+$/g,""),n[1]=n[1].replace(/^['"](.*)['"]$/g,"$1"),t[n[0]]=n[1]);return t},nu=class extends ia{constructor(){super(),this.customParsers=[],this.tagMappers=[]}push(t){let r,i;if(t=t.trim(),t.length===0)return;if(t[0]!=="#"){this.trigger("data",{type:"uri",uri:t});return}this.tagMappers.reduce((o,s)=>{let a=s(t);return a===t?o:o.concat([a])},[t]).forEach(o=>{for(let s=0;s<this.customParsers.length;s++)if(this.customParsers[s].call(this,o))return;if(o.indexOf("#EXT")!==0){this.trigger("data",{type:"comment",text:o.slice(1)});return}if(o=o.replace("\r",""),r=/^#EXTM3U/.exec(o),r){this.trigger("data",{type:"tag",tagType:"m3u"});return}if(r=/^#EXTINF:([0-9\.]*)?,?(.*)?$/.exec(o),r){i={type:"tag",tagType:"inf"},r[1]&&(i.duration=parseFloat(r[1])),r[2]&&(i.title=r[2]),this.trigger("data",i);return}if(r=/^#EXT-X-TARGETDURATION:([0-9.]*)?/.exec(o),r){i={type:"tag",tagType:"targetduration"},r[1]&&(i.duration=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-VERSION:([0-9.]*)?/.exec(o),r){i={type:"tag",tagType:"version"},r[1]&&(i.version=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-MEDIA-SEQUENCE:(\-?[0-9.]*)?/.exec(o),r){i={type:"tag",tagType:"media-sequence"},r[1]&&(i.number=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-DISCONTINUITY-SEQUENCE:(\-?[0-9.]*)?/.exec(o),r){i={type:"tag",tagType:"discontinuity-sequence"},r[1]&&(i.number=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-PLAYLIST-TYPE:(.*)?$/.exec(o),r){i={type:"tag",tagType:"playlist-type"},r[1]&&(i.playlistType=r[1]),this.trigger("data",i);return}if(r=/^#EXT-X-BYTERANGE:(.*)?$/.exec(o),r){i=rr(tu(r[1]),{type:"tag",tagType:"byterange"}),this.trigger("data",i);return}if(r=/^#EXT-X-ALLOW-CACHE:(YES|NO)?/.exec(o),r){i={type:"tag",tagType:"allow-cache"},r[1]&&(i.allowed=!/NO/.test(r[1])),this.trigger("data",i);return}if(r=/^#EXT-X-MAP:(.*)$/.exec(o),r){if(i={type:"tag",tagType:"map"},r[1]){let s=Re(r[1]);s.URI&&(i.uri=s.URI),s.BYTERANGE&&(i.byterange=tu(s.BYTERANGE))}this.trigger("data",i);return}if(r=/^#EXT-X-STREAM-INF:(.*)$/.exec(o),r){if(i={type:"tag",tagType:"stream-inf"},r[1]){if(i.attributes=Re(r[1]),i.attributes.RESOLUTION){let s=i.attributes.RESOLUTION.split("x"),a={};s[0]&&(a.width=parseInt(s[0],10)),s[1]&&(a.height=parseInt(s[1],10)),i.attributes.RESOLUTION=a}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["FRAME-RATE"]&&(i.attributes["FRAME-RATE"]=parseFloat(i.attributes["FRAME-RATE"])),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}this.trigger("data",i);return}if(r=/^#EXT-X-MEDIA:(.*)$/.exec(o),r){i={type:"tag",tagType:"media"},r[1]&&(i.attributes=Re(r[1])),this.trigger("data",i);return}if(r=/^#EXT-X-ENDLIST/.exec(o),r){this.trigger("data",{type:"tag",tagType:"endlist"});return}if(r=/^#EXT-X-DISCONTINUITY/.exec(o),r){this.trigger("data",{type:"tag",tagType:"discontinuity"});return}if(r=/^#EXT-X-PROGRAM-DATE-TIME:(.*)$/.exec(o),r){i={type:"tag",tagType:"program-date-time"},r[1]&&(i.dateTimeString=r[1],i.dateTimeObject=new Date(r[1])),this.trigger("data",i);return}if(r=/^#EXT-X-KEY:(.*)$/.exec(o),r){i={type:"tag",tagType:"key"},r[1]&&(i.attributes=Re(r[1]),i.attributes.IV&&(i.attributes.IV.substring(0,2).toLowerCase()==="0x"&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),this.trigger("data",i);return}if(r=/^#EXT-X-START:(.*)$/.exec(o),r){i={type:"tag",tagType:"start"},r[1]&&(i.attributes=Re(r[1]),i.attributes["TIME-OFFSET"]=parseFloat(i.attributes["TIME-OFFSET"]),i.attributes.PRECISE=/YES/.test(i.attributes.PRECISE)),this.trigger("data",i);return}if(r=/^#EXT-X-CUE-OUT-CONT:(.*)?$/.exec(o),r){i={type:"tag",tagType:"cue-out-cont"},r[1]?i.data=r[1]:i.data="",this.trigger("data",i);return}if(r=/^#EXT-X-CUE-OUT:(.*)?$/.exec(o),r){i={type:"tag",tagType:"cue-out"},r[1]?i.data=r[1]:i.data="",this.trigger("data",i);return}if(r=/^#EXT-X-CUE-IN:(.*)?$/.exec(o),r){i={type:"tag",tagType:"cue-in"},r[1]?i.data=r[1]:i.data="",this.trigger("data",i);return}if(r=/^#EXT-X-SKIP:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"skip"},i.attributes=Re(r[1]),i.attributes.hasOwnProperty("SKIPPED-SEGMENTS")&&(i.attributes["SKIPPED-SEGMENTS"]=parseInt(i.attributes["SKIPPED-SEGMENTS"],10)),i.attributes.hasOwnProperty("RECENTLY-REMOVED-DATERANGES")&&(i.attributes["RECENTLY-REMOVED-DATERANGES"]=i.attributes["RECENTLY-REMOVED-DATERANGES"].split(_0)),this.trigger("data",i);return}if(r=/^#EXT-X-PART:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"part"},i.attributes=Re(r[1]),["DURATION"].forEach(function(s){i.attributes.hasOwnProperty(s)&&(i.attributes[s]=parseFloat(i.attributes[s]))}),["INDEPENDENT","GAP"].forEach(function(s){i.attributes.hasOwnProperty(s)&&(i.attributes[s]=/YES/.test(i.attributes[s]))}),i.attributes.hasOwnProperty("BYTERANGE")&&(i.attributes.byterange=tu(i.attributes.BYTERANGE)),this.trigger("data",i);return}if(r=/^#EXT-X-SERVER-CONTROL:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"server-control"},i.attributes=Re(r[1]),["CAN-SKIP-UNTIL","PART-HOLD-BACK","HOLD-BACK"].forEach(function(s){i.attributes.hasOwnProperty(s)&&(i.attributes[s]=parseFloat(i.attributes[s]))}),["CAN-SKIP-DATERANGES","CAN-BLOCK-RELOAD"].forEach(function(s){i.attributes.hasOwnProperty(s)&&(i.attributes[s]=/YES/.test(i.attributes[s]))}),this.trigger("data",i);return}if(r=/^#EXT-X-PART-INF:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"part-inf"},i.attributes=Re(r[1]),["PART-TARGET"].forEach(function(s){i.attributes.hasOwnProperty(s)&&(i.attributes[s]=parseFloat(i.attributes[s]))}),this.trigger("data",i);return}if(r=/^#EXT-X-PRELOAD-HINT:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"preload-hint"},i.attributes=Re(r[1]),["BYTERANGE-START","BYTERANGE-LENGTH"].forEach(function(s){if(i.attributes.hasOwnProperty(s)){i.attributes[s]=parseInt(i.attributes[s],10);let a=s==="BYTERANGE-LENGTH"?"length":"offset";i.attributes.byterange=i.attributes.byterange||{},i.attributes.byterange[a]=i.attributes[s],delete i.attributes[s]}}),this.trigger("data",i);return}if(r=/^#EXT-X-RENDITION-REPORT:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"rendition-report"},i.attributes=Re(r[1]),["LAST-MSN","LAST-PART"].forEach(function(s){i.attributes.hasOwnProperty(s)&&(i.attributes[s]=parseInt(i.attributes[s],10))}),this.trigger("data",i);return}if(r=/^#EXT-X-DATERANGE:(.*)$/.exec(o),r&&r[1]){i={type:"tag",tagType:"daterange"},i.attributes=Re(r[1]),["ID","CLASS"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=String(i.attributes[a]))}),["START-DATE","END-DATE"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=new Date(i.attributes[a]))}),["DURATION","PLANNED-DURATION"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=parseFloat(i.attributes[a]))}),["END-ON-NEXT"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=/YES/i.test(i.attributes[a]))}),["SCTE35-CMD"," SCTE35-OUT","SCTE35-IN"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=i.attributes[a].toString(16))});let s=/^X-([A-Z]+-)+[A-Z]+$/;for(let a in i.attributes){if(!s.test(a))continue;let l=/[0-9A-Fa-f]{6}/g.test(i.attributes[a]),u=/^\d+(\.\d+)?$/.test(i.attributes[a]);i.attributes[a]=l?i.attributes[a].toString(16):u?parseFloat(i.attributes[a]):String(i.attributes[a])}this.trigger("data",i);return}if(r=/^#EXT-X-INDEPENDENT-SEGMENTS/.exec(o),r){this.trigger("data",{type:"tag",tagType:"independent-segments"});return}if(r=/^#EXT-X-CONTENT-STEERING:(.*)$/.exec(o),r){i={type:"tag",tagType:"content-steering"},i.attributes=Re(r[1]),this.trigger("data",i);return}this.trigger("data",{type:"tag",data:o.slice(4)})})}addParser({expression:t,customType:r,dataParser:i,segment:n}){typeof i!="function"&&(i=o=>o),this.customParsers.push(o=>{if(t.exec(o))return this.trigger("data",{type:"custom",data:i(o),customType:r,segment:n}),!0})}addTagMapper({expression:t,map:r}){let i=n=>t.test(n)?r(n):n;this.tagMappers.push(i)}},y0=e=>e.toLowerCase().replace(/-(\w)/g,t=>t[1].toUpperCase()),ir=function(e){let t={};return Object.keys(e).forEach(function(r){t[y0(r)]=e[r]}),t},ru=function(e){let{serverControl:t,targetDuration:r,partTargetDuration:i}=e;if(!t)return;let n="#EXT-X-SERVER-CONTROL",o="holdBack",s="partHoldBack",a=r&&r*3,l=i&&i*2;r&&!t.hasOwnProperty(o)&&(t[o]=a,this.trigger("info",{message:`${n} defaulting HOLD-BACK to targetDuration * 3 (${a}).`})),a&&t[o]<a&&(this.trigger("warn",{message:`${n} clamping HOLD-BACK (${t[o]}) to targetDuration * 3 (${a})`}),t[o]=a),i&&!t.hasOwnProperty(s)&&(t[s]=i*3,this.trigger("info",{message:`${n} defaulting PART-HOLD-BACK to partTargetDuration * 3 (${t[s]}).`})),i&&t[s]<l&&(this.trigger("warn",{message:`${n} clamping PART-HOLD-BACK (${t[s]}) to partTargetDuration * 2 (${l}).`}),t[s]=l)},pn=class extends ia{constructor(){super(),this.lineStream=new iu,this.parseStream=new nu,this.lineStream.pipe(this.parseStream),this.lastProgramDateTime=null;let t=this,r=[],i={},n,o,s=!1,a=function(){},l={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},u="urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",d=0;this.manifest={allowCache:!0,discontinuityStarts:[],dateRanges:[],segments:[]};let c=0,m=0,T={};this.on("end",()=>{i.uri||!i.parts&&!i.preloadHints||(!i.map&&n&&(i.map=n),!i.key&&o&&(i.key=o),!i.timeline&&typeof d=="number"&&(i.timeline=d),this.manifest.preloadSegment=i)}),this.parseStream.on("data",function(f){let A,h;({tag(){({version(){f.version&&(this.manifest.version=f.version)},"allow-cache"(){this.manifest.allowCache=f.allowed,"allowed"in f||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange(){let p={};"length"in f&&(i.byterange=p,p.length=f.length,"offset"in f||(f.offset=c)),"offset"in f&&(i.byterange=p,p.offset=f.offset),c=p.offset+p.length},endlist(){this.manifest.endList=!0},inf(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),f.title&&(i.title=f.title),f.duration>0&&(i.duration=f.duration),f.duration===0&&(i.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=r},key(){if(!f.attributes){this.trigger("warn",{message:"ignoring key declaration without attribute list"});return}if(f.attributes.METHOD==="NONE"){o=null;return}if(!f.attributes.URI){this.trigger("warn",{message:"ignoring key declaration without URI"});return}if(f.attributes.KEYFORMAT==="com.apple.streamingkeydelivery"){this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.apple.fps.1_0"]={attributes:f.attributes};return}if(f.attributes.KEYFORMAT==="com.microsoft.playready"){this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.microsoft.playready"]={uri:f.attributes.URI};return}if(f.attributes.KEYFORMAT===u){if(["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(f.attributes.METHOD)===-1){this.trigger("warn",{message:"invalid key method provided for Widevine"});return}if(f.attributes.METHOD==="SAMPLE-AES-CENC"&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),f.attributes.URI.substring(0,23)!=="data:text/plain;base64,"){this.trigger("warn",{message:"invalid key URI provided for Widevine"});return}if(!(f.attributes.KEYID&&f.attributes.KEYID.substring(0,2)==="0x")){this.trigger("warn",{message:"invalid key ID provided for Widevine"});return}this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.widevine.alpha"]={attributes:{schemeIdUri:f.attributes.KEYFORMAT,keyId:f.attributes.KEYID.substring(2)},pssh:eu(f.attributes.URI.split(",")[1])};return}f.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),o={method:f.attributes.METHOD||"AES-128",uri:f.attributes.URI},typeof f.attributes.IV<"u"&&(o.iv=f.attributes.IV)},"media-sequence"(){if(!isFinite(f.number)){this.trigger("warn",{message:"ignoring invalid media sequence: "+f.number});return}this.manifest.mediaSequence=f.number},"discontinuity-sequence"(){if(!isFinite(f.number)){this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+f.number});return}this.manifest.discontinuitySequence=f.number,d=f.number},"playlist-type"(){if(!/VOD|EVENT/.test(f.playlistType)){this.trigger("warn",{message:"ignoring unknown playlist type: "+f.playlist});return}this.manifest.playlistType=f.playlistType},map(){n={},f.uri&&(n.uri=f.uri),f.byterange&&(n.byterange=f.byterange),o&&(n.key=o)},"stream-inf"(){if(this.manifest.playlists=r,this.manifest.mediaGroups=this.manifest.mediaGroups||l,!f.attributes){this.trigger("warn",{message:"ignoring empty stream-inf attributes"});return}i.attributes||(i.attributes={}),rr(i.attributes,f.attributes)},media(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||l,!(f.attributes&&f.attributes.TYPE&&f.attributes["GROUP-ID"]&&f.attributes.NAME)){this.trigger("warn",{message:"ignoring incomplete or missing media group"});return}let p=this.manifest.mediaGroups[f.attributes.TYPE];p[f.attributes["GROUP-ID"]]=p[f.attributes["GROUP-ID"]]||{},A=p[f.attributes["GROUP-ID"]],h={default:/yes/i.test(f.attributes.DEFAULT)},h.default?h.autoselect=!0:h.autoselect=/yes/i.test(f.attributes.AUTOSELECT),f.attributes.LANGUAGE&&(h.language=f.attributes.LANGUAGE),f.attributes.URI&&(h.uri=f.attributes.URI),f.attributes["INSTREAM-ID"]&&(h.instreamId=f.attributes["INSTREAM-ID"]),f.attributes.CHARACTERISTICS&&(h.characteristics=f.attributes.CHARACTERISTICS),f.attributes.FORCED&&(h.forced=/yes/i.test(f.attributes.FORCED)),A[f.attributes.NAME]=h},discontinuity(){d+=1,i.discontinuity=!0,this.manifest.discontinuityStarts.push(r.length)},"program-date-time"(){typeof this.manifest.dateTimeString>"u"&&(this.manifest.dateTimeString=f.dateTimeString,this.manifest.dateTimeObject=f.dateTimeObject),i.dateTimeString=f.dateTimeString,i.dateTimeObject=f.dateTimeObject;let{lastProgramDateTime:p}=this;this.lastProgramDateTime=new Date(f.dateTimeString).getTime(),p===null&&this.manifest.segments.reduceRight((g,x)=>(x.programDateTime=g-x.duration*1e3,x.programDateTime),this.lastProgramDateTime)},targetduration(){if(!isFinite(f.duration)||f.duration<0){this.trigger("warn",{message:"ignoring invalid target duration: "+f.duration});return}this.manifest.targetDuration=f.duration,ru.call(this,this.manifest)},start(){if(!f.attributes||isNaN(f.attributes["TIME-OFFSET"])){this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"});return}this.manifest.start={timeOffset:f.attributes["TIME-OFFSET"],precise:f.attributes.PRECISE}},"cue-out"(){i.cueOut=f.data},"cue-out-cont"(){i.cueOutCont=f.data},"cue-in"(){i.cueIn=f.data},skip(){this.manifest.skip=ir(f.attributes),this.warnOnMissingAttributes_("#EXT-X-SKIP",f.attributes,["SKIPPED-SEGMENTS"])},part(){s=!0;let p=this.manifest.segments.length,g=ir(f.attributes);i.parts=i.parts||[],i.parts.push(g),g.byterange&&(g.byterange.hasOwnProperty("offset")||(g.byterange.offset=m),m=g.byterange.offset+g.byterange.length);let x=i.parts.length-1;this.warnOnMissingAttributes_(`#EXT-X-PART #${x} for segment #${p}`,f.attributes,["URI","DURATION"]),this.manifest.renditionReports&&this.manifest.renditionReports.forEach((w,_)=>{w.hasOwnProperty("lastPart")||this.trigger("warn",{message:`#EXT-X-RENDITION-REPORT #${_} lacks required attribute(s): LAST-PART`})})},"server-control"(){let p=this.manifest.serverControl=ir(f.attributes);p.hasOwnProperty("canBlockReload")||(p.canBlockReload=!1,this.trigger("info",{message:"#EXT-X-SERVER-CONTROL defaulting CAN-BLOCK-RELOAD to false"})),ru.call(this,this.manifest),p.canSkipDateranges&&!p.hasOwnProperty("canSkipUntil")&&this.trigger("warn",{message:"#EXT-X-SERVER-CONTROL lacks required attribute CAN-SKIP-UNTIL which is required when CAN-SKIP-DATERANGES is set"})},"preload-hint"(){let p=this.manifest.segments.length,g=ir(f.attributes),x=g.type&&g.type==="PART";i.preloadHints=i.preloadHints||[],i.preloadHints.push(g),g.byterange&&(g.byterange.hasOwnProperty("offset")||(g.byterange.offset=x?m:0,x&&(m=g.byterange.offset+g.byterange.length)));let w=i.preloadHints.length-1;if(this.warnOnMissingAttributes_(`#EXT-X-PRELOAD-HINT #${w} for segment #${p}`,f.attributes,["TYPE","URI"]),!!g.type)for(let _=0;_<i.preloadHints.length-1;_++){let S=i.preloadHints[_];S.type&&S.type===g.type&&this.trigger("warn",{message:`#EXT-X-PRELOAD-HINT #${w} for segment #${p} has the same TYPE ${g.type} as preload hint #${_}`})}},"rendition-report"(){let p=ir(f.attributes);this.manifest.renditionReports=this.manifest.renditionReports||[],this.manifest.renditionReports.push(p);let g=this.manifest.renditionReports.length-1,x=["LAST-MSN","URI"];s&&x.push("LAST-PART"),this.warnOnMissingAttributes_(`#EXT-X-RENDITION-REPORT #${g}`,f.attributes,x)},"part-inf"(){this.manifest.partInf=ir(f.attributes),this.warnOnMissingAttributes_("#EXT-X-PART-INF",f.attributes,["PART-TARGET"]),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),ru.call(this,this.manifest)},daterange(){this.manifest.dateRanges.push(ir(f.attributes));let p=this.manifest.dateRanges.length-1;this.warnOnMissingAttributes_(`#EXT-X-DATERANGE #${p}`,f.attributes,["ID","START-DATE"]);let g=this.manifest.dateRanges[p];g.endDate&&g.startDate&&new Date(g.endDate)<new Date(g.startDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE END-DATE must be equal to or later than the value of the START-DATE"}),g.duration&&g.duration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE DURATION must not be negative"}),g.plannedDuration&&g.plannedDuration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE PLANNED-DURATION must not be negative"});let x=!!g.endOnNext;if(x&&!g.class&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must have a CLASS attribute"}),x&&(g.duration||g.endDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must not contain DURATION or END-DATE attributes"}),g.duration&&g.endDate){let _=g.startDate.getTime()+g.duration*1e3;this.manifest.dateRanges[p].endDate=new Date(_)}if(!T[g.id])T[g.id]=g;else{for(let _ in T[g.id])if(g[_]&&JSON.stringify(T[g.id][_])!==JSON.stringify(g[_])){this.trigger("warn",{message:"EXT-X-DATERANGE tags with the same ID in a playlist must have the same attributes values"});break}let w=this.manifest.dateRanges.findIndex(_=>_.id===g.id);this.manifest.dateRanges[w]=rr(this.manifest.dateRanges[w],g),T[g.id]=rr(T[g.id],g),this.manifest.dateRanges.pop()}},"independent-segments"(){this.manifest.independentSegments=!0},"content-steering"(){this.manifest.contentSteering=ir(f.attributes),this.warnOnMissingAttributes_("#EXT-X-CONTENT-STEERING",f.attributes,["SERVER-URI"])}}[f.tagType]||a).call(t)},uri(){i.uri=f.uri,r.push(i),this.manifest.targetDuration&&!("duration"in i)&&(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),i.duration=this.manifest.targetDuration),o&&(i.key=o),i.timeline=d,n&&(i.map=n),m=0,this.lastProgramDateTime!==null&&(i.programDateTime=this.lastProgramDateTime,this.lastProgramDateTime+=i.duration*1e3),i={}},comment(){},custom(){f.segment?(i.custom=i.custom||{},i.custom[f.customType]=f.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[f.customType]=f.data)}})[f.type].call(t)})}warnOnMissingAttributes_(t,r,i){let n=[];i.forEach(function(o){r.hasOwnProperty(o)||n.push(o)}),n.length&&this.trigger("warn",{message:`${t} lacks required attribute(s): ${n.join(", ")}`})}push(t){this.lineStream.push(t)}end(){this.lineStream.push(`
`),this.manifest.dateRanges.length&&this.lastProgramDateTime===null&&this.trigger("warn",{message:"A playlist with EXT-X-DATERANGE tag must contain atleast one EXT-X-PROGRAM-DATE-TIME tag"}),this.lastProgramDateTime=null,this.trigger("end")}addParser(t){this.parseStream.addParser(t)}addTagMapper(t){this.parseStream.addTagMapper(t)}}});function ou(e){let t=null;try{let i=new pn;i.push(e),i.end(),t=i.manifest}catch{}if(!t||!Array.isArray(t.segments))return j("Parsing error");if(t.segments.some(i=>typeof i.duration!="number"))return j("Missing duration");let r=t.segments.reduce((i,n)=>(typeof n.duration=="number"&&(i+=n.duration),i),0);return L(r)}function Dg(e,t){let r=ou(e).unwrapOr("unknown"),i;return t.includes(".mp3")?i={video:!1,audio:{codec:Xe("MP3"),bitrate:D}}:i={audio:!1,video:{codec:We("H264"),fps:D,dimensions:D,quality:D,bitrate:D}},L({builder:"RawHls",protocol:"hls",content_length:D,duration:r,container:ce("Mp4"),av:i})}function Og(e,t){let r=null;try{let o=new pn;o.push(e),o.end(),r=o.manifest}catch{}if(!r||!r.playlists)return j("Parsing error");let i=r.playlists,n=r.mediaGroups.AUDIO;return L(i.map(({uri:o,attributes:s})=>{let a=new URL(o,t).href,l=D;if(s.AUDIO&&n){let A=n[s.AUDIO]?.Default?.uri;if(!A){for(let h of Object.keys(n[s.AUDIO]??{}))if(A=n[s.AUDIO]?.[h]?.uri,A)break}A&&(l=C(new URL(A,t).href))}let u={audio:!1,video:"unknown"};s.CODECS&&(u=bl(s.CODECS,D,D));let d=Qe(u,A=>({codec:Xe(A),bitrate:D}),A=>{let h=D,p=D,g=D,x=D;return s.BANDWIDTH&&(x=C(s.BANDWIDTH)),s["FRAME-RATE"]&&(h=C(s["FRAME-RATE"])),s.RESOLUTION&&(p=C(s.RESOLUTION),g=C(zr(s.RESOLUTION.height))),{codec:We(A),bitrate:x,fps:h,dimensions:p,quality:g}}),m=l.isSome()?{audio:l.unwrap(),video:a}:d.video?{audio:!1,video:a}:{video:!1,audio:a},T=[ce("Mp4"),ce("WebM"),ce("Mkv")];T=T.filter(A=>{if(d.video&&d.audio){let h=d.audio.codec.name,p=d.video.codec.name,g=!!A.supported_video_codecs.find(w=>w==p),x=!!A.supported_audio_codecs.find(w=>w==h);return g&&x}else if(d.video){let h=d.video.codec.name;return!!A.supported_video_codecs.find(p=>p==h)}else if(d.audio){let h=d.audio.codec.name;return!!A.supported_audio_codecs.find(p=>p==h)}return!1});let f=T[0]??ce("Mkv");return[{builder:"Hls",protocol:"hls",content_length:D,duration:"unknown",av:d,container:f},m]}))}var Mg=N(()=>{"use strict";ae();Ki();xt();$r();fr();Ge();Sg();je()});var lu={};ne(lu,{FireProbeForHTTPMedia:()=>w0,MPDProbe:()=>Vr,MasterHLSProbe:()=>tr,MaybeCreateProbeFromNetworkRequest:()=>A0});function v0(e){let t=null,r=!1;for(let[n,o]of e)if(n.av.video===!1){let s=n.av.audio.bitrate.unwrapOr(0);t?t.bitrate<s&&(t={id:o,bitrate:s,audio:n.av.audio}):t={id:o,bitrate:s,audio:n.av.audio}}else r=!0;return!r||!t?e.map(([n,o])=>[n,o,D]):e.filter(([n,o])=>!!n.av.video).map(([n,o])=>{let s=D;return n.av.audio===!1&&(n.av={audio:{...t.audio},video:n.av.video},s=C(t.id)),[n,o,s]})}function su(e,t){let r={bulk:e.bulk,originalId:e.originalId,isPrivate:e.isPrivate,tabId:e.tabId,title:e.title,topUrl:e.topUrl,pageTitle:e.pageTitle,pageUrl:e.pageUrl,thumbnailUrl:e.thumbnailUrl,thumbnailUrl2:e.thumbnailUrl2,headers:e.headers,extension:t.av.video?t.container.extension:t.container.audio_only_extension,core_media:t};if(t.av.video&&(t.av.video.bitrate.isSome()&&(r.bitrate=t.av.video.bitrate.unwrap()),t.av.video.dimensions.isSome())){let i=t.av.video.dimensions.unwrap();r.size=`${i.width}x${i.height}`}return t.duration!="unknown"&&typeof t.duration=="number"&&(r.duration=t.duration),r}function w0(e){let t=new Map,r={core_media:e.core_media,manifest_url:e.url,to_copy:e.url,sources:{video:e.url,audio:!1},id:`variant_${crypto.randomUUID()}`};t.set(r.id,r);let i={is_low_quality:!0,timestamp:Date.now(),incognito:e.isPrivate,tab_id:e.tabId,page_url:e.topUrl,page_title:e.pageTitle,headers:e.headers,id:`downloadable_${pt(e.url+e.tabId)}`,title:e.title,favicon_url:new URL(e.topUrl).origin+"/favicon.ico",variants:t,thumbnail_url:e.thumbnailUrl2};Zt(i)}function A0(e,t,r){if(e.method!="GET")return[];Array.isArray(r)||(r=[]),t=t?.toLowerCase()??"";let i=[];return un.canHandle(e.url,t,r)&&i.push(new un(e.url,r)),dn.canHandle(e.url,t,r)&&i.push(new dn(e.url,r)),tr.canHandle(e.url,t,r)&&i.push(new tr(e.url,r)),Vr.canHandle(e.url,t,r)&&i.push(new Vr(e.url,r)),i}var au,na,Ct,Vr,tr,ra=N(()=>{"use strict";ae();_g();Ul();fr();vg();zo();Al();Mg();au=te(),na=($e(),P(ze)),Ct=(he(),P(ge));Vr=class{constructor(t,r){this.mpd_url=t,this.headers=r}static canHandle(t,r,i){if(r.includes("application/dash+xml"))return!0;try{if(new URL(t).pathname.endsWith(".mpd"))return!0}catch{}return!1}async onHitDataAvailable(t){let r=await Ct.request({url:this.mpd_url,headers:this.headers});if(!r.ok){console.warn("Failed to fetch MPD content");return}let i=await r.text(),n=hg(i);if(n.isErr()){console.error("Failed to parse MPD",n.unwrapErr());return}let o=v0(n.unwrap()),s={is_low_quality:!1,timestamp:Date.now(),incognito:t.isPrivate,tab_id:t.tabId,page_url:t.topUrl,page_title:t.pageTitle,headers:t.headers,id:`downloadable_${pt(this.mpd_url+t.tabId)}`,title:t.title,favicon_url:new URL(t.topUrl).origin+"/favicon.ico",variants:new Map,thumbnail_url:t.thumbnailUrl2};for(let[a,l,u]of o){let c={...su(t,a),id:"dash:"+Ct.hashHex(l),descrPrefix:au._("dash_streaming"),chunked:"dash-adp",group:"grp-"+Ct.hashHex(this.mpd_url),mpd_url:this.mpd_url};if(a.av.video!==!1){c.mpd_video_id=l;let m={core_media:a,to_copy:this.mpd_url,manifest_url:this.mpd_url,sources:{video:l,audio:!1},id:`variant_${crypto.randomUUID()}`};if(a.av.video.bitrate.isSome()&&(c.bitrate=a.av.video.bitrate.unwrap()),u.isSome()&&(c.mpd_audio_id=u.unwrap(),m.sources.audio=c.mpd_audio_id),a.av.video.dimensions.isSome()){let T=a.av.video.dimensions.unwrap();c.size=`${T.width}x${T.height}`}a.duration!="unknown"&&typeof a.duration=="number"&&(c.duration=a.duration),s.variants.set(m.id,m),na.dispatch("hit.new",c)}else c.mpd_audio_id=l,a.av.audio.bitrate.isSome()&&(c.bitrate=a.av.audio.bitrate.unwrap()),a.duration!="unknown"&&typeof a.duration=="number"&&(c.duration=a.duration),na.dispatch("hit.new",c)}Zt(s)}},tr=class{constructor(t,r){this.m3u8_url=t,this.headers=r}static canHandle(t,r,i){if(t.includes(".m3u8")||r.includes("mpegurl"))return!0;for(let n of $o)if(n.canHandleHLS&&n.canHandleHLS(t,r,i))return!0;return!1}async onHitDataAvailable(t){let r=await Ct.request({url:this.m3u8_url,headers:this.headers});if(!r.ok){console.warn("Failed to fetch M3U8 content");return}let i=await r.text(),n=Og(i,this.m3u8_url);if(n.isErr()){let a=Dg(i,this.m3u8_url);if(a.isErr()){console.warn("can't parse M3U8");return}let l=a.unwrap(),d={...su(t,l),id:"rawhls:"+Ct.hashHex(this.m3u8_url),descrPrefix:au._("hls_streaming"),chunked:"hls",group:"grp-"+Ct.hashHex(this.m3u8_url),mediaManifest:this.m3u8_url};na.dispatch("hit.new",d);let c=new Map,m={core_media:l,to_copy:this.m3u8_url,manifest_url:this.m3u8_url,sources:Qe(l.av,f=>this.m3u8_url,f=>this.m3u8_url),id:`variant_${crypto.randomUUID()}`};c.set(m.id,m);let T={is_low_quality:!0,timestamp:Date.now(),incognito:t.isPrivate,tab_id:t.tabId,page_url:t.topUrl,page_title:t.pageTitle,headers:t.headers,id:`downloadable_${pt(this.m3u8_url+t.tabId)}`,title:t.title,favicon_url:new URL(t.topUrl).origin+"/favicon.ico",variants:c,thumbnail_url:t.thumbnailUrl2};T.variants.set(m.id,m),Zt(T);return}let o=n.unwrap();{let a=o.as_iter().find(([,l])=>!!l.video).map(([,l])=>l.video);if(a.isSome()){let l=a.unwrap(),u=await Ct.request({url:l,headers:this.headers});if(!u.ok){console.warn("Failed to fetch M3U8 content");return}let d=await u.text(),c=ou(d);if(c.isOk())for(let[m]of o)m.duration=c.unwrap()}}let s={is_low_quality:!1,timestamp:Date.now(),incognito:t.isPrivate,tab_id:t.tabId,page_url:t.topUrl,page_title:t.pageTitle,headers:t.headers,id:`downloadable_${pt(this.m3u8_url+t.tabId)}`,title:t.title,favicon_url:new URL(t.topUrl).origin+"/favicon.ico",variants:new Map,thumbnail_url:t.thumbnailUrl2};for(let[a,l]of o){let u=su(t,a),d="";l.audio&&(d+=l.audio),l.video&&(d+=l.video);let c={...u,id:"hls:"+Ct.hashHex(d),descrPrefix:au._("hls_streaming"),chunked:"hls",group:"grp-"+Ct.hashHex(this.m3u8_url+a.container.name),masterManifest:this.m3u8_url},m=l.video||l.audio,T={core_media:a,to_copy:m,manifest_url:this.m3u8_url,sources:l,id:`variant_${crypto.randomUUID()}`};s.variants.set(T.id,T),!l.audio&&l.video?c.mediaManifest=l.video:!l.video&&l.audio?c.mediaManifest=l.audio:l.video&&l.audio&&(c.audioMediaManifest=l.audio,c.videoMediaManifest=l.video),na.dispatch("hit.new",c)}Zt(s,t.bulk)}}});var Cg={};ne(Cg,{default:()=>I0,forbidden:()=>kg,matchHit:()=>Ng});async function P0(e){let t=e.thumbnailUrl&&e.videoDetails.thumbnail?.thumbnails[0]?.url,r={id:"tbvws:"+e.videoId,group:"tbvws:"+e.videoId,isPrivate:e.isPrivate,tabId:e.tabId,title:e.title,from:"tbvws",videoId:e.videoId,topUrl:e.topUrl,pageTitle:e.pageTitle,pageUrl:e.pageUrl,thumbnailUrl:t,thumbnailUrl2:t??"/content/images/no-thumbnail.png",duration:parseInt(e.videoDetails.lengthSeconds)||void 0,headers:[],baseJs:e.baseJs,bulk:e.bulk};if(e.hlsManifestUrl){new S0(e.hlsManifestUrl,{}).onHitDataAvailable(r);return}let i=e.adaptiveFormats,n=e.formats,o=e.formats2,s=i.as_iter().filter(p=>typeof p.url=="string").map(p=>({result:Xo(p,"dash"),json:p})).toArray(),a=n.as_iter().filter(p=>typeof p.url=="string").map(p=>({result:Xo(p,"non-adaptative"),json:p})).toArray(),u=[...o.as_iter().filter(p=>typeof p.url=="string").map(p=>({result:Qf(p),json:{url:p.url}})).toArray(),...s,...a];u.forEach(({result:p,json:g})=>{p.isErr()&&console.warn("tbvws JSON parsing error",p.unwrapErr(),g)});let d=u.as_iter().filterMap(({result:p,json:g})=>p.toOption().map(x=>({core_media:x,url:g.url}))).toArray(),c=d.as_iter().filter(({core_media:p})=>!!p.av.audio&&!!p.av.video).map(p=>p).toArray(),m=d.as_iter().filter(({core_media:p})=>!p.av.audio).map(p=>p).toArray(),T=d.as_iter().filter(({core_media:p})=>!p.av.video).map(p=>p).sort((p,g)=>{if(p.core_media.av.audio&&g.core_media.av.audio){let x=p.core_media.av.audio.bitrate.unwrapOr(0),w=g.core_media.av.audio.bitrate.unwrapOr(0);return x-w}else throw"unreachable"}),f=[];for(let p of m){let g=T.as_iter().filter(x=>x.core_media.container.name==p.core_media.container.name).toArray();if(g.length>0){let x=g[0],w={...p,core_media:{...p.core_media,av:{video:p.core_media.av.video,audio:x.core_media.av.audio}},videoUrl:p.url,audioUrl:x.url};delete w.url,f.push(w)}}let A=new Map,h=[...c,...f].as_iter().map(({core_media:p,...g})=>{let x=p.av.video,w={...r,...g,extension:p.container.extension,videoCodec:x.codec.name,audioCodec:p.av.audio.codec.name,core_media:p};if(x.quality.isSome()&&(w.quality=x.quality.unwrap()+"p"),x.fps.isSome()&&(w.fps=x.fps.unwrap()),x.dimensions.isSome()){let _=x.dimensions.unwrap();w.size=`${_.width}x${_.height}`}{let _,S,M;g.videoUrl&&g.audioUrl?(M=S=g.videoUrl,_={video:g.videoUrl,audio:g.audioUrl}):g.videoUrl?(M=S=g.videoUrl,_={video:g.videoUrl,audio:!1}):g.audioUrl?(M=S=g.audioUrl,_={video:!1,audio:g.audioUrl}):(M=S=g.url||"unreachable",_={video:g.url||"unreachable",audio:!1});let I={core_media:p,id:`variant_${crypto.randomUUID()}`,manifest_url:S,sources:_,to_copy:M};r.baseJs&&(I.base_js=r.baseJs),A.set(I.id,I)}return w}).toArray();{let p={is_low_quality:!1,timestamp:Date.now(),incognito:r.isPrivate,tab_id:r.tabId,page_url:r.topUrl,page_title:r.pageTitle,headers:r.headers,id:`downloadable_${pt(r.topUrl+r.tabId)}`,title:r.title,favicon_url:new URL(r.topUrl).origin+"/favicon.ico",variants:A,thumbnail_url:r.thumbnailUrl2};Zt(p)}h.forEach(p=>{x0.dispatch("hit.new",p)})}function R0(){return Rg.isProbablyAvailable()&&Rg.isAtLeastVersion("2.0.0")}function Ng(e){return![e.url,e.audioUrl,e.videoUrl,e.pageUrl,e.topUrl].every(t=>t?!Ig.test(t)&&!M0.test(t):!0)}async function kg(){let e=qt._("chrome_noyt_text"),t=oa.hash(e),r=qt._("chrome_noyt_text3"),i=oa.hash(r),n=r;i==-1960581238&&t!=-1126813505&&(n=e);try{switch((await E0.alert({title:qt._("chrome_warning_yt"),text:[n,qt._("chrome_noyt_text2")],height:400,buttons:[{text:qt._("chrome_install_firefox"),className:"btn-outline-secondary",close:!0,trigger:{what:"installFirefox"}},{text:qt._("chrome_install_fx_vdh"),className:"btn-outline-primary",close:!0,trigger:{what:"vdhForFirefox"}}]})).what){case"installFirefox":return Pg.gotoOrOpenTab("https://getfirefox.com/");case"vdhForFirefox":return Pg.gotoOrOpenTab("https://addons.mozilla.org/firefox/addon/video-downloadhelper/")}}catch(o){console.error("tbvws error",o)}}var qt,fn,x0,T0,oa,Pg,E0,Rg,S0,D0,O0,M0,Ig,I0,qg=N(()=>{"use strict";zf();Ul();zo();qt=te(),fn=qt.browser,x0=($e(),P(ze)),T0=(an(),P(on)),oa=(he(),P(ge)),Pg=(Ir(),P(Rr)),E0=(Zi(),P(Yi)),Rg=(mt(),P(ft)),{MasterHLSProbe:S0}=(ra(),P(lu)),D0="youtube",O0=new RegExp("^https?://([^/]*\\.)?youtube(?:\\-nocookie)?(\\.co)?.([^./]+)/"),M0=new RegExp("^https?://([^/]*.)?googlevideo\\."),Ig=new RegExp("^https?://([^/]*\\.)?youtube(\\.co)?.([^./]+)/.*");qt.rpc.listen({tbvwsDetectedVideo:async e=>{try{await P0(e)}catch(t){console.error("VDH error: detectedVideo",t)}}});fn.webNavigation.onCompleted.addListener(async function(e){let t=await qt.prefs,r={tabId:e.tabId,frameIds:[e.frameId]};if(O0.test(e.url)){let i;for(let n=0;n<5;n++){try{let o=await fn.scripting.executeScript({target:r,world:fn.scripting.ExecutionWorld.MAIN,func:()=>window.ytcfg?.data_?.VISITOR_DATA});if(typeof o?.[0]?.result=="string"){i=o[0].result;break}}catch{}await new Promise(o=>setTimeout(o,1e3))}try{let n=await fn.tabs.get(e.tabId);oa.executeScriptWithGlobal(r,{visitor_data:i,_$vdhData:{...r,isPrivate:!1},_$vdhSmartNameSpecs:await T0.getSpecs(e.url),_$vdhTopUrl:n.url,_$vdhExtractMethod:t.tbvwsExtractionMethod,_$vdhSupportChallenge:R0()},"/injected/tbvws.js")}catch(n){console.error("Cannot find tab",n)}}if(t.bulkEnabled&&Ig.test(e.url))try{let i=await fn.tabs.get(e.tabId);oa.executeScriptWithGlobal(r,{_$vdhTopUrl:i.url},"/injected/tbvws-bulk.js")}catch(i){console.error("VDH error: could not inject bulk script",i)}},{url:[{hostContains:D0}]});I0={matchHit:Ng,forbidden:kg}});var Bg={};ne(Bg,{BulkDownload:()=>C0});async function C0(e,t){let r=new Set;for(let i of e.bulk_ids){let n=`https://www.${k0}.com/watch?v=${i}&vdh-bulk=1`,o=await sa.tabs.create({url:n,active:!1});await sa.tabs.update(o.id,{muted:!0}),r.add(o.id)}for(let i=0;i<60;i++){let n=await la.getSerializedHits();for(let o of n){let s=o[0];for(let a of r){let l=s.tabId==a,u=!!s.masterManifest,d=s.extension=="mp4";if(l&&u&&d){r.delete(s.tabId),N0.execute(t,s.id),sa.tabs.remove(s.tabId);break}}}if(r.size==0){console.log("All hits were found and downloaded");break}await new Promise(o=>setTimeout(o,1e3))}}var aa,sa,la,N0,k0,Vg=N(()=>{"use strict";Ge();mr();ae();aa=te(),sa=aa.browser,la=($e(),P(ze)),N0=(da(),P(ua)),k0="youtube";aa.rpc.listen({tbvwsSelectedIds:e=>{let t=[],{flat:r}=la.getHits();for(let i of r.values())i.from=="tbvws-bulk"&&i.topUrl==e.topUrl&&!i.running&&t.push(i.id);if(t.length>0&&la.dispatch("hit.delete",t),e.ids.length>0){let i="tbvws-bulk:"+Math.floor(Math.random()*1e9),n={id:i,group:i,title:aa._("selected_media"),descrPrefix:aa._("bulk_n_videos",""+e.ids.length),from:"tbvws-bulk",bulk_ids:e.ids,pageUrl:e.pageUrl,topUrl:e.topUrl,thumbnailUrl:sa.runtime.getURL("/content/images/tbvws.png"),core_media:{content_length:D,builder:"YoutubeBulk",protocol:"unknown",duration:"unknown",container:ce("Mp4"),av:Wt()}};la.dispatch("hit.new",n)}}})});var Fg=y((qD,Hg)=>{"use strict";var mn=te(),q0=(Yt(),P(Kt));async function B0(e){let t=await mn.prefs;Math.round(Date.now()/1e3)<t.donateNotAgainExpire||q0.checkLicense().then(r=>{r&&r.status=="accepted"||mn.ui.open("funding",{type:t.alertDialogType,url:"content/funding.html",height:550})})}Hg.exports.newDownload=async function(){let e=await mn.prefs,t=e.downloadCount;t++,e.downloadCount=t,t>0&&t%100==0&&B0(t)};mn.rpc.listen({fundingLater:async()=>{let e=await mn.prefs;e.donateNotAgainExpire=Math.round(Date.now()/1e3)+60*60*24*30}})});async function X0(e,t,r,i,n,o){let s=await K.prefs,a=!!r,l=Date.now(),u=!1;if(u=Lg.matchHit(e),mi.noyt&&u){Lg.forbidden();return}le.update(e.id,{operation:"queued"});let d,c;{let{status:_,info:S}=await Bt.check();d=_,d&&(c=S.version)}let m;{if(e.gallery_urls)m="gallery";else if(e.core_media.builder=="YoutubeBulk")m="youtube_bulk";else if(e.core_media.builder=="Hls")m="hls";else if(e.core_media.builder=="RawHls")m="hls";else if(e.core_media.builder=="MPD")m="mpd";else if(e.core_media.builder=="HTTPMedia"){let _=s.coappDownloads;if(d&&_=="ask"){let M=await W0();_=M.mode,M.notAgain&&(s.coappDownloads=_)}_=="browser"||!d&&!a?m="file_inbrowser":m="file_coapp"}else e.core_media.builder=="YoutubeFormat"&&(m="youtube_format");if(!m)throw new Error("No download strategy for builder: "+e.core_media.builder)}if(i&&m!="hls"&&m!="mpd"&&m!="file_coapp"&&m!="youtube_bulk"){nr.alert({title:K._("dialog_audio_impossible_title"),text:K._("dialog_audio_impossible")});return}if(m!="file_inbrowser"&&m!="gallery"){if(!d){nr.alert({title:K._("coapp_required"),text:K._("coapp_required_text"),buttons:[{text:K._("coapp_install"),className:"btn-success",rpcMethod:"installCoApp"}]});return}let _=U0;if(m=="mpd"&&(_=j0),!Ug.isMinimumVersion(c,_)){try{await Bt.call("quit")}catch{}await new Promise(I=>setTimeout(I,2e3));let{status:S,info:M}=await Bt.check();if(!S||!Ug.isMinimumVersion(M.version,_)){Bt.call("quit"),nr.alert({title:K._("coapp_outofdate"),text:K._("coapp_outofdate_text",[M.version,_]),buttons:[{text:K._("coapp_update"),className:"btn-success",rpcMethod:"installCoApp"}]});return}c=M.version}}let T=!1;{let _=!1;{let{status:S}=await H0.checkLicense();_=S=="accepted"||S=="unneeded"}if(!_){let S=mi.target=="google",M=mi.target=="mozilla",I=mi.target=="microsoft",U=120,W=U*60*1e3,q=await B(Xt),$=l-q,O="https://www.downloadhelper.net/convert";if(i&&(m=="mpd"||m=="hls"||u)){nr.alert({title:K._("chrome_premium_required"),text:K._("converter_reg_audio"),buttons:[{text:K._("continue"),className:"btn-success",rpcMethod:"goto",rpcArgs:[O]}]});return}if(a){nr.alert({title:K._("chrome_premium_required"),text:K._("converter_needs_reg"),buttons:[{text:K._("continue"),className:"btn-success",rpcMethod:"goto",rpcArgs:[O]}]});return}if((S||I||a)&&(m=="mpd"||m=="hls"||u)&&$<W){nr.alert({title:K._("chrome_premium_required"),text:K._("chrome_premium_hls",[U]),buttons:[{text:K._("continue"),className:"btn-success",rpcMethod:"goto",rpcArgs:[O]}]});return}M&&u&&!i&&(T=!0)}}let f,A;{A=e.core_media.container.extension,(i||!e.core_media.av.video)&&(A=e.core_media.container.audio_only_extension);let _=e.title??"video";f=await F0.getFilenameFromTitle(_,A)}if(m=="gallery"){for(let _ of e.gallery_urls){let M={url:new URL(_,e.topUrl).href};gn.downloads.download(M)}return}if(m=="youtube_bulk"){V0.BulkDownload(e,i?"quickdownloadaudio":"quickdownload");return}if(m=="file_inbrowser"){le.updateRunning(e.id,1),le.update(e.id,{operation:"downloading",opStartDate:Date.now()}),le.updateProgress(e.id,0);let _={url:e.url,saveAs:!t,filename:f};mi.target=="mozilla"&&(_.incognito=e.isPrivate);let S=await gn.downloads.download(_);n.set(e.id,{inbrowser:S});let M=0;for(;;){let I=await gn.downloads.search({id:S});if(I.length>0){let U=I[0],W=U.bytesReceived-M;M=U.bytesReceived;let q=U.bytesReceived/U.totalBytes;if(le.update(e.id,{raw_bitrate:W}),le.updateProgress(e.id,100*q),U.error){Wg("No download item found");break}if(U.state=="complete")break}else break;await new Promise(U=>setTimeout(U,1e3))}return"inbrowser"}let h,p=s.lastDownloadDirectory;{if(p=await jg(p),t)h=(await Bt.call("makeUniqueFileName",p,f)).filePath;else{let _=await nr.saveAs(f,p);if(_)h=_.filePath,h.endsWith(`.${A}`)||(h+=`.${A}`),p=_.directory;else return}p=await jg(p),s.rememberLastDir&&(s.lastDownloadDirectory=p)}if(le.updateRunning(e.id,1),n.size>=s.downloadControlledMax){for(o.push(e.id);;)if(await new Promise(_=>setTimeout(_,2e3)),n.size<s.downloadControlledMax&&o[0]==e.id){o.shift();break}}n.set(e.id,{}),le.update(e.id,{operation:"downloading",opStartDate:Date.now()}),le.updateProgress(e.id,1);let g=0,x=(_,S)=>{let M=1024*(parseFloat(S.bitrate)??0);if(M>0&&le.update(e.id,{raw_bitrate:M}),_<0&&(_=0),g>0){let I=Math.floor(100*_/g);I>100&&(I=1/0),le.updateProgress(e.id,I)}},w=_=>n.set(e.id,{ffmpeg_pid:_});if(m=="hls"){let _=e,S=_.mediaManifest??_.videoMediaManifest??_.audioMediaManifest,M=await Se.info(S,!0,e.headers);g=parseFloat(M.format?.duration);let I={filePath:h,qr_code_needed:T,headers:e.headers,on_progress:x,on_start:w};i||!e.core_media.av.video?await Se.sideDownload(null,_.audioMediaManifest??_.mediaManifest,I):_.mediaManifest?await Se.sideDownload(_.mediaManifest,null,I):await Se.sideDownload(_.videoMediaManifest,_.audioMediaManifest,I)}if(m=="mpd"){let _=e,S=await Se.info(_.mpd_url,!0,e.headers);g=parseFloat(S.format?.duration);let M={filePath:h,qr_code_needed:T,headers:_.headers,on_progress:x,on_start:w};i?await Se.sideDownloadMPD(_.mpd_url,null,_.mpd_audio_id,M):await Se.sideDownloadMPD(_.mpd_url,_.mpd_video_id,_.mpd_audio_id,M)}if(m=="file_coapp"){if(g==0){let S=await Se.info(e.url,!0,e.headers);g=parseFloat(S.format?.duration)}let _={filePath:h,qr_code_needed:T,headers:e.headers,on_progress:x,on_start:w};i?await Se.sideDownload(null,e.url,_):await Se.sideDownload(e.url,null,_)}if(m=="youtube_format"){if(!e.baseJs)throw new Error("baseJs expected");let _,S;if(e.videoUrl||e.url){let q=e.videoUrl??e.url;S=new URL(q)}else{console.warn("unconsistent hit");return}e.audioUrl&&(_=new URL(e.audioUrl));let M=S.searchParams.get("n"),I;M&&(I=await Bt.call("vm.run",`((a) => {${e.baseJs}})('${M}')`),S.searchParams.set("n",I)),_&&_.searchParams.set("n",I);let U=await Se.info(S.href,!0,e.headers);g=parseFloat(U.format?.duration);let W={filePath:h,qr_code_needed:T,headers:e.headers,on_progress:x,on_start:w};_?await Se.sideDownload(S.href,_.href,W):await Se.sideDownload(S.href,null,W)}if(a){let _=(I,U)=>{I<0&&(I=0),le.update(e.id,{raw_bitrate:1024*(parseFloat(U.bitrate)??0)}),g>0&&le.updateProgress(e.id,Math.floor(100*I/g))},S=I=>n.set(e.id,{ffmpeg_pid:I});le.update(e.id,{operation:"converting",opStartDate:Date.now()});let M=await Se.convert2(h,null,r,_,S);if(!s.converterKeepTmpFiles)try{await Bt.call("fs.unlink",h),h=M}catch{}}return T&&!s.qrMessageNotAgain&&!e.bulk&&K.ui.open("explainqr#"+encodeURIComponent(e.id),{type:s.alertDialogType,url:"content/explain-qr.html"}),le.update(e.id,{localFilePath:h}),le.update(e.id,{localDirectory:p}),await ee(Xt,l),h}async function gi(e,t,r,i,n,o){let s=await K.prefs,a;try{if(a=await X0(e,t,r,i,n,o),a&&(await L0.newDownload(),s.notifyReady)){let l=K._("file_ready",a=="inbrowser"?e.title:a);e.isPrivate&&s.noPrivateNotification||gn.notifications.create(e.id,{type:"basic",title:K._("vdh_notification"),iconUrl:gn.runtime.getURL(`/content2/icons/${mi.channel}-color.png`),message:l})}}catch(l){console.error(l),Wg(l)}finally{n.delete(e.id),le.updateRunning(e.id,-1),le.updateProgress(e.id,null),le.update(e.id,{operation:null})}return a}var Wg,K,Lg,V0,mi,le,Bt,nr,Ug,H0,gn,F0,Se,L0,U0,j0,jg,W0,Xg=N(()=>{"use strict";Kr();({error:Wg}=(Xn(),P(Wn))),K=te(),Lg=(qg(),P(Cg)),V0=(Vg(),P(Bg)),mi=ur().buildOptions,le=(_n(),P(hn)),Bt=(mt(),P(ft)),nr=(Zi(),P(Yi)),Ug=(he(),P(ge)),H0=(Yt(),P(Kt)),gn=K.browser,F0=(an(),P(on)),Se=(ci(),P(di)),L0=Fg(),U0="2.0.9",j0="2.0.13",jg=async e=>{try{e=await Bt.call("path.homeJoin",e),await Bt.call("fs.mkdirp",e)}catch(t){console.error("mkdir error",t,e)}return e},W0=()=>nr.alert({title:K._("download_method"),text:[K._("download_modes1"),K._("download_modes2")],height:350,buttons:[{text:K._("download_with_browser"),className:"btn-primary",close:!0,trigger:{mode:"browser"}},{text:K._("download_with_coapp"),className:"btn-success",close:!0,trigger:{mode:"coapp"}}],notAgain:K._("download_method_not_again")})});var ua={};ne(ua,{availableActions:()=>$g,describeAll:()=>i1,execute:()=>Jg,execute_default:()=>r1});async function $g(e){let t=yn.getHit(e);if(!t)return[];let r=["details","copyurl"],i=t.operation=="downloading",n=t.operation=="converting",o=!!t.localFilePath,s=!!t.core_media?.av.audio,a=t.core_media?.builder=="YoutubeBulk",l=[],u="";return et.has(t.id)?(u="abort",l=["abort",...r]):o?(u=(await H.prefs)["default-action-1"],Ze.isDegradedVersion()?l=["deletehit",...r]:l=["openlocalfile","openlocalcontainer","deletehit",...r]):!n&&!i?(u=(await H.prefs)["default-action-0"],l=["quickdownload","download"],l=[...l,"quickdownloadaudio","downloadaudio"],l=[...l,"downloadconvert","blacklist","deletehit",...r]):(u="copyurl",l=r),l.sort((d,c)=>d==c?0:d==u?-1:c==u?1:0)}function G0(e){H.ui.open("details#"+encodeURIComponent(e.id),{type:"tab",url:"content/details.html"})}async function Q0(e){let t;if(e.core_media?.builder=="Hls"||e.core_media?.builder=="RawHls"){let r=e;t=r.videoMediaManifest??r.audioMediaManifest??r.mediaManifest??r.masterManifest}else e.core_media?.builder=="MPD"?t=e.mpd_url:e.core_media?.builder=="HTTPMedia"&&(t=e.url);if(t){try{du.call("main","copyToClipboard",t)}catch{}try{await navigator.clipboard.writeText(t)}catch{}try{await ca.scripting.executeScript({target:{tabId:e.tabId},func:r=>navigator.clipboard.writeText(r),args:[t]})}catch{}}}async function z0(e){yn.dispatch("hit.delete",e.id)}async function $0(e){"localFilePath"in e&&await hi.open(e.localFilePath)}async function J0(e){"localDirectory"in e&&await hi.open(e.localDirectory)}async function K0(e){let t=et.get(e.id);if(!t)throw new Error("Attempt to abord non-downloading hit");t.inbrowser&&ca.downloads.cancel(e.id),t.ffmpeg_pid&&await Ze.call("abortConvert",t.ffmpeg_pid)}async function Y0(e){let t=await H.prefs,r="dlconv#"+e.id;await H.rpc.call("main","embed",ca.runtime.getURL("content/dlconv-embed.html?panel="+r));let{outputConfigId:i}=await H.wait(r);return t.dlconvLastOutput=i,await gi(e,!0,i,!1,et,bn)}async function Z0(e){let t="content/blacklist-embed.html?panel=blacklist#"+encodeURIComponent(e.id);await du.call("main","embed",ca.runtime.getURL(t))}async function e1(){{let{status:a,info:l}=await Ze.check();if(!a){Hr.alert({title:H._("coapp_required"),text:H._("coapp_required_text"),buttons:[{text:H._("coapp_install"),className:"btn-success",rpcMethod:"installCoApp"}]});return}let u=l.version;if(!Gg.isMinimumVersion(u,uu)){try{await Ze.call("quit")}catch{}await new Promise(m=>setTimeout(m,2e3));let{status:d,info:c}=await Ze.check();if(!d||!Gg.isMinimumVersion(c.version,uu)){Ze.call("quit"),Hr.alert({title:H._("coapp_outofdate"),text:H._("coapp_outofdate_text",[c.version,uu]),buttons:[{text:H._("coapp_update"),className:"btn-success",rpcMethod:"installCoApp"}]});return}}}{let{status:a}=await zg.checkLicense();if(!(a=="accepted"||a=="unneeded")){Hr.alert({title:H._("converter_needs_reg"),buttons:[{text:H._("get_conversion_license"),className:"btn-success",rpcMethod:"goto",rpcArgs:["https://www.downloadhelper.net/convert"]}]});return}}H.ui.close("main");let e,t,r,i;{let a=await Ze.call("filepicker","pick_file","~/dwhelper","Video file"),l=a.split(`
`);e=l[0];let u=l[2];if(!e)return;let d=u.split("."),c=d.pop(),m=d.join(".")+"-combined."+c;if(a=await Ze.call("filepicker","pick_file","~/dwhelper","Audio file"),l=a.split(`
`),t=l[0],!t||(a=await Ze.call("filepicker","save_file","~/dwhelper","Save as\u2026",m),l=a.split(`
`),r=l[0],i=l[1],!r||!i))return}let n={id:r,group:r,operation:"converting",status:"inactive",opStartDate:Date.now(),descrPrefix:"merge",title:r};yn.dispatch("hit.new",n);let o=(a,l)=>{a<0&&(a=0);try{Ie.update(n.id,{raw_bitrate:1024*parseFloat(l.bitrate)})}catch{}},s=a=>et.set(n.id,{ffmpeg_pid:a});try{Ie.updateRunning(n.id,1),Ie.update(n.id,{localFilePath:r}),Ie.update(n.id,{localDirectory:i}),await hi.sideDownload(e,t,{filePath:r,merge:!0,on_progress:o,on_start:s})}catch(a){console.error(a),Qg(a)}finally{et.delete(n.id),Ie.updateRunning(n.id,-1),Ie.update(n.id,{operation:null})}}async function t1(){{let{status:s}=await Ze.check();if(!s){Hr.alert({title:H._("coapp_required"),text:H._("coapp_required_text"),buttons:[{text:H._("coapp_install"),className:"btn-success",rpcMethod:"installCoApp"}]});return}}{let{status:s}=await zg.checkLicense();if(!(s=="accepted"||s=="unneeded")){Hr.alert({title:H._("converter_needs_reg"),buttons:[{text:H._("get_conversion_license"),className:"btn-success",rpcMethod:"goto",rpcArgs:["https://www.downloadhelper.net/convert"]}]});return}}H.ui.close("main");let e=await H.prefs,t=await Hr.selectConvertFiles(e.lastDownloadDirectory||"dwhelper");if(!t)return;let r=t.selected,i=t.outputConfig,n=t.directory;e.dlconvLastOutput=i;let o=null;if(r.length==1){let s=hi.defaultOutputConfigs[i],a=r[0].split(".");a[a.length-1]=s.ext,o=a.join(".");let l=await Hr.saveAs(o,n);if(l)o=l.filePath,o.endsWith(`.${s.ext}`)||(o+=`.${s.ext}`);else return}for(let s of r){s=await Ze.call("path.homeJoin",n,s);let a=await hi.info(s,!0),l=parseFloat(a.format?.duration),u={id:s,group:s,operation:"converting",status:"inactive",opStartDate:Date.now(),descrPrefix:"local convert",title:s};yn.dispatch("hit.new",u);let d=(m,T)=>{m<0&&(m=0);try{Ie.update(u.id,{raw_bitrate:1024*parseFloat(T.bitrate)})}catch{}l>0&&Ie.updateProgress(u.id,Math.floor(100*m/l))},c=m=>et.set(u.id,{ffmpeg_pid:m});try{Ie.updateRunning(u.id,1),Ie.update(u.id,{localFilePath:s}),Ie.update(u.id,{localDirectory:n}),s=await hi.convert2(s,o,i,d,c)}finally{et.delete(u.id),Ie.updateRunning(u.id,-1),Ie.update(u.id,{operation:null})}}}function Jg(e,t){let r=yn.getHit(t),i=!!r.running,n=!!r.localFilePath,o=!i&&!n,s=!0;try{if(e=="details"&&G0(r),e=="copyurl")Q0(r);else{if(!i&&e=="deletehit")return z0(r),s;if(o){if(e=="download"&&gi(r,!1,null,!1,et,bn),e=="quickdownload"&&gi(r,!0,null,!1,et,bn),e=="downloadaudio"&&gi(r,!1,null,!0,et,bn),e=="quickdownloadaudio"&&gi(r,!0,null,!0,et,bn),e=="downloadconvert")return Y0(r),s;if(e=="blacklist")return Z0(r),s}else{if(i&&e=="abort")return K0(r),s;n&&(e=="openlocalfile"&&$0(r),e=="openlocalcontainer"&&J0(r))}}}catch(a){return Qg(a),s}return!s}async function r1(e){let t=await $g(e);return t.length>0?Jg(t[0],e):!1}function i1(){return{abort:{name:"abort",title:H._("action_abort_title"),description:H._("action_abort_description"),icon:"images/icon-action-abort-64.png",icon18:"images/icon-action-abort-64.png",catPriority:2},download:{name:"download",title:H._("action_download_title"),description:H._("action_download_description"),icon:"images/icon-action-download-64.png",icon18:"images/icon-action-download-64.png",catPriority:0},quickdownload:{name:"quickdownload",title:H._("action_quickdownload_title"),description:H._("action_quickdownload_description"),icon:"images/icon-action-quick-download2-64.png",icon18:"images/icon-action-quick-download2-64.png",catPriority:0},downloadaudio:{name:"downloadaudio",title:H._("action_downloadaudio_title"),description:H._("action_downloadaudio_description"),icon:"images/icon-action-download-only-sound-64.png",icon18:"images/icon-action-download-only-sound-64.png",catPriority:0},quickdownloadaudio:{name:"quickdownloadaudio",title:H._("action_quickdownloadaudio_title"),description:H._("action_quickdownloadaudio_description"),icon:"images/icon-action-quick-download-only-sound-64.png",icon18:"images/icon-action-quick-download-only-sound-64.png",catPriority:0},downloadconvert:{name:"downloadconvert",title:H._("action_downloadconvert_title"),description:H._("action_downloadconvert_description"),icon:"images/icon-action-download-convert-64.png",icon18:"images/icon-action-download-convert-64.png",catPriority:0},details:{name:"details",title:H._("action_details_title"),description:H._("action_details_description"),icon:"images/icon-action-details-64.png",icon18:"images/icon-action-details-64.png",catPriority:0},copyurl:{name:"copyurl",title:H._("action_copyurl_title"),description:H._("action_copyurl_description"),icon:"images/icon-action-copy-link-64.png",icon18:"images/icon-action-copy-link-64.png",catPriority:0},deletehit:{name:"deletehit",title:H._("action_deletehit_title"),description:H._("action_deletehit_description"),icon:"images/icon-action-delete-64.png",icon18:"images/icon-action-delete-64.png",catPriority:0},pin:{name:"pin",title:H._("action_pin_title"),description:H._("action_pin_description"),icon:"images/icon-action-pin-64.png",icon18:"images/icon-action-pin-64.png",catPriority:0},blacklist:{name:"blacklist",title:H._("action_blacklist_title"),description:H._("action_blacklist_description"),icon:"images/icon-action-blacklist-64.png",icon18:"images/icon-action-blacklist-64.png",catPriority:0},openlocalfile:{name:"openlocalfile",title:H._("action_openlocalfile_title"),description:H._("action_openlocalfile_description"),icon:"images/icon-action-play-64.png",icon18:"images/icon-action-play-64.png",catPriority:1},openlocalcontainer:{name:"openlocalcontainer",title:H._("action_openlocalcontainer_title"),description:H._("action_openlocalcontainer_description"),icon:"images/icon-action-open-dir-64.png",icon18:"images/icon-action-open-dir-64.png",catPriority:1}}}var Qg,Ze,H,du,yn,hi,Hr,zg,ca,Ie,Gg,et,bn,uu,da=N(()=>{"use strict";Xg();({error:Qg}=(Xn(),P(Wn))),Ze=(mt(),P(ft)),H=te(),du=Lt(),yn=($e(),P(ze)),hi=(ci(),P(di)),Hr=(Zi(),P(Yi)),zg=(Yt(),P(Kt)),ca=H.browser,Ie=(_n(),P(hn)),Gg=(he(),P(ge)),et=new Map,bn=[];uu="2.0.17";du.listen({convertLocal:t1,mergeLocal:e1})});var hn={};ne(hn,{clearHits:()=>Yg,create:()=>d1,progressReducer:()=>l1,reducer:()=>g1,setHitOperation:()=>m1,update:()=>Kg,updateOriginal:()=>p1,updateProgress:()=>f1,updateRunning:()=>c1});function l1(e={},t){let r;switch(t.type){case"hit.progress":r=e[t.payload.id],r!==t.payload.progress&&(e=Object.assign({},e,{[t.payload.id]:t.payload.progress}));break;case"hit.clear-progress":r=e[t.payload],typeof r<"u"&&(e=Object.assign({},e),delete e[t.payload]);break}return e}function u1(e){let t=e.status,{url:r,urls:i}=s1.current();return e.status=="running"?"running":(e.status=="active"&&e.topUrl!=r?e.topUrl in i?t="inactive":t="orphan":e.status=="inactive"&&!(e.topUrl in i)?t="orphan":(e.status=="inactive"||e.status=="orphan")&&e.topUrl==r&&(t="active"),t=="orphan"&&e.pinned&&(t="pinned"),t)}function d1(e){gt.dispatch("hit.new",e)}function Kg(e,t={}){gt.dispatch("hit.update",{id:e,changes:t})}function c1(e,t){gt.dispatch("hit.updateRunning",{id:e,runningDelta:t})}function p1(e,t={}){gt.dispatch("hit.updateOriginal",{id:e,changes:t})}function f1(e,t){t===null?gt.dispatch("hit.clear-progress",e):gt.dispatch("hit.progress",{id:e,progress:t})}function m1(e,t){let r=gt.getHit(e);r&&r.operation!==t&&Kg(e,{operation:t})}function Yg(e){let t=[],{flat:r}=gt.getHits();for(let i of r.values())(e=="all"&&i.status!="running"&&i.status!="pinned"||e=="pinned"&&i.status=="pinned"||e=="inactive"&&i.status=="inactive"||e=="orphans"&&i.status=="orphan")&&t.push(i.id);gt.dispatch("hit.delete",t)}function g1(e,t){e||(e={flat:new Map});let r=e.flat;function i(n,o={}){!n.referrer&&o.pageUrl&&(o.referrer=o.pageUrl);let s=o.core_media??n.core_media;n.core_media&&o.core_media&&(s=Dd(n.core_media,o.core_media));let a=n.status!="orphan";if(n={...n,...o,core_media:s},n={...n,status:u1(n)},n.status=="orphan"&&a){let l=Date.now(),u=n1.unsafe_prefs.orphanExpiration*1e3;n.orphanT0=l,n.orphanT=l+u,setTimeout(()=>gt.dispatch("hit.orphanTimeout",n.id),u+100)}return n}switch(t.type){case"hit.new":{if(r.size>1e4)return console.error("Hit DB is reaching limit. Something is wrong. Abording."),{flat:r};let n=t.payload;n.created=new Date().getTime();let o=r.get(n.id)??{};if(o.status=="running")return{flat:r};o.status="active";let s=i(o,n);s.core_media||console.trace("Missing core_media"),r.set(s.id,s)}break;case"hits.urlUpdated":for(let n of r.keys())r.set(n,i(r.get(n)));break;case"hit.update":{let{id:n,changes:o}=t.payload;Array.isArray(n)||(n=[n]);for(let s of n){let a=r.get(s);if(a){let l=i(a,o);r.set(l.id,l)}else console.trace("unknown hit")}}break;case"hit.updateRunning":{let{id:n,runningDelta:o}=t.payload,s=r.get(n);if(s){let a=s.running??0,l={running:a+o};a==0&&(l.status="running"),l.running<=0&&(l.running=0,l.status="active"),r.set(n,i(s,l))}else console.trace("unknown hit")}break;case"hit.updateOriginal":{let{id:n,changes:o}=t.payload;for(let s of r.values())(n===s.id||n===s.originalId)&&r.set(s.id,i(s,o))}break;case"hit.delete":{let n=t.payload;Array.isArray(n)||(n=[n]);for(let o of n)r.delete(o)}break;case"hit.orphanTimeout":{let n=t.payload,o=r.get(n);if(o&&o.status=="orphan"&&!isNaN(o.orphanT)&&Date.now()>o.orphanT){let s=t.payload;Array.isArray(s)||(s=[s]);for(let a of s)r.delete(a)}}break;case"blacklist-changed":break;default:return!t.type.startsWith("@@redux")&&t.type!="hit.clear-progress"&&t.type!="log.new"&&t.type!="log.clear"&&t.type!="hit.progress"&&console.trace("Unexpected action:",t.type),e}return{flat:r}}var n1,o1,a1,gt,s1,_n=N(()=>{"use strict";mr();n1=te(),o1=Lt(),a1=(da(),P(ua)),gt=($e(),P(ze)),s1=(Ir(),P(Rr));o1.listen({actionCommand:(e,t)=>a1.execute(e,t),clearHits:Yg})});var nh={};ne(nh,{checkHitBlacklisted:()=>y1,set:()=>ih});async function cu(){try{await Zg.storage.local.set({blacklist:await wn})}catch{console.error("Cannot write blacklist storage")}}function vn(e){let t=[],r=/^https?:\/\/([^\/:]+)/.exec(e);if(r)if(h1.test(r[1]))t.push(r[1]);else{let i=r[1].split(".");for(;i.length>1&&(i[0]!="co"||i.length>2);)t.push(i.join(".")),i.shift()}return t}function rh(e){let t=[];e.url&&(t=t.concat(vn(e.url))),e.audioUrl&&(t=t.concat(vn(e.audioUrl))),e.videoUrl&&(t=t.concat(vn(e.videoUrl))),e.topUrl&&(t=t.concat(vn(e.topUrl))),e.pageUrl&&(t=t.concat(vn(e.pageUrl)));let r={};return t.forEach(function(i){r[i]=1}),r}function _1(e){let t=Object.keys(e);return t.sort(function(r,i){let n=r.split(".").reverse(),o=i.split(".").reverse();for(;;){if(n.length&&!o.length)return-1;if(!n.length&&o.length)return 1;if(!n.length&&!o.length)return 0;let s=n.shift(),a=o.shift();if(s!=a)return s<a?-1:1}}),t}function b1(e){let t=rh(e);return _1(t)}function y1(e){if(!pa.unsafe_prefs.blacklistEnabled)return!1;let t=rh(e);for(let r in t)if(th[r])return!0;return!1}async function v1(e){let t=await wn;e.forEach(r=>{t[r]=!0}),await cu(),eh.dispatch("blacklist-changed")}async function w1(e){let t=await wn;e.forEach(r=>{delete t[r]}),await cu()}async function ih(e){wn=Promise.resolve(e||{}),await cu()}var pa,Zg,eh,h1,th,wn,oh=N(()=>{"use strict";pa=te(),Zg=pa.browser,eh=($e(),P(ze)),h1=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"),wn=(async()=>{try{let t=(await Zg.storage.local.get({blacklist:{}})).blacklist;return th=t,t}catch{console.error("Cannot read blacklist storage");return}})();pa.rpc.listen({domainsFromHitId:e=>{let t=eh.getHit(e);return t&&b1(t)||[]},addToBlacklist:v1,removeFromBlacklist:w1,setBlacklist:async e=>{await ih(e)},getBlacklist:async()=>{let e=await wn;return Object.keys(e).filter(t=>!!e[t])},editBlacklist:()=>{pa.ui.open("blacklist-edit",{type:"tab",url:"content/blacklist-edit.html"})}})});var mu={};ne(mu,{outputConfigForHit:()=>x1,set:()=>sh});async function sh(e){fu=Promise.resolve(e),await ah.storage.local.set({convrules:e})}async function x1(e){let t=await fu,r=(e.url||e.videoUrl||e.audioUrl)&&e.topUrl;if(!r)return null;let i=new URL(r).hostname,n=[],o=i.split(".");for(let l=0;l<o.length-1;l++)n.push(o.slice(l).join("."));let s=null;return t.every(l=>{let u=!0;return l.extension&&e.extension!==l.extension&&(u=!1),u&&l.domain&&(u=!n.every(d=>d!==l.domain)),u&&(s=l),!u})||!s.convert?null:(await A1.getOutputConfigs())[s.format]||null}var pu,ah,A1,fu,gu=N(()=>{"use strict";pu=te(),ah=pu.browser,A1=(ci(),P(di)),fu=ah.storage.local.get({convrules:[]}).then(e=>e.convrules);pu.rpc.listen({editConversionRules:()=>{pu.ui.open("convrules-edit",{type:"tab",url:"content/convrules-edit.html"})},getConversionRules:()=>fu,setConversionRules:e=>sh(e)})});var ze={};ne(ze,{closePopup:()=>mh,dispatch:()=>F1,getHit:()=>ch,getHits:()=>vu,getLogs:()=>q1,getMainData:()=>fh,getSerializedHits:()=>wu});function vu(){return De.getState().hits}function ch(e){return vu().flat.get(e)}function q1(){return De.getState().logs}async function ph(){let{flat:e}=vu(),t=(await ht.prefs).mediaweightMinSize,r=await Fr.storage.local.get({blacklist:{}}),i=Object.keys(r.blacklist).map(n=>n.split(".").reverse());return Nd(e,i,t)}async function wu(){let e=await B(Fi),t=await ph(),r=kd(t,e);for(let i of r)for(let n of i)n.actions=await lh.availableActions(n.id);return r}async function Au(){if(!await uh)return;let e=0,t=0,r=0,i=0,n=await ph();for(let d of n.values())switch(d.status){case"running":i++;break;case"active":e++,t++;break;case"inactive":t++;break;case"pinned":r++;break}let o=await ht.prefs,s=!1;(t==0||o.iconActivation=="currenttab"&&e==0)&&(s=!0),Fr.action.setIcon({path:`/content2/icons/${O1.channel}-${s?"grey":"color"}.png`});let a="",l="#000";switch(o.iconBadge){case"tasks":l="#00f",a=i||"";break;case"activetab":l="#080",a=e||"";break;case"anytab":l="#b59e32",a=t||"";break;case"pinned":l="#000",a=r||"";break;case"mixed":r>0?(l="#000",a=r):i>0?(l="#00f",a=i):e>0?(l="#080",a=e):t>0&&(l="#b59e32",a=t)}let u=De.getState().logs.filter(d=>d.type==="error");u.length>0&&(a=u.length,l="#f44"),Fr.action.setBadgeText({text:""+a}),Fr.action.setBadgeBackgroundColor({color:l})}function F1(e,t){De.dispatch({type:e,payload:t})}async function fh(){return{hits:await wu(),actions:lh.describeAll(),logs:De.getState().logs,progress:De.getState().progress}}async function mh(){return An.call("main","close")}async function L1(){let e=["blacklist","license",bu,"convrules","outputConfigs","smartname"],t=await ht.prefs;return Fr.storage.local.get(e).then(r=>{let i=Object.assign({blacklist:{},license:null,conversionRules:[],outputConfigs:{}},r,{"weh-prefs":t.getAll()}),n=JSON.stringify(i,null,4),o;if(ht.isBrowser("firefox")){let s=new Blob([n],{type:"text/json;charset=utf-8"});o=URL.createObjectURL(s)}else o="data:,"+n;Fr.downloads.download({url:o,filename:"vdh-settings.json",saveAs:!0,conflictAction:"uniquify"})})}async function U1(e){return e.convrules&&await C1.set(e.convrules),e.outputConfigs&&P1.setOutputConfigs(e.outputConfigs),e.license&&R1.setLicense(e.license),e.blacklist&&await N1.set(e.blacklist),bu in e&&await ee(Fi,e[bu]),e.smartname&&await k1.set(e.smartname),e["weh-prefs"]||{}}function j1(){Fr.runtime.reload()}var T1,E1,S1,D1,yu,O1,ht,An,_u,lh,M1,P1,R1,I1,N1,k1,C1,uh,dh,Fr,De,B1,V1,H1,hu,bu,$e=N(()=>{"use strict";Cd();Kr();({createStore:T1,combineReducers:E1,applyMiddleware:S1}=Vc()),{createLogger:D1}=Fc(),yu=Xc(),O1=ur(),ht=te(),An=Lt(),_u=(_n(),P(hn)),lh=(da(),P(ua)),M1=(Xn(),P(Wn)),P1=(ci(),P(di)),R1=(Yt(),P(Kt)),I1=void 0,N1=(oh(),P(nh)),k1=(an(),P(on)),C1=(gu(),P(mu));Ir();uh=B(Hi),dh=[];ht.prefs.then(e=>{e.backgroundReduxLogger&&dh.push(D1({collapsed:(t,r,i)=>!0}))});Fr=ht.browser,De=T1(E1({hits:_u.reducer,progress:_u.progressReducer,logs:M1.reducer}),S1(...dh));B1=yu(De.getState,"hits"),V1=yu(De.getState,"progress"),H1=yu(De.getState,"logs");hu=null;De.subscribe(B1(async()=>{if(hu||!await uh)return;let e=(await ht.prefs).hitUpdateFloodProtect;hu=setTimeout(async()=>{hu=null;let t=await wu();try{ht.openedContents().indexOf("main")>=0&&An.call("main","hits",t),I1?.updateHits(t),Au()}catch(r){console.error(r)}},e)}));De.subscribe(V1(()=>{try{An.call("main","progress",De.getState().progress)}catch{}}));De.subscribe(H1(()=>{try{An.call("main","logs",De.getState().logs)}catch{}try{Au()}catch(e){console.error(e)}}));An.listen({getHit:e=>ch(e),getMainData:fh,hitPageData:e=>{_u.updateOriginal(e.id,e.data)},closePopup:mh,closePanel:e=>ht.ui.close(e)});Au();bu="media_user_pref";ht.rpc.listen({exportSettings:L1,importSettings:U1,reloadAddon:j1})});var Rr={};ne(Rr,{current:()=>z1,gotoOrOpenTab:()=>Z1,setTransientTab:()=>gh,update:()=>bi});function z1(){return{url:xu,urls:ma}}function $1(){fa=null,Q1().then(e=>{e&&(xu=e.url,Vt.tabs.query({}).then(t=>{ma={};for(let r in t)ma[t[r].url]=1;X1.dispatch("hits.urlUpdated",{url:xu,urls:ma})}))})}function bi(){fa&&clearTimeout(fa),fa=setTimeout($1,50)}function gh(e,t){or=e,_i=t}function J1(e){or===e&&_i&&Vt.tabs.update(_i,{active:!0}),or=null,_i=null,bi()}function K1({tabId:e,_windowId:t}){e!==or&&(or=null,_i=null),bi()}function Y1(e){or==="<next-tab>"&&(or=e.id)}function Z1(e){return or=null,_i=null,G1.gotoOrOpenTab(e,gh)}var W1,Vt,X1,G1,fa,Q1,xu,ma,or,_i,Ir=N(()=>{"use strict";W1=te(),Vt=W1.browser,X1=($e(),P(ze)),G1=(he(),P(ge)),fa=null,Q1=async()=>{try{let e=await Vt.windows.getLastFocused({populate:!0});if(e.focused){let t=e.tabs.filter(r=>r.active);return t.length?t[0]:null}else return null}catch{return null}},xu="about:blank",ma={};or=null,_i=null;Vt.windows?.onFocusChanged?.addListener(bi);Vt.windows?.onRemoved?.addListener(bi);Vt.tabs.onActivated.addListener(K1);Vt.tabs.onRemoved.addListener(J1);Vt.tabs.onUpdated.addListener(bi);Vt.tabs.onCreated.addListener(Y1)});var ft={};ne(ft,{call:()=>bh,check:()=>Su,downloads:()=>cx,gotoInstall:()=>Eu,isAtLeastVersion:()=>dx,isDegradedVersion:()=>ux,isProbablyAvailable:()=>lx,listen:()=>ax,request:()=>px,requestBinary:()=>fx});function Eu(){ix(async()=>{let e=await ga.prefs,t=`https://www.downloadhelper.net/install-coapp-v2?channel=${ox}`;return e.forcedCoappVersion&&(t+="&version="+e.forcedCoappVersion),rx.gotoOrOpenTab(t)})}function bh(...e){return tt.call(...e)}function ax(...e){return tt.listen(...e)}function sx(){return new Promise((e,t)=>{let r=!1;tt.callCatchAppNotFound(i=>{yi=!1,r=!0,e({status:!1,error:i.message})},"info").then(i=>{yi=!0,Tu=i.version,_h=i.target?.node==10,e({status:!0,info:i})}).catch(i=>{yi=!1,r||e({status:!1,error:i.message})})})}function Su(){return nx(()=>sx())}function lx(){return yi}function ux(){return!!_h}function dx(e){if(Tu)return tx(Tu,e)>=0;throw new Error("Coapp no available")}function px(e,t){return new Promise((r,i)=>{let n=[];function o(s){if(n.push(s.data),!s.more)return r(n.join(""));tt.call("requestExtra",s.id).then(a=>{o(a)}).catch(i)}tt.call("request",e,t).then(s=>(yi=!0,s)).then(o).catch(i)})}function fx(e,t){return new Promise((r,i)=>{let n=0,o=[];function s(a){if(a.data&&a.data.data&&(n+=a.data.data.length,o.push(new Uint8Array(a.data.data))),!a.more){let l=new Uint8Array(n),u=0;return o.forEach(d=>{l.set(d,u),u+=d.length}),r(l)}tt.call("requestExtra",a.id).then(l=>{setTimeout(()=>{s(l)})}).catch(i)}tt.call("requestBinary",e,t).then(a=>(yi=!0,a)).then(s).catch(i)})}var ga,hh,ex,tx,tt,rx,ix,nx,ox,xn,yi,Tu,_h,cx,mt=N(()=>{"use strict";ga=te(),hh=(he(),P(ge)),ex=(sd(),P(ad)),{compareSemVer:tx}=(Fa(),P(hd)),tt=bd()("net.downloadhelper.coapp"),rx=(Ir(),P(Rr)),ix=hh.Concurrent(),nx=hh.Concurrent(),{channel:ox}=ur(),xn=null;tt.onAppNotFound.addListener(()=>{Eu()});tt.onCallCount.addListener(async(e,t)=>{let r=await ga.prefs;xn&&(clearTimeout(xn),xn=null),e===0&&t===0&&r.coappIdleExit&&(xn=setTimeout(()=>{xn=null,tt.close()},r.coappIdleExit))});cx=new ex.Downloads(tt);ga.prefs.then(e=>{e.checkCoappOnStartup&&Su()});ga.rpc.listen({coappProxy:bh,checkCoApp:Su,installCoApp:Eu})});var Du={};ne(Du,{getProxyHeaders:()=>Ix});async function xh(){let e=await _t.prefs,t={};return e.mediaExtensions.split("|").forEach(function(r){t[r]=1}),t}async function Th(){let e=(await _t.prefs).networkFilterOut;if(e)try{return new RegExp(e,"i")}catch{console.warn("networkFilterOut preference is not a valid regex");return}}function vh(){He.webRequest.onHeadersReceived.addListener(Dh,{urls:["<all_urls>"]},["responseHeaders"])}function Px(){He.webRequest.onHeadersReceived.removeListener(Dh)}function wh(){Ht={},He.webRequest.onSendHeaders.addListener(Oh,{urls:["<all_urls>"]},Ph),He.webRequest.onErrorOccurred.addListener(Mh,{urls:["<all_urls>"]})}function Rx(){He.webRequest.onSendHeaders.removeListener(Oh),He.webRequest.onErrorOccurred.removeListener(Mh),Ht=null}function Rh(e){let t=vi[e.url];if(t){clearTimeout(t.timer),delete vi[e.url];let r=e.requestHeaders.filter(i=>typeof Ah[i.name.toLowerCase()]>"u");t.handlers.forEach(i=>{i.resolve({proxy:e.proxyInfo,headers:r})})}}function Ix(e){function t(){let i=vi[e];i&&(i.handlers.forEach(n=>{n.reject(new Error("timeout monitoring proxyHeaders"))}),delete vi[e])}let r=vi[e];return r?clearTimeout(r.timer):r=vi[e]={handlers:[]},new Promise((i,n)=>{r.handlers.push({resolve:i,reject:n}),r.timer=setTimeout(t,3e4),fetch(e,{method:"HEAD",credentials:"include"})})}var _t,mx,gx,yh,hx,_x,bx,He,yx,vx,wx,Ax,xx,Tx,Ex,Sx,Dx,Ox,Ah,Eh,Sh,Dh,Mx,Ht,Oh,Mh,Ph,vi,Ou=N(()=>{"use strict";Ki();ae();fr();Ge();xt();mr();_t=te(),mx=($e(),P(ze)),gx=(_n(),P(hn)),yh=(he(),P(ge)),hx=(an(),P(on)),{MaybeCreateProbeFromNetworkRequest:_x,FireProbeForHTTPMedia:bx}=(ra(),P(lu)),He=_t.browser,yx=new RegExp("^bytes [0-9]+-[0-9]+/([0-9]+)$"),vx=new RegExp("^(audio|video)/(?:x-)?([^; ]+)"),wx=new RegExp('filename\\s*=\\s*"\\s*([^"]+?)\\s*"'),Ax=new RegExp("/([^/]+?)(?:\\.([a-z0-9]{1,5}))?(?:\\?|#|$)","i"),xx=new RegExp("\\.([a-z0-9]{1,5})(?:\\?|#|$)","i"),Tx=new RegExp("\\bsource=yt_otf\\b"),Ex=new RegExp("/ptracking\\b"),Sx=new RegExp("^https://www.gstatic.com/youtube/doodle\\b"),Dx=new RegExp("^(https?)://v[^\\/]*\\.tumblr\\.com/(tumblr_[0-9a-zA-Z_]+)\\.(?:mp4|mov)"),Ox=new RegExp("^https://soundcloud.com/"),Ah={host:!0,range:!0,"content-length":!0};Eh=xh();_t.prefs.then(e=>e.on("mediaExtensions",()=>{Eh=xh()}));Sh=Th();_t.prefs.then(e=>e.on("networkFilterOut",()=>{Sh=Th()}));Dh=async e=>{let t=await _t.prefs,r;if(Ht&&(r=Ht[e.requestId],r&&delete Ht[e.requestId]),e.tabId<0&&!e.initiator?.startsWith("http"))return;let i=await Sh;if(Tx.test(e.url)||Ex.test(e.url)||Sx.test(e.url)||i&&i.test(e.url))return;function n(f,A){if(!A&&(!a&&isNaN(l)&&!u||!a&&!u&&(isNaN(l)||t.mediaweightThreshold===0||l<t.mediaweightThreshold)||a&&a[2].toLowerCase()=="ms-asf"))return;let h={id:"network-probe:"+yh.hashHex(e.url),status:"active",url:e.url,tabId:e.tabId,frameId:e.frameId,fromCache:!0,referrer:m};!isNaN(l)&&!f&&(h.length=l),e.proxyInfo&&e.proxyInfo.type.substr(0,4)=="http"&&(h.proxy=e.proxyInfo);let p=o["content-disposition"];if(p){let _=wx.exec(p);_&&_[1]&&(h.headerFilename=_[1])}let g=Ax.exec(e.url);g&&(h.urlFilename=g[1]),h.title=h.headerFilename||h.urlFilename||_t._("media");let x=Dx.exec(e.url);x&&(h.thumbnailUrl=x[1]+"://media.tumblr.com/"+x[2]+"_frame1.jpg"),u?(h.type="video",h.extension=u[1]):a?(h.type=a[1],h.extension=a[2]):h.extension=d,h.headers=r&&r.filter(_=>typeof Ah[_.name.toLowerCase()]>"u")||[];async function w(_){for(;_.status!="complete";)await new Promise(S=>setTimeout(S,500)),_=await He.tabs.get(_.id);if(_){h.tabId=_.id,h.topUrl=_.url,h.isPrivate=_.incognito,h.pageTitle=_.title,h.title=h.headerFilename||_.title||h.urlFilename||_t._("media");let S=await hx.getSpecs(_.url);S&&(S.headerFilename=h.headerFilename,S.urlFilename=h.urlFilename);{let M;try{M=await He.scripting.executeScript({target:{tabId:_.id},func:()=>{let I=[{sel:"meta[property='og:image:secure_url']",attr:"content"},{sel:"meta[property='og:image']",attr:"content"},{sel:"link[as='image']",attr:"href"},{sel:"link[rel='thumbnail']",attr:"href"},{sel:"link[rel='image_src']",attr:"href"},{sel:"meta[property='twitter:image']",attr:"content"},{sel:"video",attr:"poster"},{sel:"#vp-preview",attr:"data-thumb"}];for(let U of I){let W=document.querySelectorAll(U.sel);for(let q of W){let $=q.getAttribute(U.attr);if(typeof $=="string")try{return new URL($,window.location.href).href}catch{}}}return null}})}catch{}typeof M?.[0]?.result=="string"?h.thumbnailUrl2=M[0].result:h.thumbnailUrl2="/content/images/no-thumbnail.png"}if(f){h.originalId=h.id;for(let M of f)await M.onHitDataAvailable(h)}h.frameId>=0&&yh.executeScriptWithGlobal({tabId:_.id},{_$vdhHitId:h.id,_$vdhSmartNameSpecs:S},"/injected/pagedata.js").catch(M=>{He.webNavigation.getFrame({tabId:_.id,frameId:h.frameId}).then(I=>{I&&(console.warn("pagedata execution error",M.message),gx.updateOriginal(h.id,{title:_.title||h.title,pageUrl:I.url,topUrl:_.url}))})})}if(!f){h.group=h.id;let S=D,M={builder:"HTTPMedia",protocol:"non-adaptative",duration:"unknown",content_length:D};h.length&&(M.content_length=C(h.length));{if(s){let U=si(s);if(U.isErr())console.warn("Couldn't not parse mimetype",s);else{let{container:W,av_codecs:q}=U.unwrap();S=C({...M,container:ce(W),av:Qe(q,$=>({codec:Xe($),bitrate:D}),$=>({codec:We($),fps:D,dimensions:D,quality:D,bitrate:D}))})}}let I=u?.[1]||h.headerFilename?.split(".").pop()||"";S.isNone()&&(S=Ni(I).map(([U,W])=>{let q=W=="audio_only";return{...M,container:U,av:{audio:q?Sd():!1,video:q?!1:Wt()}}}))}S.isSome()&&(h.core_media=S.unwrap(),bx(h),mx.dispatch("hit.new",h))}}e.tabId>0?He.tabs.get(e.tabId).then(w):f&&e.initiator?.startsWith("http")&&He.tabs.query({url:e.initiator+"/*"}).then(async _=>{for(let S of _)await w(S)})}let o={};(e.responseHeaders||[]).forEach(f=>{o[f.name.toLowerCase()]=f.value});let s=o["content-type"],a=vx.exec(s),l=parseInt(o["content-length"]);if(isNaN(l)){let f=o["content-range"];if(f){let A=yx.exec(f);A&&(l=parseInt(A[1]))}}let u=xx.exec(e.url),d=null,c=await Eh;if(u){if(d=u[1].toLowerCase(),d=="m4s"&&t.dashHideM4s||d=="ts"&&t.mpegtsHideTs)return;!Ni(u[1]).isSome()&&!c[u[1]]&&(u=null)}let m=e.originUrl||e.documentUrl||void 0;if(Ox.test(m)&&l<1e6&&s=="audio/mpeg")return;let T=_x(e,s,r);if(T.length>0)try{n(T,!0,m,r,e)}catch(f){console.error("Uncaught PostHook error:",f)}else{let f=a&&(a[1]=="audio"||a[1]=="video"),A=!isNaN(l)&&t.mediaweightThreshold>0&&l>=t.mediaweightThreshold;n(null,f||A)}},Mx=["main_frame","sub_frame","xmlhttprequest","object","media"];_t.browserType=="firefox"&&Mx.push("object_subrequest");_t.prefs.then(e=>{e.networkProbe&&vh(),e.monitorNetworkRequests&&wh(),e.on("networkProbe",(t,r)=>{r?vh():Px()}),e.on("monitorNetworkRequests",(t,r)=>{r?wh():Rx()})});Ht=null,Oh=e=>{Rh(e),Ht&&(Ht[e.requestId]=e.requestHeaders)},Mh=e=>{Rh(e),Ht&&delete Ht[e.requestId]},Ph=["requestHeaders"];He.runtime.getManifest().manifest_version>=3&&Ph.push("extraHeaders");vi={}});var Ch={};ne(Ch,{analyzePage:()=>Pu});async function Mu(){let e=await Tn.tabs.query({active:!0,currentWindow:!0});if(e.length===0)throw new Error("Can't find current tab");return{tabId:e[0].id}}async function Pu(e){let t;e?t={tabId:e}:t=await Mu(),await Tn.scripting.insertCSS({target:t,css:kx});let r=await ar.prefs;await kh.executeScriptWithGlobal(t,{_$vdhParams:{extensions:r.medialinkExtensions,maxHits:r.medialinkMaxHits,minFilesPerGroup:r.medialinkMinFilesPerGroup,minImgSize:r.medialinkMinImgSize,scanImages:r.medialinkScanImages,scanLinks:r.medialinkScanLinks}},"/injected/gallery.js")}function Nh(e){return".vdh-mask."+e+" { display: block; }"}var ar,Tn,Ih,kh,Nx,kx,qh=N(()=>{"use strict";ar=te(),Tn=ar.browser,Ih=($e(),P(ze)),kh=(he(),P(ge)),Nx=(Ou(),P(Du)),kx=".vdh-mask { position: absolute; display: none; background-color: rgba(255,0,0,0.5); z-index: 2147483647; }";ar.rpc.listen({analyzePage:()=>{Pu()},galleryGroups:e=>{Object.keys(e.groups).forEach(t=>{let r=e.groups[t],i="??",n="??";try{n=new URL(r.baseUrl).hostname}catch(a){console.warn("Uncaught URL error",a)}switch(r.type){case"image":i=ar._("gallery_from_domain",n);break;case"link":i=ar._("gallery_links_from_domain",n);break}let o;if(r.extensions){let a=Object.keys(r.extensions);a.sort((u,d)=>r.extensions[u]-r.extensions[d]);let l=[];a.forEach(u=>{let d=ar._("number_type",[""+r.extensions[u],u.toUpperCase()]);l.push(d)}),o=ar._("gallery_files_types",l.length>0&&l.join(", ")||""+r.urls.length)}let s="gallery:"+kh.hashHex(e.pageUrl)+":"+t;Ih.dispatch("hit.new",Object.assign({},r,{gallery_urls:r.urls,id:s,topUrl:e.pageUrl,title:i,description:o,mouseTrack:!0})),Nx.getProxyHeaders(e.pageUrl).then(a=>{Ih.dispatch("hit.update",{id:s,changes:a})})})},galleryHighlight:async e=>{Tn.scripting.insertCSS({target:await Mu(),css:Nh(e)})},galleryUnhighlight:async e=>{Tn.scripting.removeCSS({target:await Mu(),css:Nh(e)})}});Tn.tabs.onUpdated.addListener(async(e,t,r)=>{let i=await ar.prefs;t.status==="complete"&&i.medialinkAutoDetect&&Pu(e)})});Nu();var Cx=(async()=>{let e=te(),t=e.browser,r=ur();r.prod||console.info("=========== VDH started",new Date().toLocaleTimeString(),"=========="),Zu(),mt(),Yt(),Ou(),qh(),gu();let i=(Ir(),P(Rr));return e.rpc.listen({openSettings:()=>{e.ui.open("settings",{type:"tab",url:"content/settings.html"}),e.ui.close("main")},openTranslation:()=>{e.ui.open("translation",{type:"tab",url:"content/translation.html"}),e.ui.close("main")},openSites:()=>i.gotoOrOpenTab("https://www.downloadhelper.net/sites"),openForum:()=>i.gotoOrOpenTab("https://github.com/aclap-dev/video-downloadhelper/discussions"),openHomepage:()=>i.gotoOrOpenTab("https://www.downloadhelper.net/"),openTranslationForum:()=>i.gotoOrOpenTab("https://github.com/aclap-dev/video-downloadhelper/discussions/categories/language-translation"),openWeh:()=>i.gotoOrOpenTab("https://github.com/mi-g/weh"),openAbout:()=>{e.ui.open("about",{type:"panel",url:"content/about.html"}),e.ui.close("main")},openCoapp:()=>{e.ui.open("coappShell",{type:"tab",url:"content/coapp-shell.html"}),e.ui.close("main")},goto:n=>i.gotoOrOpenTab(n),getBuild:()=>r,updateLastFocusedWindowHeight:(n,o)=>{t.windows.getLastFocused().then(s=>{if(s){n=Math.floor(n),o=Math.floor(o);let a=Math.floor(s.height)-o;t.windows.update(s.id,{height:n+a})}})},editMediaUserPrefs:()=>{e.ui.open("media-user-prefs-edit",{type:"tab",url:"content/media-user-prefs-edit.html"})}}),n=>{let o=t.runtime.getManifest(),s=r.channel,a=r.buildOptions.target,l=o.version.split(".").slice(0,2).join("."),u=n.reason=="install",d=n.reason=="update";if(!u&&!d)return;function c(){if(u)i.gotoOrOpenTab(`https://www.downloadhelper.net/welcome/${a}/${s}/`);else{let m=n.previousVersion.split(".").slice(0,2).join(".");l!=m&&i.gotoOrOpenTab(`https://www.downloadhelper.net/changelog/${a}/${s}/`)}}a=="mozilla"?t.storage.local.get("privacy_accept").then(m=>{m.privacy_accept!=!0?t.tabs.create({url:"/content2/privacy.html"}):c()}):c()}})();(chrome||browser).runtime.onInstalled.addListener(async t=>{(await Cx)(t)});})();
/*! Bundled license information:

semver-parser/index.js:
  (*!
   * SemVer Parser
   *
   * @license MIT
   * @copyright asamuzaK (Kazz)
   * @see {@link https://github.com/asamuzaK/semverParser/blob/master/LICENSE}
   * @see {@link https://semver.org/ Semantic Versioning 2.0.0}
   *)

m3u8-parser/dist/m3u8-parser.es.js:
  (*! @name m3u8-parser @version 7.1.0 @license Apache-2.0 *)
*/
