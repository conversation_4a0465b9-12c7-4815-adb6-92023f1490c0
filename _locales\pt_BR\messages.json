{"Bytes": {"message": "$1 Bytes"}, "GB": {"message": "$1 GB"}, "KB": {"message": "$1 KB"}, "MB": {"message": "$1 MB"}, "__MSG_appDesc_": {"message": "Video DownloadHelper"}, "about": {"message": "Sobre"}, "about_alpha_extra7_fx": {"message": "Devido às mudanças técnicas internas no Firefox, o complemento teve que ser totalmente reescrito. Aguarde algumas semanas para recuperar todos os recursos das versões anteriores."}, "about_alpha_intro": {"message": "Esta é uma versão alfa."}, "about_beta_intro": {"message": "Esta é uma versão beta."}, "about_chrome_licenses": {"message": "Acerca das licenças Chrome"}, "about_qr": {"message": "Arquivo gerado"}, "about_vdh": {"message": "Sobre o Video DownloadHelper"}, "action_abort_description": {"message": "Anula a ação em andamento"}, "action_abort_title": {"message": "<PERSON><PERSON>"}, "action_as_default": {"message": "Usar esta ação como padrão"}, "action_avplay_description": {"message": "Reproduz o vídeo com o visualizador nativo do conversor"}, "action_avplay_title": {"message": "Reproduzir"}, "action_blacklist_description": {"message": "Vídeos originados ou hospedados no(s) domínio(s) selecionado(s) serão ignorados"}, "action_blacklist_title": {"message": "Adicionar à lista negra"}, "action_bulkdownload_description": {"message": "Baixar vídeos selecionados"}, "action_bulkdownload_title": {"message": "Baixar vídeos em massa"}, "action_bulkdownloadconvert_description": {"message": "Baixar e converter vídeos selecionados"}, "action_bulkdownloadconvert_title": {"message": "Baixar e converter vídeos em massa"}, "action_copyurl_description": {"message": "Copia o URL da mídia para a área de transferência"}, "action_copyurl_title": {"message": "Copiar URL"}, "action_deletehit_description": {"message": "Exclui o item da lista atual"}, "action_deletehit_title": {"message": "Excluir"}, "action_details_description": {"message": "Exibe detalhes sobre o item"}, "action_details_title": {"message": "<PERSON><PERSON><PERSON>"}, "action_download_description": {"message": "Baixa o arquivo para o seu disco rígido"}, "action_download_title": {"message": "Baixar"}, "action_downloadaudio_description": {"message": "Baixar somente o áudio"}, "action_downloadaudio_title": {"message": "Download somente de áudio"}, "action_downloadconvert_description": {"message": "Baixa a mídia e a converte para outro formato"}, "action_downloadconvert_title": {"message": "Baixar & Converter"}, "action_openlocalcontainer_description": {"message": "Abrir o diretório/ficheiro de arquivos locais"}, "action_openlocalcontainer_title": {"message": "Abrir o diretório/ficheiro"}, "action_openlocalfile_description": {"message": "Abre arquivo de mídia local"}, "action_openlocalfile_title": {"message": "<PERSON><PERSON><PERSON>"}, "action_pin_description": {"message": "Torne o item sempre visível"}, "action_pin_title": {"message": "Fixar"}, "action_quickdownload_description": {"message": "Baixa sem perguntar o destino"}, "action_quickdownload_title": {"message": "Download rápido"}, "action_quickdownloadaudio_description": {"message": "<PERSON><PERSON><PERSON>, sem solicitar o destino"}, "action_quickdownloadaudio_title": {"message": "Download rápido somente de áudio"}, "action_quicksidedownload_description": {"message": "Download secudario sem solicitar o destino"}, "action_quicksidedownload_title": {"message": "Download secudario rápido"}, "action_sidedownload_description": {"message": "Downloader experimental"}, "action_sidedownload_title": {"message": "Download secudario"}, "action_sidedownloadconvert_description": {"message": "Download secudario de mídia e conversão para outro formato"}, "action_sidedownloadconvert_title": {"message": "Download secudario e conversão"}, "action_stop_description": {"message": "<PERSON><PERSON> de capturar"}, "action_stop_title": {"message": "<PERSON><PERSON>"}, "adaptative": {"message": "$1 adaptável"}, "add_to_blacklist": {"message": "Adicionar à lista negra"}, "add_to_blacklist_help": {"message": "Vídeos originados ou hospedados no(s) domínio(s) selecionado(s) serão ignorados"}, "advanced": {"message": "Avançado"}, "aggregating": {"message": "Agregando..."}, "analyze_page": {"message": "<PERSON><PERSON><PERSON>"}, "appDesc": {"message": "Baixar vídeos da <PERSON>"}, "appName": {"message": "Video DownloadHelper"}, "appearance": {"message": "Aparência"}, "audio_only": {"message": "<PERSON><PERSON>"}, "behavior": {"message": "Comportamento"}, "blacklist": {"message": "Lista negra"}, "blacklist_add_domain": {"message": "Adicione um domínio à lista negra"}, "blacklist_add_placeholder": {"message": "Domínio a ser colocado na lista negra"}, "blacklist_edit_descr": {"message": "A lista negra autoriza a ignorar a midia proveniente de alguns domínios"}, "blacklist_empty": {"message": "Lista negra sem domínio"}, "browser_info": {"message": "Navegador $1 $2 $3"}, "browser_locale": {"message": "Localização do Navegador: $1"}, "build_options": {"message": "Opções de Construção: $1"}, "built_on": {"message": "Construído em $1"}, "bulk_in_progress": {"message": "Operações em massa do Video DownloadHelper em progresso. Não feche esta aba, isso ocorrerá automaticamente"}, "bulk_n_videos": {"message": "$1 vídeos"}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "change": {"message": "<PERSON><PERSON>"}, "chrome_basic_mode": {"message": "Chrome basic (upgrade recomendado)"}, "chrome_inapp_descr_premium_lifetime": {"message": "Status Premium sem limite de tempo"}, "chrome_inapp_descr_premium_monthly": {"message": "Status Premium para uma inscrição mensal"}, "chrome_inapp_descr_premium_yearly": {"message": "Status Premium para uma inscrição anual"}, "chrome_inapp_no_subs": {"message": "Observação: após a suspensão de uso dos serviços de pagamento do Chrome pelo Google, as assinaturas não estão mais disponíveis "}, "chrome_inapp_not_avail": {"message": "Não disponível"}, "chrome_inapp_premium_lifetime": {"message": "Premium ilimitado"}, "chrome_inapp_premium_monthly": {"message": "Inscrição Premium mensal"}, "chrome_inapp_premium_yearly": {"message": "Inscrição Premium anual"}, "chrome_install_firefox": {"message": "Instalar Firefox"}, "chrome_install_fx_vdh": {"message": "Video DownloadHelper para o Firefox"}, "chrome_license_webstore_accepted": {"message": "Licença Chrome Webstore aceite"}, "chrome_licensing": {"message": "Licensiamento do Chrome"}, "chrome_noyt_text": {"message": "Infelizmente, a Loja Web do Chrome não permite extensões para baixar vídeos do YouTube, então nós tivemos que remover esta funcionalidade"}, "chrome_noyt_text2": {"message": "Você pode usar o Video DownloadHelper para baixar vídeos do Youtube na versão para Firefox"}, "chrome_noyt_text3": {"message": "Infelizmente, a loja do navegador Chrome não permite realizar transferências de vídeos, sendo assim não podemos incluir esta função na versão destinada ao Chrome."}, "chrome_premium_audio": {"message": "Geração de apenas áudio está disponível somente no modo Premium"}, "chrome_premium_check_error": {"message": "Status Premium de checagem de erro"}, "chrome_premium_hls": {"message": "Sem um status Premium, um download HLS somente pode ser realizado $1 após o download anterior."}, "chrome_premium_mode": {"message": "Chrome Premium"}, "chrome_premium_need_sign": {"message": "Você precisa logar no Chrome para obter os benefícios Premium"}, "chrome_premium_not_signed": {"message": "Não está logado no Chrome"}, "chrome_premium_recheck": {"message": "Verificar novamente o status Premium"}, "chrome_premium_required": {"message": "É necessário status Premium"}, "chrome_premium_source": {"message": "Você é um usuário premium por $1"}, "chrome_product_intro": {"message": "Você pode fazer o upgrade para Premium usando qualquer das opções abaixo:"}, "chrome_req_review": {"message": "Como alternativa, você se importaria em escrever uma boa avaliação na Chrome WebStore?"}, "chrome_signing_in": {"message": "Logar no Chrome"}, "chrome_verif_premium": {"message": "Verificando o status premium..."}, "chrome_verif_premium_error": {"message": "API de pagamentos in-app não está disponível"}, "chrome_warning_yt": {"message": "Aviso em extensões do Chrome e Youtube"}, "clear": {"message": "Limpar"}, "clear_hits": {"message": "<PERSON><PERSON> itens"}, "clear_logs": {"message": "Limpar relatórios"}, "coapp": {"message": "CoApp"}, "coapp_error": {"message": "Checando o Aplicativo complementar retornado:"}, "coapp_found": {"message": "Auxiliar do Aplicativo encontrado"}, "coapp_help": {"message": "Clique aqui para solucionar seu problema."}, "coapp_install": {"message": "Instalar Aplicativo auxiliar"}, "coapp_installed": {"message": "Aplicativo auxiliar instalado"}, "coapp_latest_version": {"message": "Última versão disponível é $1"}, "coapp_not_installed": {"message": "Aplicativo auxiliar não instalado"}, "coapp_outdated": {"message": "Versão desatualizada do Aplicativo auxiliar - favor atualizar"}, "coapp_outofdate": {"message": "Necessária versão mais recente do Aplicativo auxiliar"}, "coapp_outofdate_text": {"message": "Você está executando o Aplicativo auxiliar na versão $1 mas, é necessária a versão $2"}, "coapp_path": {"message": "Aplicativo auxiliar biná<PERSON>"}, "coapp_recheck": {"message": "Verifique novamente"}, "coapp_required": {"message": "Aplicativo auxiliar necessário"}, "coapp_required_text": {"message": "Essa operação requer um Aplicativo extra para ser completada"}, "coapp_shell": {"message": "CoApp Shell"}, "coapp_unchecked": {"message": "Verificando Aplicativo auxiliar"}, "coapp_update": {"message": "Atualize o Aplicativo auxiliar"}, "collecting": {"message": "Coletando..."}, "confirmation_required": {"message": "Confirmação necessária"}, "congratulations": {"message": "Parabéns !"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "convconf_2passes": {"message": "2 passes"}, "convconf_ac": {"message": "Canais de áudio"}, "convconf_acnone": {"message": "<PERSON><PERSON><PERSON>"}, "convconf_acodec": {"message": "Codec de áudio"}, "convconf_aspect": {"message": "Taxa de proporção"}, "convconf_audiobitrate": {"message": "Taxa de bits de áudio"}, "convconf_audiofreq": {"message": "Frequência de áudio"}, "convconf_audioonly": {"message": "<PERSON><PERSON>"}, "convconf_bitrate": {"message": "Taxa de bits"}, "convconf_container": {"message": "Formato"}, "convconf_duplicate": {"message": "Duplicar"}, "convconf_ext": {"message": "Extensão do arquivo de saída"}, "convconf_extra": {"message": "Parâmetros extras"}, "convconf_level": {"message": "Nível"}, "convconf_mono": {"message": "Mono"}, "convconf_new": {"message": "Novo"}, "convconf_preset": {"message": "Predefinição"}, "convconf_profilev": {"message": "Perfil de vídeo"}, "convconf_rate": {"message": "Taxa de quadros"}, "convconf_readonly": {"message": "Esta configuração padrão é somente leitura. Duplique-a para fazer modificações."}, "convconf_remove": {"message": "Remover"}, "convconf_reset": {"message": "Redefinir tudo"}, "convconf_reset_confirm": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON> as suas configurações personalizadas"}, "convconf_save": {"message": "<PERSON><PERSON>"}, "convconf_size": {"message": "Tamanho do quadro"}, "convconf_stereo": {"message": "Estéreo"}, "convconf_target": {"message": "Alvo"}, "convconf_tune": {"message": "<PERSON><PERSON><PERSON>"}, "convconf_vcodec": {"message": "Codec de vídeo"}, "convconf_videobitrate": {"message": "Taxa de bits de vídeo"}, "conversion_create_rule": {"message": "<PERSON><PERSON><PERSON> regra"}, "conversion_outputs": {"message": "Saídas de conversão"}, "conversion_rules": {"message": "Regras de conversão"}, "conversion_update_rule": {"message": "At<PERSON><PERSON><PERSON> regra"}, "convert": {"message": "Converter"}, "convert_local_files": {"message": "Converter arquivos locais"}, "converter_needed_aggregate": {"message": "Esta operação necessita ter um conversor instalado em seu sistema para agregar os fluxos de áudio e vídeo."}, "converter_needed_aggregate_why": {"message": "Por que eu preciso de um conversor?"}, "converter_needs_reg": {"message": "Requer registro"}, "converter_queued": {"message": "Fila do conversor..."}, "converter_reg_audio": {"message": "<PERSON>ocê solicitou, deliberadamente ou através de uma regra de conversão automática, a criação de um arquivo somente áudio. Isto requer um conversor registrado."}, "converting": {"message": "Convertendo..."}, "convrule_convert": {"message": "Converter"}, "convrule_domain": {"message": "<PERSON><PERSON><PERSON>"}, "convrule_extension": {"message": "Extensão"}, "convrule_format": {"message": "Formatar $1"}, "convrule_from_domain": {"message": "Do domínio $1"}, "convrule_no_convert": {"message": "Não converter"}, "convrule_output_format": {"message": "Formato de saída"}, "convrule_refresh_formats": {"message": "Atualizar formatos de saída"}, "convrule_with_ext": {"message": "Com a extensão  '$1'"}, "convrules_add_rule": {"message": "Criar uma nova regra de conversão"}, "convrules_edit_descr": {"message": "Regras de conversão permitem realizar uma conversão de mídia automaticamente, imediatamente após o download"}, "convrules_empty": {"message": "Sem regras de conversão"}, "copy_of": {"message": "Cópia de $1"}, "copy_settings_info_to_clipboard": {"message": "Copiar informações para a área de transferência"}, "copy_settings_info_to_clipboard_success": {"message": "Informações copiadas para a área de transferência."}, "corrupted_media_file": {"message": "Não foi possível ace<PERSON>r as informações da mídia '$1' do arquivo '$2'. O arquivo deve estar corrompido."}, "create": {"message": "<PERSON><PERSON><PERSON>"}, "custom_output": {"message": "Formato de saída personalizado"}, "dash_streaming": {"message": "Transmissão DASH"}, "default": {"message": "Padrão"}, "details_parenthesis": {"message": "(<PERSON><PERSON><PERSON>)"}, "dev_build": {"message": "Construção de Desenvolvimento"}, "dialog_audio_impossible": {"message": "Esse tipo de mídia não é compatível com o download somente de áudio"}, "dialog_audio_impossible_title": {"message": "Não é possível fazer o download do áudio"}, "directory_not_exist": {"message": "Pasta não existente"}, "directory_not_exist_body": {"message": "Pasta '$1' não existe, você deseja criá-la?"}, "dlconv_download_and_convert": {"message": "Baixar & Converter"}, "dlconv_output_details": {"message": "Configu<PERSON> <PERSON><PERSON><PERSON> de <PERSON>"}, "donate": {"message": "<PERSON><PERSON>"}, "donate_vdh": {"message": "Ajude o Video DownloadHelper"}, "download_error": {"message": "Erro no download"}, "download_method": {"message": "Método de download"}, "download_method_not_again": {"message": "Use esse método por padrão na próxima vez"}, "download_modes1": {"message": "A transferência atual pode ser realizado pelo próprio navegador ou pelo Aplicativo da companhia"}, "download_modes2": {"message": "Por razões técnicas, baixar pelos serviços do navegador podem fazer a transferência ser rejeitada pelo servidor do vídeo e não é possível definir uma alternativa padrão para o diretório de transferência"}, "download_with_browser": {"message": "Use o Navegador"}, "download_with_coapp": {"message": "Usar Aplicativo da companhia"}, "downloading": {"message": "Baixando..."}, "edge_req_review": {"message": "Alternativamente, você se importaria em escrever uma boa análise na loja de extensões para o Microsoft Edge?"}, "error": {"message": "Erro "}, "error_not_directory": {"message": "'$1' existe, mas não é uma pasta"}, "errors": {"message": "<PERSON><PERSON><PERSON>"}, "exit_natmsgsh": {"message": "Sair do 'CoApp'"}, "explain_qr1": {"message": "Você notará que o vídeo resultante contém uma marca d'água no canto."}, "explain_qr2": {"message": "Is<PERSON> ocorre porque o recurso de conversão não foi registrado."}, "export": {"message": "Exportar"}, "failed_aggregating": {"message": "Falha ao agregar \"$1\""}, "failed_converting": {"message": "Falha ao converter \"$1\""}, "failed_getting_info": {"message": "Falha ao obter informações de \"$1\""}, "failed_opening_directory": {"message": "<PERSON>alha em abrir a pasta"}, "failed_playing_file": {"message": "Falha em rodar o arquivo"}, "file_dialog_date": {"message": "Data"}, "file_dialog_name": {"message": "Nome"}, "file_dialog_size": {"message": "<PERSON><PERSON><PERSON>"}, "file_generated": {"message": "O arquivo \"$1\" foi gerado."}, "file_ready": {"message": "\"$1\" já está pronto"}, "finalizing": {"message": "Finalizando..."}, "from_domain": {"message": "A partir de $1"}, "gallery": {"message": "Galeria"}, "gallery_files_types": {"message": "arquivos de $1"}, "gallery_from_domain": {"message": "Galeria de $1"}, "gallery_links_from_domain": {"message": "Links de $1"}, "general": {"message": "G<PERSON>"}, "get_conversion_license": {"message": "Adquirir uma licença de conversão"}, "help_translating": {"message": "Ajude-nos traduzindo"}, "hit_details": {"message": "Detalhes do item"}, "hit_go_to_tab": {"message": "Vá para a aba"}, "hls_streaming": {"message": "Transmissão HLS"}, "homepage": {"message": "Homepage"}, "import": {"message": "Importe"}, "import_invalid_format": {"message": "Formato Inválido"}, "in_current_tab": {"message": "Na aba atual"}, "in_other_tab": {"message": "Em outras abas"}, "lic_mismatch1": {"message": "A licença é para  $1  mas a versão de construção do navegador não foi especificada"}, "lic_mismatch2": {"message": "A licença é para  $1  mas a construção da extensão é para $2"}, "lic_not_needed_linux": {"message": "Nossa contribuição para o Linux: Não requisitamos licença"}, "lic_status_accepted": {"message": "Licença verificada"}, "lic_status_blocked": {"message": "Licença bloqueada"}, "lic_status_error": {"message": "Erro de licença"}, "lic_status_locked": {"message": "Licença expirada (revalidando)"}, "lic_status_mismatch": {"message": "Licença/navegador não conferem"}, "lic_status_nocoapp": {"message": "A Licença não pôde ser verificada"}, "lic_status_unneeded": {"message": "Licença não necessária"}, "lic_status_unset": {"message": "Licença não selecionada"}, "lic_status_unverified": {"message": "Licença não verificada"}, "lic_status_verifying": {"message": "Verificando licença..."}, "license": {"message": "Licença"}, "license_key": {"message": "Chave de licença"}, "licensing": {"message": "Licenciando"}, "live_stream": {"message": "transmissão ao vivo"}, "logs": {"message": "Relatórios"}, "media": {"message": "Mí<PERSON>"}, "merge_error": {"message": "Erro de incorporação"}, "merge_local_files": {"message": "Incorporar arquivos de áudio e vídeo locais"}, "more": {"message": "Mais..."}, "mup_best_video_quality": {"message": "Qualidade de vídeo preferencial"}, "mup_ignore_low_quality": {"message": "Ignorar vídeos de baixa qualidade"}, "mup_ignore_low_quality_help": {"message": "Algumas páginas incluem mídias de \"baixa qualidade\" que são usadas apenas como \"efeitos\", como um arquivo WAV para um som de clique ou um vídeo curto para uma pequena animação de página."}, "mup_ignored_containers": {"message": "Não mostre mídia com contêineres"}, "mup_ignored_video_codecs": {"message": "Não mostrar mídia com codecs"}, "mup_lowest_video_quality": {"message": "Ignorar os vídeos estritamente abaixo"}, "mup_max_variants": {"message": "Contagem de variantes"}, "mup_max_variants_help": {"message": "Cada vídeo vem em diferentes formatos. Mostramos a você a melhor versão primeiro e também alguns formatos alternativos (variantes)."}, "mup_page_title": {"message": "Preferências de mídia"}, "mup_prefer_60fps": {"message": "Prefira 60FPS ou superior"}, "mup_prefered_container": {"message": "Formato de contêiner preferido"}, "mup_prefered_video_codecs": {"message": "Codecs de vídeo preferidos"}, "mup_reset": {"message": "reiniciar"}, "mup_saved": {"message": "salvo!"}, "network_error_no_response": {"message": "Erro de network - Sem resposta"}, "network_error_status": {"message": "Erro de network - status $1"}, "new_sub_directory": {"message": "Crie sub-pasta"}, "next": {"message": "Próximo"}, "no": {"message": "Não"}, "no_audio_in_file": {"message": "Nenhuma faixa de áudio no arquivo $1"}, "no_coapp_license_unverified": {"message": "A licença não pôde ser verificada devido ao app não ter sido instalado"}, "no_license_registered": {"message": "Nenhuma licença foi registrada"}, "no_media_current_tab": {"message": "Nenhuma mídia para processar na aba atual"}, "no_media_to_process": {"message": "Nenhuma mídia para processar"}, "no_media_to_process_descr": {"message": "Clique em reproduzir no vídeo para ajudar a detectar arquivos..."}, "no_such_hit": {"message": "Ítem inexistente"}, "no_validate_without_coapp": {"message": "O Aplicativo da companhia precisa estar instalado para a validação da licença"}, "no_video_in_file": {"message": "Nenhuma faixa de vídeo no arquivo $1"}, "not_again_3months": {"message": "Não me incomode com isso novamente por 3 meses"}, "not_see_again": {"message": "Não ver essa mensagem novamente"}, "number_type": {"message": "$1 $2"}, "ok": {"message": "OK"}, "orphan": {"message": "<PERSON><PERSON><PERSON>"}, "output_configuration": {"message": "Configuração de saída"}, "overwrite_file": {"message": "Sobrescrever o arquivo '$1'"}, "per_month": {"message": "/mês"}, "per_year": {"message": "/ano"}, "pinned": {"message": "Fixado"}, "platform": {"message": "Plataforma"}, "platform_info": {"message": "Plataforma $1 $2"}, "powered_by_weh": {"message": "Provido por <PERSON>h"}, "preferences": {"message": "Preferências"}, "prod_build": {"message": "Construção de produção"}, "quality_medium": {"message": "Média"}, "quality_small": {"message": "Baixa"}, "queued": {"message": "Na fila..."}, "recheck_license": {"message": "Re-checar licença"}, "register_converter": {"message": "Registrar o conversor"}, "register_existing_license": {"message": "Registre uma licença existente"}, "registered_email": {"message": "E-mail"}, "registered_key": {"message": "Chave"}, "reload_addon": {"message": "Recarregar extensão"}, "reload_addon_confirm": {"message": "Você tem certeza de recarregar a extensão?"}, "req_donate": {"message": "Você consideraria apoiar o desenvolvimento e doar uma pequena quantia ?"}, "req_locale": {"message": "Ou então ajudar traduzindo o add-on para '$1' ? (ainda faltam '$2' strings para traduzir)"}, "req_review": {"message": "Alternativamente, você se importaria de escrever um comentário legal no site de complementos da Mozilla ?"}, "req_review_link": {"message": "Escreva um comentário sobre o Video DownloadHelper"}, "reset_settings": {"message": "Resetar configurações"}, "running": {"message": "Executando"}, "save": {"message": "<PERSON><PERSON>"}, "save_as": {"message": "<PERSON>var como..."}, "save_file_as": {"message": "Salvar arquivo como..."}, "select_audio_file_to_merge": {"message": "Selecione o arquivo contendo a faixa de áudio"}, "select_files_to_convert": {"message": "Converter arquivos locais"}, "select_output_config": {"message": "Selecione a configuração de saída"}, "select_output_directory": {"message": "Pasta de saída"}, "select_video_file_to_merge": {"message": "Selecione o arquivo contendo a faixa de vídeo"}, "selected_media": {"message": "Seleção em massa"}, "settings": {"message": "Configurações"}, "smartname_add_domain": {"message": "Adicionar uma regra de nomeação inteligente"}, "smartname_create_rule": {"message": "<PERSON><PERSON><PERSON> regra"}, "smartname_define": {"message": "Definir regra de nomeação inteligente"}, "smartname_edit_descr": {"message": "Uma regra de nomeação inteligente permite a customização do nome dos vídeos baseado nos nomes dos hospedeiros"}, "smartname_empty": {"message": "Sem regras de nomeação inteligente"}, "smartname_update_rule": {"message": "At<PERSON><PERSON><PERSON> regra"}, "smartnamer_delay": {"message": "Atraso da nomeação da captura (ms)"}, "smartnamer_domain": {"message": "<PERSON><PERSON><PERSON>"}, "smartnamer_get_name_from_header_url": {"message": "Nomear a partir do cabeçalho/URL do documento"}, "smartnamer_get_name_from_page_content": {"message": "Nomear a partir do conteúdo da página"}, "smartnamer_get_name_from_page_title": {"message": "Nomear a partir do título da página"}, "smartnamer_get_obfuscated_name": {"message": "Usar nome ofuscado"}, "smartnamer_regexp": {"message": "Expressão regular"}, "smartnamer_selected_text": {"message": "Texto selecionado"}, "smartnamer_xpath_expr": {"message": "Expressão XPath"}, "smartnaming_rule": {"message": "Regra de nomeação inteligente"}, "smartnaming_rules": {"message": "Regras de nomeação inteligente"}, "sub_directory_name": {"message": "Nome da sub-pasta"}, "support_forum": {"message": "Fórum de suporte"}, "supported_sites": {"message": "Sites com suporte"}, "tbsn_quality_hd": {"message": "Qualidade média"}, "tbsn_quality_sd": {"message": "Qualidade baixa"}, "tell_me_more": {"message": "Mais informações sobre isso"}, "title": {"message": "Video DownloadHelper"}, "translation": {"message": "Tradução"}, "up": {"message": "Acima"}, "v9_about_qr": {"message": "Arquivo gerado"}, "v9_badge_new": {"message": "novo"}, "v9_blacklist_glob": {"message": "Use '*' para uma correspondência mais ampla."}, "v9_checkbox_remember_action": {"message": "Lembrar como ação padrão"}, "v9_chrome_noyt_text2": {"message": "Você pode usar o Video DownloadHelper para baixar vídeos do Youtube na versão para Firefox"}, "v9_chrome_noyt_text3": {"message": "Infelizmente, a loja do navegador Chrome não permite realizar transferências de vídeos, sendo assim não podemos incluir esta função na versão destinada ao Chrome."}, "v9_chrome_premium_hls": {"message": "Sem um status Premium, um download HLS somente pode ser realizado $1 após o download anterior."}, "v9_chrome_premium_required": {"message": "É necessário status Premium"}, "v9_chrome_warning_yt": {"message": "Aviso em extensões do Chrome e Youtube"}, "v9_coapp_help": {"message": "Clique aqui para solucionar seu problema."}, "v9_coapp_install": {"message": "Instalar Aplicativo auxiliar"}, "v9_coapp_installed": {"message": "Aplicativo auxiliar instalado"}, "v9_coapp_not_installed": {"message": "Aplicativo auxiliar não instalado"}, "v9_coapp_outdated": {"message": "Versão desatualizada do Aplicativo auxiliar - favor atualizar"}, "v9_coapp_recheck": {"message": "Verifique novamente"}, "v9_coapp_required": {"message": "Aplicativo auxiliar necessário"}, "v9_coapp_required_text": {"message": "Essa operação requer um Aplicativo extra para ser completada"}, "v9_coapp_unchecked": {"message": "Verificando Aplicativo auxiliar"}, "v9_coapp_update": {"message": "Atualize o Aplicativo auxiliar"}, "v9_converter_needs_reg": {"message": "Requer registro"}, "v9_converter_reg_audio": {"message": "<PERSON>ocê solicitou, deliberadamente ou através de uma regra de conversão automática, a criação de um arquivo somente áudio. Isto requer um conversor registrado."}, "v9_copy_settings_info_to_clipboard": {"message": "Copiar informações para a área de transferência"}, "v9_date_long_ago": {"message": "muito tempo atrás"}, "v9_date_today": {"message": "hoje"}, "v9_date_x_days_ago": {"message": "$1 dias atrás"}, "v9_date_yesterday": {"message": "ontem"}, "v9_dialog_audio_impossible": {"message": "Esse tipo de mídia não é compatível com o download somente de áudio"}, "v9_dialog_audio_impossible_title": {"message": "Não é possível fazer o download do áudio"}, "v9_error": {"message": "Erro "}, "v9_explain_qr1": {"message": "Você notará que o vídeo resultante contém uma marca d'água no canto."}, "v9_file_ready": {"message": "\"$1\" já está pronto"}, "v9_filepicker_select_download_dir": {"message": "Selecione o Diretório de Download"}, "v9_filepicker_select_file": {"message": "Selecione o arquivo"}, "v9_get_conversion_license": {"message": "Adquirir uma licença de conversão"}, "v9_history_button_clear": {"message": "<PERSON><PERSON>"}, "v9_history_button_start_recording": {"message": "<PERSON><PERSON><PERSON>"}, "v9_history_button_stop_recording": {"message": "<PERSON>ão lembrar meu histórico"}, "v9_history_input_search": {"message": "Pesquisa"}, "v9_history_no_entries": {"message": "<PERSON>enhuma entrada"}, "v9_history_no_recording_description": {"message": "Não registramos seu histórico de downloads. Você quer que o Video DownloadHelper se lembre do seu histórico de downloads?"}, "v9_history_no_recording_description_safe": {"message": "<PERSON><PERSON> se preocupe, tudo fica na sua máquina. Valorizamos sua privacidade."}, "v9_history_page_title": {"message": "Seu histórico de <PERSON>"}, "v9_lic_mismatch2": {"message": "A licença é para $1 mas a construção da extensão é para $2"}, "v9_lic_status_accepted": {"message": "Licença verificada"}, "v9_lic_status_blocked": {"message": "Licença bloqueada"}, "v9_lic_status_locked": {"message": "Licença expirada (revalidando)"}, "v9_lic_status_locked2": {"message": "Licença bloqueada (verifique seu e-mail)"}, "v9_lic_status_unset": {"message": "Licença não selecionada"}, "v9_lic_status_verifying": {"message": "Verificando licença..."}, "v9_menu_item_blacklist": {"message": "Lista Negra"}, "v9_menu_item_blacklist_domain": {"message": "<PERSON><PERSON><PERSON>"}, "v9_menu_item_blacklist_media": {"message": "Mí<PERSON>"}, "v9_menu_item_blacklist_page": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "v9_menu_item_details": {"message": "<PERSON><PERSON><PERSON>"}, "v9_menu_item_download_and_convert": {"message": "Baixe e converta para"}, "v9_menu_item_smartnaming": {"message": "Nomenclatura Inteligente"}, "v9_mup_max_variants": {"message": "Máximo de formatos"}, "v9_no": {"message": "Não"}, "v9_no_license_registered": {"message": "Nenhuma licença foi registrada"}, "v9_no_media_current_tab": {"message": "Nenhuma mídia para processar na aba atual"}, "v9_no_media_to_process_descr": {"message": "Clique em reproduzir no vídeo para ajudar a detectar arquivos..."}, "v9_no_validate_without_coapp": {"message": "O Aplicativo da companhia precisa estar instalado para a validação da licença"}, "v9_not_see_again": {"message": "Não ver essa mensagem novamente"}, "v9_panel_copy_url_button_label": {"message": "Copiar URL"}, "v9_panel_download_as_button_label": {"message": "Baixar Como…"}, "v9_panel_download_audio_button_label": {"message": "<PERSON><PERSON><PERSON>"}, "v9_panel_download_button_label": {"message": "Baixar"}, "v9_panel_downloadable_variant_no_details": {"message": "sem detalhes"}, "v9_panel_downloaded_delete_file_tooltip": {"message": "Excluir arquivo"}, "v9_panel_downloaded_retry_tooltip": {"message": "Baixar novamente"}, "v9_panel_downloaded_show_dir_tooltip": {"message": "Mostrar pasta de download"}, "v9_panel_downloading_stop": {"message": "parar"}, "v9_panel_error_coapp_failure_copy_report_button": {"message": "Co<PERSON>r de<PERSON>hes do bug"}, "v9_panel_error_coapp_failure_description": {"message": "Infelizmente, falhamos no download dessa mídia específica. Tentamos oferecer suporte ao maior número possível de sites, então ajudaria muito se você pudesse relatar esse erro!"}, "v9_panel_error_coapp_failure_report_button": {"message": "<PERSON><PERSON><PERSON>"}, "v9_panel_error_coapp_failure_title": {"message": "Download falhou"}, "v9_panel_error_coapp_too_old_button_udpate": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "v9_panel_error_nocoapp_button_install": {"message": "Baixe e Instale"}, "v9_panel_error_report_button2": {"message": "Reportar"}, "v9_panel_error_reported_button": {"message": "<PERSON>ado, obrigado!"}, "v9_panel_error_unknown_description": {"message": "Infelizmente, o complemento encontrou um erro inesperado. Ajudaria muito se você pudesse relatar esse erro (é anônimo!)."}, "v9_panel_footer_clean_all_tooltip": {"message": "Remova a mídia descoberta e baixada (os arquivos não são excluídos)"}, "v9_panel_footer_clean_tooltip": {"message": "Remover mídia des<PERSON>a"}, "v9_panel_footer_convert_local_tooltip": {"message": "Converter arquivos locais"}, "v9_panel_footer_show_history_tooltip": {"message": "Mostrar histórico de <PERSON>"}, "v9_panel_footer_show_in_popup_tooltip": {"message": "Mostrar em pop-up"}, "v9_panel_footer_show_in_sidebar_tooltip": {"message": "Mostrar na barra lateral"}, "v9_panel_variant_menu_prefer_format": {"message": "Prefira sempre este formato"}, "v9_panel_variant_menu_prefer_quality": {"message": "Prefira sempre esta qualidade"}, "v9_panel_view_clean": {"message": "Mostrar botão Limpar"}, "v9_panel_view_clean_all": {"message": "Mostrar botão Limpar Tudo"}, "v9_panel_view_hide_downloaded": {"message": "Ocultar automaticamente a mídia baixada"}, "v9_panel_view_open_settings": {"message": "<PERSON><PERSON> configu<PERSON>"}, "v9_panel_view_show_all_tabs": {"message": "<PERSON><PERSON> to<PERSON> as guias"}, "v9_panel_view_show_low_quality": {"message": "Mostrar mídia de baixa qualidade"}, "v9_panel_view_sort_reverse": {"message": "Classificação reversa"}, "v9_panel_view_sort_status": {"message": "Classificar por status"}, "v9_reset": {"message": "Reiniciar"}, "v9_save": {"message": "<PERSON><PERSON>"}, "v9_settings": {"message": "Configurações"}, "v9_settings_button_export": {"message": "Exportar configurações"}, "v9_settings_button_import": {"message": "Importar configurações"}, "v9_settings_button_reload": {"message": "Recarregar extensão"}, "v9_settings_button_reset": {"message": "Redefinir configurações"}, "v9_settings_checkbox_force_inbrowser": {"message": "Não use o CoApp quando possível"}, "v9_settings_checkbox_forget_on_close": {"message": "Limpar mídia descoberta quando a guia for fechada"}, "v9_settings_checkbox_notification": {"message": "Mostrar notificação quando o download for concluído"}, "v9_settings_checkbox_notification_incognito": {"message": "Mostrar notificações para navegação privada"}, "v9_settings_checkbox_thumbnail_in_notification": {"message": "Mostrar miniatura nas notificações"}, "v9_settings_checkbox_use_legacy_ui": {"message": "Use a interface legada"}, "v9_settings_checkbox_use_wide_ui": {"message": "Aumente o pop-up da extensão"}, "v9_settings_checkbox_view_convert_local": {"message": "Mostrar botão de Conversão Local"}, "v9_settings_download_directory": {"message": "Diretório do download"}, "v9_settings_download_directory_change": {"message": "<PERSON><PERSON>"}, "v9_settings_history_limit": {"message": "Esqueça o histórico de downloads após X dias"}, "v9_settings_license_check": {"message": "Verifique a chave de licença"}, "v9_settings_license_get": {"message": "Obter licença"}, "v9_settings_license_placeholder": {"message": "Insira a licença"}, "v9_settings_theme_dark": {"message": "escuro"}, "v9_settings_theme_light": {"message": "claro"}, "v9_settings_theme_system": {"message": "sistema"}, "v9_settings_theme_title": {"message": "<PERSON><PERSON>"}, "v9_settings_variants_clear": {"message": "Limpar"}, "v9_settings_variants_title": {"message": "Formato preferido"}, "v9_short_help": {"message": "<PERSON><PERSON><PERSON>?"}, "v9_smartnaming_max_length": {"message": "Comprimento máximo"}, "v9_smartnaming_reset_for": {"message": "Redefinir para"}, "v9_smartnaming_reset_for_all": {"message": "Redefina todas as regras de nomes de host."}, "v9_smartnaming_result": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "v9_smartnaming_save_for": {"message": "<PERSON><PERSON> para"}, "v9_smartnaming_save_for_all": {"message": "Salve para todos os nomes de host."}, "v9_smartnaming_selector": {"message": "Texto do seletor CSS (opcional)"}, "v9_smartnaming_template": {"message": "Modelo. Use: %title %hostname %pathname %selector"}, "v9_smartnaming_test": {"message": "<PERSON>e"}, "v9_smartnaming_title": {"message": "Nomenclatura Inteligente"}, "v9_tell_me_more": {"message": "Mais informações sobre isso"}, "v9_user_message_auto_hide_downloaded": {"message": "Ocultar automaticamente a mídia baixada?"}, "v9_user_message_no_incognito_body": {"message": "Video DownloadHelper não está habilitado em janelas privadas/anônimas. Você precisa ativar essa opção manualmente (isso não é obrigatório)."}, "v9_user_message_no_incognito_open_settings": {"message": "Ativar nas Configurações do Navegador"}, "v9_user_message_no_incognito_title": {"message": "Sem modo de Navegação Anônima"}, "v9_user_message_one_hundred_downloads": {"message": "Você baixou 100 vídeos!"}, "v9_user_message_one_hundred_downloads_body": {"message": "Esperamos que você esteja gostando do Vídeo DownloadHelper :) Você se importaria de escrever uma boa crítica no site do complemento?"}, "v9_user_message_one_hundred_downloads_leave_review": {"message": "Deixe um comentário"}, "v9_user_message_one_hundred_downloads_never_show_again": {"message": "Não pergunte novamente"}, "v9_vdh_notification": {"message": "Video DownloadHelper"}, "v9_weh_prefs_description_contextMenuEnabled": {"message": "Comandos de acesso a partir do botão direito na página"}, "v9_weh_prefs_label_downloadControlledMax": {"message": "Máximo de downloads simultâneos"}, "v9_yes": {"message": "<PERSON>m"}, "v9_yt_bulk_detected": {"message": "Detectados $1 vídeos do Youtube"}, "v9_yt_bulk_detected_trigger": {"message": "Iniciar download em massa"}, "validate_license": {"message": "Registrar licença"}, "variants_list_adp": {"message": "<PERSON><PERSON><PERSON>"}, "variants_list_full": {"message": "<PERSON><PERSON><PERSON>"}, "vdh_notification": {"message": "Video DownloadHelper"}, "version": {"message": "Versão $1"}, "video_only": {"message": "Somente vídeo"}, "video_qualities": {"message": "Qualidades de vídeo"}, "weh_prefs_alertDialogType_option_panel": {"message": "<PERSON><PERSON>"}, "weh_prefs_alertDialogType_option_tab": {"message": "Aba"}, "weh_prefs_coappDownloads_option_ask": {"message": "Pergun<PERSON>"}, "weh_prefs_coappDownloads_option_browser": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "weh_prefs_coappDownloads_option_coapp": {"message": "Aplicativo da companhia"}, "weh_prefs_dashOnAdp_option_audio": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_dashOnAdp_option_audio_video": {"message": "Agregar <PERSON> e vídeo"}, "weh_prefs_dashOnAdp_option_video": {"message": "Baixar vídeo"}, "weh_prefs_description_adpHide": {"message": "Não mostrar variantes ADP na lista de downloads"}, "weh_prefs_description_alertDialogType": {"message": "Como os alertas são exibidos"}, "weh_prefs_description_autoPin": {"message": "Fixa o item após baixá-lo"}, "weh_prefs_description_avplayEnabled": {"message": "Permitir executar o vídeo com o Aplicativo da companhia"}, "weh_prefs_description_blacklistEnabled": {"message": "Impede que alguns sites ativem o reconhecimento de mídia."}, "weh_prefs_description_bulkEnabled": {"message": "Habilitar operações de transferência em massa"}, "weh_prefs_description_checkCoappOnStartup": {"message": "Na inicialização do Complemento, verifique quando o Aplicativo da companhia está disponível para uma melhor detecção das mídias"}, "weh_prefs_description_chunkedCoappDataRequests": {"message": "Pedir dados fragmentados utilizando o Aplicativo da companhia"}, "weh_prefs_description_chunkedCoappManifestsRequests": {"message": "Pedir 'manifests' fragmentados utilizando o Aplicativo da companhia"}, "weh_prefs_description_chunksConcurrentDownloads": {"message": "Número máximo de segmentos para serem transferidos em paralelo"}, "weh_prefs_description_chunksEnabled": {"message": "Transmissão fragmentada habilitada"}, "weh_prefs_description_chunksPrefetchCount": {"message": "Quantos segmentos para transferir com antecedência"}, "weh_prefs_description_coappDownloads": {"message": "Aplicativo realizando a transferência atual"}, "weh_prefs_description_coappIdleExit": {"message": "Fechar <PERSON>amente após um tempo em milisegundos, desabilitado se o tempo for 0"}, "weh_prefs_description_coappRestartDelay": {"message": "Atraso em milisegundos quando reiniciar o Aplicativo da companhia"}, "weh_prefs_description_coappUseProxy": {"message": "CoApp utiliza o mesmo proxy da requisição original"}, "weh_prefs_description_contentRedirectEnabled": {"message": "Alguns sites podem retornar uma nova URL ao invés de um conteúdo de mídia"}, "weh_prefs_description_contextMenuEnabled": {"message": "Comandos de acesso a partir do botão direito na página"}, "weh_prefs_description_convertControlledMax": {"message": "Número máximo de agregações simultâneas ou tarefas de conversão"}, "weh_prefs_description_converterAggregTuneH264": {"message": "Forçar ajuste na agregação em H264"}, "weh_prefs_description_converterKeepTmpFiles": {"message": "Não remova os arquivos temporários após o processamento"}, "weh_prefs_description_converterThreads": {"message": "Número de threads a serem utilizadas durante a conversão"}, "weh_prefs_description_dashEnabled": {"message": "Colisão de fragmentos da transmissão habilitada"}, "weh_prefs_description_dashHideM4s": {"message": "Não mostrar entradas .m4s na lista de downloads"}, "weh_prefs_description_dashOnAdp": {"message": "Quando o DASH contiver áudio e vídeo"}, "weh_prefs_description_dialogAutoClose": {"message": "Diálogos são fechados quando perderem o foco"}, "weh_prefs_description_downloadControlledMax": {"message": "Controla o número de transferências geradas pelo complemento executadas simultaneamente para preservar um pouco de largura de banda."}, "weh_prefs_description_downloadRetries": {"message": "Número de tentativas de download"}, "weh_prefs_description_downloadRetryDelay": {"message": "<PERSON><PERSON><PERSON> entre as tentativas de download (ms)"}, "weh_prefs_description_downloadStreamControlledMax": {"message": "Controla o número de transmissões sendo transferidas de um único item"}, "weh_prefs_description_fileDialogType": {"message": "Como os diálogos de arquivo são exibidos"}, "weh_prefs_description_galleryNaming": {"message": "Como nomear arquivos da galeria de downloads"}, "weh_prefs_description_hitsGotoTab": {"message": "Exibir um link na descrição de entrada para trocar para uma aba de vídeo"}, "weh_prefs_description_hlsDownloadAsM2ts": {"message": "Baixar transmissões HLS como M2TS"}, "weh_prefs_description_hlsEnabled": {"message": "Fluxo de HLS fragmentado habilitado"}, "weh_prefs_description_hlsEndTimeout": {"message": "Tempo limite em segundos para parar de esperar por novos blocos HLS"}, "weh_prefs_description_hlsRememberPrevLiveChunks": {"message": "Recorda o bloco HLS anterior"}, "weh_prefs_description_iconActivation": {"message": "Quando ativar o ícone da barra de ferramentas"}, "weh_prefs_description_iconBadge": {"message": "Exibição de insígnias na barra de ferramenteas"}, "weh_prefs_description_ignoreProtectedVariants": {"message": "Não exibir variações que são protegidas"}, "weh_prefs_description_lastDownloadDirectory": {"message": "Utilizado apenas com o processador de transferências do Aplicativo da companhia"}, "weh_prefs_description_mediaExtensions": {"message": "Extensões a serem consideradas como mídia"}, "weh_prefs_description_medialinkAutoDetect": {"message": "Executar a cada carregamento de página (Pode impactar o desempenho)"}, "weh_prefs_description_medialinkExtensions": {"message": "Extensões de arquivo a serem consideradas para captura da galeria"}, "weh_prefs_description_medialinkMaxHits": {"message": "Número limite de entradas detectadas na galeria"}, "weh_prefs_description_medialinkMinFilesPerGroup": {"message": "<PERSON><PERSON><PERSON> de entradas a serem detectadas como galeria"}, "weh_prefs_description_medialinkMinImgSize": {"message": "<PERSON><PERSON><PERSON> mínimo de imagem para ser considera como uma imagem da galeria"}, "weh_prefs_description_medialinkScanImages": {"message": "Detectar as imagens na página"}, "weh_prefs_description_medialinkScanLinks": {"message": "Detectar conteúdo diretamente linkado da página"}, "weh_prefs_description_mediaweightMinSize": {"message": "Ignora os itens abaixo deste tamanho."}, "weh_prefs_description_mediaweightThreshold": {"message": "Força a detecção de mídia acima deste tamanho."}, "weh_prefs_description_monitorNetworkRequests": {"message": "Transferir utilizando os mesmos cabeçalhos da requisição original"}, "weh_prefs_description_mpegtsHideTs": {"message": "Não mostrar entradas .ts na lista de downloads"}, "weh_prefs_description_networkFilterOut": {"message": "Expressão regular para ignorar alguns urls de mídia."}, "weh_prefs_description_networkProbe": {"message": "Sondar tráfico de internet para detecção de acertos"}, "weh_prefs_description_noPrivateNotification": {"message": "Nenhuma notificação sobre itens privados"}, "weh_prefs_description_notifyReady": {"message": "Notificar ao concluir"}, "weh_prefs_description_orphanExpiration": {"message": "Tempo em segundos antes de remover itens órfãos."}, "weh_prefs_description_qualitiesMaxVariants": {"message": "Número máximo de variantes exibidas para um mesmo vídeo."}, "weh_prefs_description_rememberLastDir": {"message": "Usar a última pasta de downloads como pasta padrão"}, "weh_prefs_description_smartnamerFnameMaxlen": {"message": "Garante que os nomes de arquivos não excederão este tamanho."}, "weh_prefs_description_smartnamerFnameSpaces": {"message": "Como lidar com os espaços nos nomes de vídeos"}, "weh_prefs_description_tbsnEnabled": {"message": "Permitir detecção e download de videos do Facebook"}, "weh_prefs_description_titleMode": {"message": "Quão longos devem ser os títulos de vídeo em exibição no painel"}, "weh_prefs_description_toolsMenuEnabled": {"message": "Comandos de acesso a partir do menu de ferramentas"}, "weh_prefs_description_use_native_filepicker": {"message": "Use o selecionador de arquivos do SO"}, "weh_prefs_fileDialogType_option_panel": {"message": "<PERSON><PERSON>"}, "weh_prefs_fileDialogType_option_tab": {"message": "Aba"}, "weh_prefs_galleryNaming_option_index_url": {"message": "Índice - Url"}, "weh_prefs_galleryNaming_option_type_index": {"message": "Tipo - Índice"}, "weh_prefs_galleryNaming_option_url": {"message": "Url"}, "weh_prefs_iconActivation_option_anytab": {"message": "Itens de qualquer aba"}, "weh_prefs_iconActivation_option_currenttab": {"message": "<PERSON><PERSON> da aba atual"}, "weh_prefs_iconBadge_option_activetab": {"message": "Mídia a partir da aba ativa"}, "weh_prefs_iconBadge_option_anytab": {"message": "Mídia a partir de qualquer aba"}, "weh_prefs_iconBadge_option_mixed": {"message": "<PERSON><PERSON>"}, "weh_prefs_iconBadge_option_none": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_iconBadge_option_pinned": {"message": "Itens fixados"}, "weh_prefs_iconBadge_option_tasks": {"message": "Executando tarefas"}, "weh_prefs_label_adpHide": {"message": "Esconder variações ADP"}, "weh_prefs_label_alertDialogType": {"message": "Diálogo de alerta"}, "weh_prefs_label_autoPin": {"message": "Fixar automaticamente"}, "weh_prefs_label_avplayEnabled": {"message": "Reprodutor habilitado"}, "weh_prefs_label_blacklistEnabled": {"message": "Habilitar lista negra"}, "weh_prefs_label_bulkEnabled": {"message": "Em massa habilitado"}, "weh_prefs_label_checkCoappOnStartup": {"message": "Verificar CoApp na inicialização"}, "weh_prefs_label_chunkedCoappDataRequests": {"message": "Requisições de dados framentados do CoApp"}, "weh_prefs_label_chunkedCoappManifestsRequests": {"message": "Requisições de 'manifests' framentados do CoApp"}, "weh_prefs_label_chunksConcurrentDownloads": {"message": "Downloads simultâneos dos fragmentos"}, "weh_prefs_label_chunksEnabled": {"message": "Transmissão fragmentada"}, "weh_prefs_label_chunksPrefetchCount": {"message": "Pré-busca de contagem de fragmentos"}, "weh_prefs_label_coappDownloads": {"message": "Processador de transferência"}, "weh_prefs_label_coappIdleExit": {"message": "Tempo ocioso para saída do CoApp"}, "weh_prefs_label_coappRestartDelay": {"message": "Atraso de reinicialização do CoApp"}, "weh_prefs_label_coappUseProxy": {"message": "Proxy do CoApp"}, "weh_prefs_label_contentRedirectEnabled": {"message": "Redirecionamento de conteúdo ativado"}, "weh_prefs_label_contextMenuEnabled": {"message": "<PERSON><PERSON>"}, "weh_prefs_label_convertControlledMax": {"message": "Operações simultâneas do conversor"}, "weh_prefs_label_converterAggregTuneH264": {"message": "Melhoria H264"}, "weh_prefs_label_converterKeepTmpFiles": {"message": "Manter arquivos temporários"}, "weh_prefs_label_converterThreads": {"message": "Threads de conversão"}, "weh_prefs_label_dashEnabled": {"message": "DASH habilitado"}, "weh_prefs_label_dashHideM4s": {"message": "Esconder .m4s"}, "weh_prefs_label_dashOnAdp": {"message": "Transmissões DASH"}, "weh_prefs_label_dialogAutoClose": {"message": "Diálogos que fecham automaticamente"}, "weh_prefs_label_downloadControlledMax": {"message": "Máximo de downloads simultâneos"}, "weh_prefs_label_downloadRetries": {"message": "Tentativas de transferência"}, "weh_prefs_label_downloadRetryDelay": {"message": "Atraso de tentativas"}, "weh_prefs_label_downloadStreamControlledMax": {"message": "Máximo concorrente de transferência de transmissões"}, "weh_prefs_label_fileDialogType": {"message": "Diálogo de arquivo"}, "weh_prefs_label_galleryNaming": {"message": "Nome do arquivo da Galeria"}, "weh_prefs_label_hitsGotoTab": {"message": "Exibir alça Vá para a aba"}, "weh_prefs_label_hlsDownloadAsM2ts": {"message": "HLS como M2TS"}, "weh_prefs_label_hlsEnabled": {"message": "HLS habilitado"}, "weh_prefs_label_hlsEndTimeout": {"message": "Tempo limite de término do HLS"}, "weh_prefs_label_hlsRememberPrevLiveChunks": {"message": "Histórico do HLS"}, "weh_prefs_label_iconActivation": {"message": "Ativação do ícone"}, "weh_prefs_label_iconBadge": {"message": "Ícone do complemento"}, "weh_prefs_label_ignoreProtectedVariants": {"message": "Ignorar variantes protegidas"}, "weh_prefs_label_lastDownloadDirectory": {"message": "Diretório padrão de download"}, "weh_prefs_label_mediaExtensions": {"message": "Detectar extensões"}, "weh_prefs_label_medialinkAutoDetect": {"message": "Detecção automática de galeria"}, "weh_prefs_label_medialinkExtensions": {"message": "Extensões de Link de Mídia"}, "weh_prefs_label_medialinkMaxHits": {"message": "Entrada máxima de arquivos"}, "weh_prefs_label_medialinkMinFilesPerGroup": {"message": "Entrada mínima de arquivos"}, "weh_prefs_label_medialinkMinImgSize": {"message": "<PERSON><PERSON><PERSON> m<PERSON>"}, "weh_prefs_label_medialinkScanImages": {"message": "Detectar embed em imagens"}, "weh_prefs_label_medialinkScanLinks": {"message": "Detectar links para mídia"}, "weh_prefs_label_mediaweightMinSize": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_label_mediaweightThreshold": {"message": "<PERSON><PERSON> <PERSON>"}, "weh_prefs_label_monitorNetworkRequests": {"message": "Cabeçalhos de requisição"}, "weh_prefs_label_mpegtsHideTs": {"message": "Esconder .ts"}, "weh_prefs_label_networkFilterOut": {"message": "Sem filtragem de rede"}, "weh_prefs_label_networkProbe": {"message": "Examinar rede"}, "weh_prefs_label_noPrivateNotification": {"message": "Notificação privada"}, "weh_prefs_label_notifyReady": {"message": "Notificação"}, "weh_prefs_label_orphanExpiration": {"message": "Tempo limite para órfãos"}, "weh_prefs_label_qualitiesMaxVariants": {"message": "Máximo de variantes"}, "weh_prefs_label_rememberLastDir": {"message": "Lembrar último diretó<PERSON>/pasta"}, "weh_prefs_label_smartnamerFnameMaxlen": {"message": "Comprimento máximo de nome de arquivo"}, "weh_prefs_label_smartnamerFnameSpaces": {"message": "<PERSON><PERSON><PERSON><PERSON> longos no painel principal"}, "weh_prefs_label_tbsnEnabled": {"message": "Suporte Facebook"}, "weh_prefs_label_tbvwsExtractionMethod": {"message": "Método de extração"}, "weh_prefs_label_titleMode": {"message": "<PERSON><PERSON><PERSON><PERSON> longos no painel principal"}, "weh_prefs_label_toolsMenuEnabled": {"message": "Menu de Ferramentas"}, "weh_prefs_label_use_native_filepicker": {"message": "Usar o selecionador de arquivos nativo"}, "weh_prefs_smartnamerFnameSpaces_option_hyphen": {"message": "Substituir por hifens"}, "weh_prefs_smartnamerFnameSpaces_option_keep": {"message": "<PERSON><PERSON>"}, "weh_prefs_smartnamerFnameSpaces_option_remove": {"message": "Remover"}, "weh_prefs_smartnamerFnameSpaces_option_underscore": {"message": "Substituir por sublinhados"}, "weh_prefs_titleMode_option_left": {"message": "Reticências à esquerda"}, "weh_prefs_titleMode_option_multiline": {"message": "Em várias linhas"}, "weh_prefs_titleMode_option_right": {"message": "Reticências à direita"}, "yes": {"message": "<PERSON>m"}, "you_downloaded_n_videos": {"message": "Você acabou de baixar com sucesso seu $1º arquivo com o Video DownloadHelper."}}