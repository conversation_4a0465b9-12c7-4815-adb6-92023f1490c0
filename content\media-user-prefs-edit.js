var tt=Object.create;var be=Object.defineProperty;var rt=Object.getOwnPropertyDescriptor;var ot=Object.getOwnPropertyNames;var nt=Object.getPrototypeOf,it=Object.prototype.hasOwnProperty;var ie=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var at=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of ot(t))!it.call(e,i)&&i!==r&&be(e,i,{get:()=>t[i],enumerable:!(o=rt(t,i))||o.enumerable});return e};var ae=(e,t,r)=>(r=e!=null?tt(nt(e)):{},at(t||!e||!e.__esModule?be(r,"default",{value:e,enumerable:!0}):r,e));var Y=ie((se,ve)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof se<"u")t(ve);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:se,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=o=>{let i={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(i).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class n extends WeakMap{constructor(c,y=void 0){super(y),this.createItem=c}get(c){return this.has(c)||this.set(c,this.createItem(c)),super.get(c)}}let a=u=>u&&typeof u=="object"&&typeof u.then=="function",s=(u,c)=>(...y)=>{o.runtime.lastError?u.reject(new Error(o.runtime.lastError.message)):c.singleCallbackArg||y.length<=1&&c.singleCallbackArg!==!1?u.resolve(y[0]):u.resolve(y)},p=u=>u==1?"argument":"arguments",l=(u,c)=>function(h,...S){if(S.length<c.minArgs)throw new Error(`Expected at least ${c.minArgs} ${p(c.minArgs)} for ${u}(), got ${S.length}`);if(S.length>c.maxArgs)throw new Error(`Expected at most ${c.maxArgs} ${p(c.maxArgs)} for ${u}(), got ${S.length}`);return new Promise((O,k)=>{if(c.fallbackToNoCallback)try{h[u](...S,s({resolve:O,reject:k},c))}catch(g){console.warn(`${u} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,g),h[u](...S),c.fallbackToNoCallback=!1,c.noCallback=!0,O()}else c.noCallback?(h[u](...S),O()):h[u](...S,s({resolve:O,reject:k},c))})},m=(u,c,y)=>new Proxy(c,{apply(h,S,O){return y.call(S,u,...O)}}),f=Function.call.bind(Object.prototype.hasOwnProperty),M=(u,c={},y={})=>{let h=Object.create(null),S={has(k,g){return g in u||g in h},get(k,g,N){if(g in h)return h[g];if(!(g in u))return;let b=u[g];if(typeof b=="function")if(typeof c[g]=="function")b=m(u,u[g],c[g]);else if(f(y,g)){let R=l(g,y[g]);b=m(u,u[g],R)}else b=b.bind(u);else if(typeof b=="object"&&b!==null&&(f(c,g)||f(y,g)))b=M(b,c[g],y[g]);else if(f(y,"*"))b=M(b,c[g],y["*"]);else return Object.defineProperty(h,g,{configurable:!0,enumerable:!0,get(){return u[g]},set(R){u[g]=R}}),b;return h[g]=b,b},set(k,g,N,b){return g in h?h[g]=N:u[g]=N,!0},defineProperty(k,g,N){return Reflect.defineProperty(h,g,N)},deleteProperty(k,g){return Reflect.deleteProperty(h,g)}},O=Object.create(u);return new Proxy(O,S)},v=u=>({addListener(c,y,...h){c.addListener(u.get(y),...h)},hasListener(c,y){return c.hasListener(u.get(y))},removeListener(c,y){c.removeListener(u.get(y))}}),w=new n(u=>typeof u!="function"?u:function(y){let h=M(y,{},{getContent:{minArgs:0,maxArgs:0}});u(h)}),x=new n(u=>typeof u!="function"?u:function(y,h,S){let O=!1,k,g=new Promise(Q=>{k=function(I){O=!0,Q(I)}}),N;try{N=u(y,h,k)}catch(Q){N=Promise.reject(Q)}let b=N!==!0&&a(N);if(N!==!0&&!b&&!O)return!1;let R=Q=>{Q.then(I=>{S(I)},I=>{let ne;I&&(I instanceof Error||typeof I.message=="string")?ne=I.message:ne="An unexpected error occurred",S({__mozWebExtensionPolyfillReject__:!0,message:ne})}).catch(I=>{console.error("Failed to send onMessage rejected reply",I)})};return R(b?N:g),!0}),J=({reject:u,resolve:c},y)=>{o.runtime.lastError?o.runtime.lastError.message===t?c():u(new Error(o.runtime.lastError.message)):y&&y.__mozWebExtensionPolyfillReject__?u(new Error(y.message)):c(y)},D=(u,c,y,...h)=>{if(h.length<c.minArgs)throw new Error(`Expected at least ${c.minArgs} ${p(c.minArgs)} for ${u}(), got ${h.length}`);if(h.length>c.maxArgs)throw new Error(`Expected at most ${c.maxArgs} ${p(c.maxArgs)} for ${u}(), got ${h.length}`);return new Promise((S,O)=>{let k=J.bind(null,{resolve:S,reject:O});h.push(k),y.sendMessage(...h)})},et={devtools:{network:{onRequestFinished:v(w)}},runtime:{onMessage:v(x),onMessageExternal:v(x),sendMessage:D.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:D.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},oe={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return i.privacy={network:{"*":oe},services:{"*":oe},websites:{"*":oe}},M(o,et,i)};e.exports=r(chrome)}else e.exports=globalThis.browser})});var Se=ie((wt,$)=>{"use strict";$.exports.browser=Y();var U;typeof browser>"u"&&typeof chrome<"u"&&chrome.runtime?/\bOPR\//.test(navigator.userAgent)?U="opera":U="chrome":/\bEdge\//.test(navigator.userAgent)?U="edge":U="firefox",$.exports.browserType=U,typeof $.exports.browser.action>"u"&&($.exports.browser.action=$.exports.browser.browserAction),$.exports.isBrowser=(...e)=>{for(let t=0;t<e.length;t++)if(e[t]==$.exports.browserType)return!0;return!1},$.exports.error=e=>{console.groupCollapsed(e.message),e.stack&&console.error(e.stack),console.groupEnd()}});var Oe=ie((xt,Me)=>{"use strict";var{browser:le}=Se(),Ce={},Te=new RegExp("\\$[a-zA-Z]*([0-9]+)\\$","g"),Ee=!1,st=le.storage.local.get("wehI18nCustom").then(e=>{Ee=!0;let t=e.wehI18nCustom;t&&Object.assign(Ce,t)});function lt(e,t){if(Ee||console.warn("Using `weh._` before custom strings were loaded:",e),/-/.test(e)){let o=e.replace(/-/g,"_");console.warn("Wrong i18n message name. Should it be",o,"instead of",e,"?"),e=o}let r=Ce[e];if(t&&!Array.isArray(t)&&(t=[t]),r&&r.message.length>0)return(r.message||"").replace(Te,o=>{let i=Te.exec(o);return i&&t&&t[parseInt(i[1])-1]||"??"});try{return t?le.i18n.getMessage(e,t):le.i18n.getMessage(e)}catch{return""}}Me.exports={getMessage:lt,custom_strings_ready:st}});var T=ae(Oe(),1);var ke="stable";function Ne(e,t,r=document){let o=new CustomEvent(`reef:${e}`,{bubbles:!0,cancelable:!0,detail:t});return r.dispatchEvent(o)}function ct(e){return typeof e=="string"?document.querySelector(e):e}var Ie=["input","option","textarea"],Z=["value","checked","selected"],Pe=["checked","selected"];function je(e){return["false","null","undefined","0","-0","NaN","0n","-0n"].includes(e)}function $e(e,t,r,o){if(!t.startsWith("on")||!o||e[t])return;let i=o[r.split("(")[0]];i&&(e[t]=i)}function qe(e,t){let r=t.replace(/\s+/g,"").toLowerCase();return!(!["src","href","xlink:href"].includes(e)||!r.includes("javascript:")&&!r.includes("data:text/html"))||!!(e.startsWith("on")||e.startsWith("@on")||e.startsWith("#on"))||void 0}function Le(e,t,r,o){$e(e,t,r,o),qe(t,r)||(Z.includes(t)&&(e[t]=t==="value"?r:" "),e.setAttribute(t,r))}function X(e,t){Z.includes(t)&&(e[t]=""),e.removeAttribute(t)}function ue(e,t){if(e.nodeType===1){for(let{name:r,value:o}of e.attributes){if(qe(r,o)){X(e,r),$e(e,r,o,t);continue}if(!r.startsWith("@")&&!r.startsWith("#"))continue;let i=r.slice(1);X(e,r),Pe.includes(i)&&je(o)||Le(e,i,o,t)}if(e.childNodes)for(let r of e.childNodes)ue(r,t)}}function Ve(e){return e.childNodes&&e.childNodes.length?null:e.textContent}function ce(e,t,r){let o=e.childNodes,i=t.childNodes;(function(n){let a=n.querySelectorAll("script");for(let s of a)s.remove()})(e)||(o.forEach(function(n,a){if(!i[a]){let m=n.cloneNode(!0);return ue(m,r),void t.append(m)}if(s=n,p=i[a],typeof s.nodeType=="number"&&s.nodeType!==p.nodeType||typeof s.tagName=="string"&&s.tagName!==p.tagName||typeof s.id=="string"&&s.id&&s.id!==p.id||"getAttribute"in s&&"getAttribute"in p&&s.getAttribute("key")!==p.getAttribute("key")||typeof s.src=="string"&&s.src&&s.src!==p.src){let m=function(f,M){if(f.nodeType!==1)return;let v=f.getAttribute("id"),w=f.getAttribute("key");if(!v||!w)return;let x=v?`#${v}`:`[key="${w}"]`;return M.querySelector(`:scope > ${x}`)}(n,t);if(!m){let f=n.cloneNode(!0);return ue(f,r),void i[a].before(f)}i[a].before(m)}var s,p;if(o[a]&&"hasAttribute"in o[a]&&o[a].hasAttribute("reef-ignore")||(function(m,f,M){if(m.nodeType!==1)return;let v=m.attributes,w=f.attributes;for(let{name:x,value:J}of v){if(x.startsWith("#")||Z.includes(x)&&Ie.includes(m.tagName.toLowerCase()))continue;let D=x.startsWith("@")?x.slice(1):x;Pe.includes(D)&&je(J)?X(f,D):Le(f,D,J,M)}for(let{name:x,value:J}of w)v[x]||Z.includes(x)&&Ie.includes(f.tagName.toLowerCase())||X(f,x)}(n,i[a],r),n.nodeName.includes("-")))return;let l=Ve(n);if(l&&l!==Ve(i[a])&&(i[a].textContent=l),n.childNodes.length||!i[a].childNodes.length){if(!i[a].childNodes.length&&n.childNodes.length){let m=document.createDocumentFragment();return ce(n,m,r),void i[a].appendChild(m)}n.childNodes.length&&ce(n,i[a],r)}else i[a].innerHTML=""}),function(n,a){let s=n.length-a.length;if(!(s<1))for(;s>0;s--)n[n.length-1].remove()}(i,o))}function De(e,t,r){let o=ct(e),i=function(n){let a=new DOMParser().parseFromString(`<body><template>${n}</template></body>`,"text/html");return a.body?a.body.firstElementChild.content:document.createElement("body")}(t);Ne("before-render",null,o)&&(ce(i,o,r),Ne("render",null,o))}function q(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var dt=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return C(t)},e.prototype.toString=function(){return"None"},e}(),d=new dt;Object.freeze(d);var mt=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return A(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return _(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(q(this.value),")")},e.EMPTY=new e(void 0),e}(),A=mt,F;(function(e){function t(){for(var i=[],n=0;n<arguments.length;n++)i[n]=arguments[n];for(var a=[],s=0,p=i;s<p.length;s++){var l=p[s];if(l.isSome())a.push(l.value);else return l}return A(a)}e.all=t;function r(){for(var i=[],n=0;n<arguments.length;n++)i[n]=arguments[n];for(var a=0,s=i;a<s.length;a++){var p=s[a];return p.isSome(),p}return d}e.any=r;function o(i){return i instanceof A||i===d}e.isOption=o})(F||(F={}));var pt=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(q(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(q(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new C(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return d},e.prototype.toString=function(){return"Err(".concat(q(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new de(this)},e.EMPTY=new e(void 0),e}();var C=pt,ft=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(q(this.value)),{cause:this.value})},e.prototype.map=function(t){return new _(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return A(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(q(this.value),")")},e.prototype.toAsyncResult=function(){return new de(this)},e.EMPTY=new e(void 0),e}();var _=ft,H;(function(e){function t(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];for(var p=[],l=0,m=a;l<m.length;l++){var f=m[l];if(f.isOk())p.push(f.value);else return f}return new _(p)}e.all=t;function r(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];for(var p=[],l=0,m=a;l<m.length;l++){var f=m[l];if(f.isOk())return f;p.push(f.error)}return new C(p)}e.any=r;function o(a){try{return new _(a())}catch(s){return new C(s)}}e.wrap=o;function i(a){try{return a().then(function(s){return new _(s)}).catch(function(s){return new C(s)})}catch(s){return Promise.resolve(new C(s))}}e.wrapAsync=i;function n(a){return a instanceof C||a instanceof _}e.isResult=n})(H||(H={}));var Re=function(e,t,r,o){function i(n){return n instanceof r?n:new r(function(a){a(n)})}return new(r||(r=Promise))(function(n,a){function s(m){try{l(o.next(m))}catch(f){a(f)}}function p(m){try{l(o.throw(m))}catch(f){a(f)}}function l(m){m.done?n(m.value):i(m.value).then(s,p)}l((o=o.apply(e,t||[])).next())})},Fe=function(e,t){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(l){return function(m){return p([l,m])}}function p(l){if(o)throw new TypeError("Generator is already executing.");for(;a&&(a=0,l[0]&&(r=0)),r;)try{if(o=1,i&&(n=l[0]&2?i.return:l[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,l[1])).done)return n;switch(i=0,n&&(l=[l[0]&2,n.value]),l[0]){case 0:case 1:n=l;break;case 4:return r.label++,{value:l[1],done:!1};case 5:r.label++,i=l[1],l=[0];continue;case 7:l=r.ops.pop(),r.trys.pop();continue;default:if(n=r.trys,!(n=n.length>0&&n[n.length-1])&&(l[0]===6||l[0]===2)){r=0;continue}if(l[0]===3&&(!n||l[1]>n[0]&&l[1]<n[3])){r.label=l[1];break}if(l[0]===6&&r.label<n[1]){r.label=n[1],n=l;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(l);break}n[2]&&r.ops.pop(),r.trys.pop();continue}l=t.call(e,r)}catch(m){l=[6,m],i=0}finally{o=n=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}},de=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(o){return Re(r,void 0,void 0,function(){var i;return Fe(this,function(n){return o.isErr()?[2,o]:(i=t(o.value),[2,i instanceof e?i.promise:i])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(o){return Re(r,void 0,void 0,function(){var i;return Fe(this,function(n){switch(n.label){case 0:return o.isErr()?[2,o]:(i=_,[4,t(o.value)]);case 1:return[2,i.apply(void 0,[n.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();function E(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return A(r);return d},count:function(t){return this.reduce((r,o)=>(t(o)&&r++,r),0)},reduce:function(t,r){let o=r;for(let i of this)o=t(o,i);return o},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>A(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?A(r):d)},enumerate:function(){let t=this;return E(function*(){let r=0;for(let o of t)yield[r,o],r++})()},filterMap:function(t){let r=this;return E(function*(){for(let o of r){let i=t(o);i.isSome()&&(yield i.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return E(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return E(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return E(function*(){for(let t of e)yield t})()});var G=ae(Y(),1);var He=(e,t)=>typeof e[t]=="string";function V(e){try{if(He(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return _(e.__serializer_value);if(e.__serializer_tag==="regex"){let o=new RegExp(e.__serializer_value);return _(o)}else if(e.__serializer_tag==="array"){let o=[];for(let i of e.__serializer_value){let n=V(i);if(n.isErr())return n;o.push(n.unwrap())}return _(o)}else if(e.__serializer_tag==="map"){let o=[];for(let i of e.__serializer_value){let n=V(i);if(n.isErr())return n;o.push(n.unwrap())}return _(new Map(o))}else if(e.__serializer_tag==="set"){let o=[];for(let i of e.__serializer_value){let n=V(i);if(n.isErr())return n;o.push(n.unwrap())}return _(new Set(o))}else if(e.__serializer_tag==="result_ok"){let o=e.__serializer_value,i=V(o);return i.isErr()?i:_(_(i.unwrap()))}else if(e.__serializer_tag==="result_err"){let o=e.__serializer_value,i=V(o);return i.isErr()?i:_(C(i.unwrap()))}else if(e.__serializer_tag==="option_some"){let o=e.__serializer_value,i=V(o);return i.isErr()?i:_(A(i.unwrap()))}else if(e.__serializer_tag==="option_none")return _(d)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return C("This object was not serialized with Serialize");let r={};for(let o of Object.keys(e))if(typeof o=="string"){let i=V(e[o]);if(i.isErr())return i;r[o]=i.unwrap()}return _(r)}catch{return C("Failed to inspect object. Not JSON?")}}function P(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return _({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return _({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(n=>P(n)),o=r.as_iter().find(n=>n.isErr());if(o.isSome())return o.unwrap();let i=r.as_iter().map(n=>n.unwrap()).toArray();return _({__serializer_tag:"array",__serializer_value:i})}else if(e instanceof Map){let r=[...e.entries()].map(n=>P(n)),o=r.as_iter().find(n=>n.isErr());if(o.isSome())return o.unwrap();let i=r.as_iter().map(n=>n.unwrap()).toArray();return _({__serializer_tag:"map",__serializer_value:i})}else if(e instanceof Set){let r=[...e.values()].map(n=>P(n)),o=r.as_iter().find(n=>n.isErr());if(o.isSome())return o.unwrap();let i=r.as_iter().map(n=>n.unwrap()).toArray();return _({__serializer_tag:"set",__serializer_value:i})}else if(H.isResult(e))if(e.isOk()){let r=e.unwrap(),o=P(r);return o.isErr()?o:_({__serializer_tag:"result_ok",__serializer_value:o.unwrap()})}else{let r=e.unwrapErr(),o=P(r);return o.isErr()?o:_({__serializer_tag:"result_err",__serializer_value:o.unwrap()})}else if(F.isOption(e))if(e.isSome()){let r=e.unwrap(),o=P(r);return o.isErr()?o:_({__serializer_tag:"option_some",__serializer_value:o.unwrap()})}else return _({__serializer_tag:"option_none"});else if(t==="object"){let r={},o=e;for(let i of Object.keys(e)){let n=o[i],a=P(n);if(a.isErr())continue;let s=a.unwrap();r[i]=s}return _(r)}else return C("Unsupported value")}var z=/.^/,me={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:z,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:z,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:z,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:z,defacto_container:"Mp4"}},ze={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:z,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:z,defacto_container:"Mp4"}},ee=E(function*(){for(let e of Object.keys(me))yield me[e]}),Je=E(function*(){for(let e of Object.keys(ze))yield ze[e]});function pe(e){return typeof e=="string"&&e in me?A(e):d}var te={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:ee().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:Je().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:A("MP3"),video:A("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:A("AAC"),video:d},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:A("FLAC"),video:d},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:A("MP3"),video:A("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:d,video:d},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:A("Wav"),video:d},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:d,video:d},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},fe=E(function*(){for(let e of Object.keys(te))yield e}),Qe=E(function*(){for(let e of fe())yield te[e]});function ge(e){return typeof e=="string"&&e in te?A(e):d}function _e(e){return te[e]}var Ue={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};function We(e,t){let r=parseInt(e),o=parseInt(t);return r<o}function Be(e,t){let r=parseInt(e),o=parseInt(t);return r>o}var re=E(function*(){for(let e of Object.keys(Ue))yield e}),pr=E(function*(){for(let e of re())yield Ue[e]});function L(e){if(typeof e=="string")return re().find(t=>t==e);if(typeof e=="number"){let t=e.toString();return L(t)}return d}function ye(){return{prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"4320",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:[]}}function Ge(e){return P(e).unwrap()}function Ke(e){let t=V(e).unwrapOr({}),r=ye(),o=ge(t.container).unwrapOr(r.container),i=pe(t.video_codec).unwrapOr(r.video_codec),n=L(t.best_video_quality).unwrapOr(r.best_video_quality),a=L(t.lowest_video_quality).unwrapOr(r.lowest_video_quality),s;if("prefered_video_quality"in t){let w=L(t.prefered_video_quality);w.isSome()&&(s=w.unwrap())}let p=r.max_variants;if(typeof t.max_variants=="number"){let w=t.max_variants;Number.isInteger(w)&&w<=11&&w>0&&(p=w)}let l=r.prefer_60fps;typeof t.prefer_60fps=="boolean"&&(l=t.prefer_60fps);let m=r.ignore_low_quality_hits;typeof t.ignore_low_quality_hits=="boolean"&&(m=t.ignore_low_quality_hits);let f=[];if(Array.isArray(t.ignored_containers))for(let w of t.ignored_containers){let x=ge(w);x.isSome()&&f.push(x.unwrap())}let M=[];if(Array.isArray(t.ignored_video_codecs))for(let w of t.ignored_video_codecs){let x=pe(w);x.isSome()&&M.push(x.unwrap())}let v={prefer_60fps:l,ignore_low_quality_hits:m,container:o,max_variants:p,video_codec:i,lowest_video_quality:a,best_video_quality:n,ignored_containers:f,ignored_video_codecs:M};return typeof s<"u"&&(v.prefered_video_quality=s),v}var gt=ae(Y(),1);function Ae(e,t){if(e==null||t===null||t===void 0)return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Function||e instanceof RegExp)return e===t;if(e===t||e.valueOf()===t.valueOf())return!0;if(Array.isArray(e)&&e.length!==t.length||e instanceof Date||!(e instanceof Object)||!(t instanceof Object))return!1;let r=Object.keys(e),o=Object.keys(t).every(n=>r.indexOf(n)!==-1),i=r.every(n=>Ae(e[n],t[n]));return o&&i}async function Ye(e,t){let r=t;e.hooks&&(r=e.hooks.setter(t)),await G.storage[e.where].set({[e.name]:r})}async function he(e){let t=await G.storage[e.where].get(e.name);if(e.name in t){let r=t[e.name];return e.hooks?e.hooks.getter(r,e):r}return e.default()}async function Ze(e){await G.storage[e.where].remove(e.name)}function Xe(e,t){G.storage[e.where].onChanged.addListener(r=>{let o=r[e.name];if(o){if(Ae(o.oldValue,o.newValue))return;typeof o.newValue>"u"?t(e.default()):e.hooks?t(e.hooks.getter(o.newValue,e)):t(o.newValue)}})}var K={name:"media_user_pref",where:"local",default:()=>ye(),hooks:{setter:e=>Ge(e),getter:e=>Ke(e)}};{await new Promise(e=>{document.readyState=="loading"?window.addEventListener("DOMContentLoaded",()=>e()):e()}),await T.custom_strings_ready,document.title=(0,T.getMessage)("mup_page_title"),document.documentElement.setAttribute("channel",ke);for(let e of Array.from(document.querySelectorAll("[data-i18n]")))e.textContent=(0,T.getMessage)(e.dataset.i18n)}var we=null,_t=()=>{we&&clearTimeout(we);let e=document.querySelector("#saved-toaster");e.classList.remove("hidden"),we=window.setTimeout(()=>{e.classList.add("hidden")},1e3)},yt=e=>{let t="",r="";r+=`
    <div>
      <label for="max-variant-input">${(0,T.getMessage)("mup_max_variants")}:</label>
      <input type="number" step="1" size="3" id="max-variant-input" min="1" max="10" @value="${e.max_variants}"/>
      <br><em>${(0,T.getMessage)("mup_max_variants_help")}</em>
    </div>`,r+=`
    <div>
      <label for="prefer-60fps-input">${(0,T.getMessage)("mup_prefer_60fps")}:</label>
      <input type="checkbox" id="prefer-60fps-input" @checked="${e.prefer_60fps}"/>
    </div>`,r+=`
    <div>
      <label for="ignore-low-quality-hits-input">${(0,T.getMessage)("mup_ignore_low_quality")}:</label>
      <input type="checkbox" id="ignore-low-quality-hits-input" @checked="${e.ignore_low_quality_hits}"/>
      <br><em>${(0,T.getMessage)("mup_ignore_low_quality_help")}</em>
    </div>`;{let o=Qe().filter(i=>i.supported_video_codecs.length>0);for(let i of o){let n=i.name==e.container?"selected":"";t+=`<option value="${i.name}" ${n}>${i.name}</option>`}r+=`
    <div>
      <label for="container-select">${(0,T.getMessage)("mup_prefered_container")}:</label>
      <select id="container-select" value="${e.container}">
        ${t}
      </select>
    </div>`}{let o="",i=_e(e.container);for(let n of i.supported_video_codecs){let a=e.video_codec==n?"selected":"";o+=`<option value="${n}" ${a}>${n}</option>`}r+=`
    <div>
      <label for="codec-select-${e.container}">${(0,T.getMessage)("mup_prefered_video_codecs")}:</label>
      <select class="codec-select" id="codec-select-${e.container}" value="${e.video_codec}">
        ${o}
      </select>
    </div>`}{let o="",i="";for(let n of re()){let a=e.lowest_video_quality==n?"selected":"",s=We(n,e.best_video_quality)?"":"disabled";o=`<option value="${n}" ${s} ${a}>${n}p</option>`+o,a=e.best_video_quality==n?"selected":"",s=Be(n,e.lowest_video_quality)?"":"disabled",i=`<option value="${n}" ${s} ${a}>${n}p</option>`+i}r+=`
    <div>
      <label for="best-video-quality-select">${(0,T.getMessage)("mup_best_video_quality")}:</label>
      <select id="best-video-quality-select" value="${e.best_video_quality}">
         ${i}
      </select>
    </div>
    <div>
      <label for="lowest-video-quality-select">${(0,T.getMessage)("mup_lowest_video_quality")}:</label>
      <select id="lowest-video-quality-select" value="${e.lowest_video_quality}">
        ${o}
      </select>
    </div>`}{let o=fe().map(n=>{let a=e.ignored_containers.as_iter().any(s=>s==n);return`
        <div>
          <input type="checkbox" data-container="${n}" id="ignored_container_${n}" @checked="${a}"/>
          <label for="ignored_container_${n}">${n}</label>
        </div>`}).toArray().join(""),i=ee().map(n=>{let a=e.ignored_video_codecs.as_iter().any(s=>s==n.name);return`
        <div>
          <input type="checkbox" data-video-codec="${n.name}" id="ignored_codec_${n.name}" @checked="${a}"/>
          <label for="ignored_codec_${n.name}">${n.name}</label>
        </div>`}).toArray().join("");r+=`
      <vbox>
        <fieldset id="ignored-containers-fieldset">
          <legend>${(0,T.getMessage)("mup_ignored_containers")}:</legend>
          ${o}
        </fieldset>
        <fieldset id="ignored-video-codecs-fieldset">
          <legend>${(0,T.getMessage)("mup_ignored_video_codecs")}:</legend>
          ${i}
        </fieldset>
      </vbox>
    `}De("section",r)},xe=class{constructor(){Xe(K,()=>{_t(),this.read_from_storage()}),this.is_ready_p=this.read_from_storage()}async is_ready(){return await this.is_ready_p,this}async read_from_storage(){this.state=await he(K),yt(this.state)}async reset(){await Ze(K)}async write_to_storage(){return Ye(K,this.state)}async set_max_variants(t){Number.isInteger(t)&&t<=10&&t>0&&(this.state.max_variants=t,await this.write_to_storage())}async set_prefer_60fps(t){this.state.prefer_60fps=t,await this.write_to_storage()}async set_ignore_low_quality_hits(t){this.state.ignore_low_quality_hits=t,await this.write_to_storage()}async set_lowest_video_quality(t){this.state.lowest_video_quality=t,await this.write_to_storage()}async set_best_video_quality(t){this.state.best_video_quality=t,await this.write_to_storage()}async set_video_codec(t){this.state.video_codec=t,await this.write_to_storage()}async set_ignored_containers(t){this.state.ignored_containers=t,await this.write_to_storage()}async set_ignored_video_codecs(t){this.state.ignored_video_codecs=t,await this.write_to_storage()}async set_container(t){if(this.state.container==t)return;this.state.container=t;let o=_e(t).supported_video_codecs[0];if(o)this.state.video_codec=o;else throw new Error("Unexpected container.");await this.write_to_storage()}},j=await new xe().is_ready();{let e=function(t){let r=t.target;if(r){let o=r.closest("#container-select"),i=r.closest(".codec-select"),n=r.closest("#lowest-video-quality-select"),a=r.closest("#best-video-quality-select"),s=r.closest("#ignored-containers-fieldset"),p=r.closest("#ignored-video-codecs-fieldset"),l=r.closest("#max-variant-input"),m=r.closest("#prefer-60fps-input"),f=r.closest("#ignore-low-quality-hits-input");if(o)j.set_container(o.value);else if(i)j.set_video_codec(i.value);else if(n)j.set_lowest_video_quality(L(n.value).unwrap());else if(a)j.set_best_video_quality(L(a.value).unwrap());else if(s){let v=Array.from(s.querySelectorAll("input")).as_iter().filterMap(w=>w.checked?A(w.dataset.container):d).toArray();j.set_ignored_containers(v)}else if(p){let v=Array.from(p.querySelectorAll("input")).as_iter().filterMap(w=>w.checked?A(w.dataset.videoCodec):d).toArray();j.set_ignored_video_codecs(v)}else l?j.set_max_variants(parseInt(l.value)):m?j.set_prefer_60fps(m.checked):f&&j.set_ignore_low_quality_hits(f.checked)}};ht=e,window.addEventListener("input",e),window.addEventListener("change",e),window.addEventListener("click",t=>{let r=t.target;r&&r.closest("#reset_button")&&j.reset()})}var ht;
/*! Bundled license information:

reefjs/dist/reef.es.min.js:
  (*! reef v13.0.2 | (c) 2023 Chris Ferdinandi | MIT License | http://github.com/cferdinandi/reef *)
*/
