{"Bytes": {"message": "$1 bajtów"}, "GB": {"message": "$1 GB"}, "KB": {"message": "$1 KB"}, "MB": {"message": "$1 MB"}, "__MSG_appDesc_": {"message": "Pomocnik Pobierania Wideo"}, "about": {"message": "Informacje"}, "about_alpha_extra7_fx": {"message": "Z powodu wewnętrznych zmian technicznych w Firefoksie dodatek musiał zostać całkowicie przepisany. Odczekaj kilka tygodni, aby odzyskać wszystkie funkcje poprzednich wersji."}, "about_alpha_intro": {"message": "To jest wers<PERSON> al<PERSON>."}, "about_beta_intro": {"message": "To jest wersja beta."}, "about_chrome_licenses": {"message": "O licencji Chrome"}, "about_qr": {"message": "Plik został wygenerowany"}, "about_vdh": {"message": "Video DownloadHelper – informacje"}, "action_abort_description": {"message": "Przerwij trwające działanie"}, "action_abort_title": {"message": "Przerwij"}, "action_as_default": {"message": "<PERSON><PERSON><PERSON><PERSON> to działanie jako domy<PERSON>lne"}, "action_avplay_description": {"message": "Odtwarza wideo w natywnym odtwarzaczu konwertera"}, "action_avplay_title": {"message": "Odtwórz"}, "action_blacklist_description": {"message": "Filmy pochodzące z lub serwowane przez wybrane domeny będą pomijane"}, "action_blacklist_title": {"message": "Dodaj do listy zabronionych"}, "action_bulkdownload_description": {"message": "Pobierz zaznaczone"}, "action_bulkdownload_title": {"message": "<PERSON><PERSON><PERSON> p<PERSON>"}, "action_bulkdownloadconvert_description": {"message": "Pobierz masowo oraz konwertuj wybrane pliki"}, "action_bulkdownloadconvert_title": {"message": "Masowe pobieranie i konwersja"}, "action_copyurl_description": {"message": "Kopiuj do schowka adres URL pliku multimedialnego"}, "action_copyurl_title": {"message": "Kopiuj URL"}, "action_deletehit_description": {"message": "Usuń utwór z bieżącej listy"}, "action_deletehit_title": {"message": "Usuń"}, "action_details_description": {"message": "Wyświetl szczegóły dotyczące tego utworu"}, "action_details_title": {"message": "Szczegóły"}, "action_download_description": {"message": "Pobierz plik na dysk twardy"}, "action_download_title": {"message": "<PERSON><PERSON><PERSON>"}, "action_downloadconvert_description": {"message": "Pobierz plik multimedialny i konwertuj go na inny format"}, "action_downloadconvert_title": {"message": "Pobieranie i konwersja"}, "action_openlocalcontainer_description": {"message": "Otwórz ścieżkę zapisu na komputerze"}, "action_openlocalcontainer_title": {"message": "Otwórz folder"}, "action_openlocalfile_description": {"message": "Otwórz lokalny plik multimedialny"}, "action_openlocalfile_title": {"message": "Otwórz multimedium"}, "action_pin_description": {"message": "Przypnij utwór"}, "action_pin_title": {"message": "Przypnij"}, "action_quickdownload_description": {"message": "Pobierz bez pytania o folder docelowy"}, "action_quickdownload_title": {"message": "Szybkie pobieranie"}, "action_stop_description": {"message": "Przerwij pobieranie"}, "action_stop_title": {"message": "Przerwij"}, "adaptative": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "add_to_blacklist": {"message": "Dodaj do listy zabronionych"}, "add_to_blacklist_help": {"message": "Filmy pochodzące z lub serwowane przez wybrane domeny będą pomijane"}, "advanced": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "aggregating": {"message": "<PERSON><PERSON><PERSON><PERSON>…"}, "analyze_page": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "appDesc": {"message": "Ściągnij wideo ze strony"}, "appName": {"message": "Video DownloadHelper"}, "appearance": {"message": "Wygląd"}, "audio_only": {"message": "Tylko audio"}, "behavior": {"message": "Zachowanie"}, "blacklist": {"message": "Lista zabronionych"}, "blacklist_add_domain": {"message": "Dodaj domenę do czarnej listy"}, "blacklist_add_placeholder": {"message": "Domeny na czarnej liście"}, "blacklist_edit_descr": {"message": "Czarna lista pozwala na ignorowanie plików multimedialnych pochodzących z niektórych domen"}, "blacklist_empty": {"message": "Brak domen na czarnej liście"}, "browser_info": {"message": "Przeglądarka $1 $2 $3"}, "browser_locale": {"message": "Lokalizacja przeglądarki: $1"}, "build_options": {"message": "<PERSON><PERSON><PERSON> budowy: $1"}, "built_on": {"message": "Budowa w $1"}, "bulk_in_progress": {"message": "Video DownloadHelper Masowe pobieranie w toku - nie zamykaj tej zakładki, zostanie ona zamknięta automatycznie"}, "bulk_n_videos": {"message": "wideo $1"}, "cancel": {"message": "<PERSON><PERSON><PERSON>"}, "change": {"message": "Zmień"}, "chrome_basic_mode": {"message": "Chrome tryb podstawowy (aktualizacja wskazana)"}, "chrome_inapp_descr_premium_lifetime": {"message": "Status Premium bez limitu czasowego"}, "chrome_inapp_descr_premium_monthly": {"message": "Status Premium z miesięcznej subskrypcji"}, "chrome_inapp_descr_premium_yearly": {"message": "Status Premium z rocznej subskrypcji"}, "chrome_inapp_no_subs": {"message": "Notatka: Chrome zapłata została odrzucona przez Google, przedpłata niedostępna"}, "chrome_inapp_not_avail": {"message": "Niedostępne"}, "chrome_inapp_premium_lifetime": {"message": "Dożywotnie Premium"}, "chrome_inapp_premium_monthly": {"message": "Miesięczna subskrypcja Premium"}, "chrome_inapp_premium_yearly": {"message": "Roczna subskrypcja Premium"}, "chrome_install_firefox": {"message": "Zainstaluj Firefoks"}, "chrome_install_fx_vdh": {"message": "Video DownloadHelper dla Firefoxa"}, "chrome_license_webstore_accepted": {"message": "Aktywna licencja dla Chrome Webstore"}, "chrome_licensing": {"message": "Licencje Chrome"}, "chrome_noyt_text": {"message": "<PERSON><PERSON><PERSON>, Chrome Web Store nie pozwala na rozszerzenia umożliwiające pobieranie filmów z serwisu Youtube, więc byliśmy zmuszeni usunąć tę funkcję."}, "chrome_noyt_text2": {"message": "Mo<PERSON><PERSON><PERSON> z Video DownloadHelper dla przeglądarki Firefox, aby pob<PERSON> filmy z serwisu Youtube."}, "chrome_noyt_text3": {"message": "Niestety, Chrome Web Store nie pozwala na zamieszczanie rozszerzeń pozwalajacych na pobieranie plików wideo z serwisu YouTube. Nie możemy więc dołączyć tej funkcji do tego rozszerzenia w wersji dla przeglądarki Chrome."}, "chrome_premium_audio": {"message": "Generowanie plików audio jest dostępne wyłącznie w wersji Premium"}, "chrome_premium_check_error": {"message": "Błąd sprawdzania statusu Premium"}, "chrome_premium_hls": {"message": "Bez statusu Premium, pobieranie HLS może zostać przeprowadzone tylko $1 minut po poprzednim"}, "chrome_premium_mode": {"message": "Chrome Premium"}, "chrome_premium_need_sign": {"message": "Musz<PERSON>z się zalogować do Chrome, aby k<PERSON> z funkcji Premium."}, "chrome_premium_not_signed": {"message": "Niezalogowany do Chrome"}, "chrome_premium_recheck": {"message": "Sprawdź ponownie status Premium"}, "chrome_premium_required": {"message": "Status Premium wymagany"}, "chrome_premium_source": {"message": "Jesteś użytkownikiem premium poprzez $1"}, "chrome_product_intro": {"message": "Możesz uzyskać Premium korzystając z dowolnej z poniższych opcji:"}, "chrome_req_review": {"message": "Alternaty<PERSON><PERSON>, czy mógłbys napisać miłą recenzję w Chrome WebStore?"}, "chrome_signing_in": {"message": "Logowanie do Chrome"}, "chrome_verif_premium": {"message": "Sprawdzanie statusu Premium"}, "chrome_verif_premium_error": {"message": "Interfejs API płatności w aplikacji jest niedostępny"}, "chrome_warning_yt": {"message": "<PERSON><PERSON><PERSON><PERSON>, rozszerzenie dla Chrome oraz YouTube"}, "clear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "clear_hits": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> utwory"}, "clear_logs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane"}, "coapp": {"message": "Aplikacja firmowa"}, "coapp_error": {"message": "Ponownie sprawdzam aplikację firmową"}, "coapp_found": {"message": "Znaleziono aplikację firmową"}, "coapp_install": {"message": "Zainstaluj aplikacje <PERSON>ową"}, "coapp_installed": {"message": "Aplikacja firmowa zainstalowana"}, "coapp_latest_version": {"message": "Ostatnia dostępna wersja to $1"}, "coapp_not_installed": {"message": "Aplikacja firmowa nie zainstalowana"}, "coapp_outdated": {"message": "Aplikacja firmowa nieaktualna - pro<PERSON><PERSON> z<PERSON>"}, "coapp_outofdate": {"message": "Aplikacja firmowa wymaga aktualizacji"}, "coapp_outofdate_text": {"message": "Używasz aplikacji firmowej w wersji $1 lecz potrzebujesz werszji $2"}, "coapp_path": {"message": "Scieżka aplikacji firmowej:"}, "coapp_recheck": {"message": "Sprawdź ponownie"}, "coapp_required": {"message": "Wymagana firmowa aplikacja"}, "coapp_required_text": {"message": "Ta operacja wymaga aplikacji firmowej aby zakończyć."}, "coapp_shell": {"message": "Powłoka aplikacji firmowej"}, "coapp_unchecked": {"message": "Weryfikacja aplikacji firmowej..."}, "coapp_update": {"message": "Aktualizacja aplikacji firmowej"}, "collecting": {"message": "Zbieranie..."}, "confirmation_required": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "congratulations": {"message": "G<PERSON>ulacje!"}, "continue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "convconf_2passes": {"message": "2 przebiegi"}, "convconf_ac": {"message": "Kanały audio"}, "convconf_acnone": {"message": "Brak"}, "convconf_acodec": {"message": "Kodek audio"}, "convconf_aspect": {"message": "Propor<PERSON><PERSON> o<PERSON>zu"}, "convconf_audiobitrate": {"message": "Bitrate audio"}, "convconf_audiofreq": {"message": "Częstotliwość audio"}, "convconf_audioonly": {"message": "Tylko audio"}, "convconf_bitrate": {"message": "Bitrate"}, "convconf_container": {"message": "Format"}, "convconf_duplicate": {"message": "Duplikat"}, "convconf_ext": {"message": "Rozszerzenie pliku wyjściowego"}, "convconf_extra": {"message": "Dodatkowe parametry"}, "convconf_level": {"message": "Poziom"}, "convconf_mono": {"message": "Mono"}, "convconf_new": {"message": "Nowy"}, "convconf_preset": {"message": "Ustawienia domyślne"}, "convconf_profilev": {"message": "Profil <PERSON>o"}, "convconf_rate": {"message": "Klatek na sekundę"}, "convconf_readonly": {"message": "Ta domyślna konfiguracja jest tylko do odczytu. <PERSON><PERSON><PERSON> j<PERSON>, by w<PERSON><PERSON><PERSON><PERSON><PERSON>."}, "convconf_remove": {"message": "Usuń"}, "convconf_reset": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "convconf_reset_confirm": {"message": "Czynność ta spowoduje usunięcie wszystkich dokonanych zmian"}, "convconf_save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "convconf_size": {"message": "<PERSON>oz<PERSON><PERSON>"}, "convconf_stereo": {"message": "Stereo"}, "convconf_target": {"message": "Format docelowy"}, "convconf_tune": {"message": "St<PERSON>jenie"}, "convconf_vcodec": {"message": "<PERSON><PERSON>"}, "convconf_videobitrate": {"message": "Bitrate wideo"}, "conversion_create_rule": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "conversion_outputs": {"message": "Wyjścia konwersji"}, "conversion_rules": {"message": "<PERSON><PERSON><PERSON> kon<PERSON>ji"}, "conversion_update_rule": {"message": "Zasady aktualizacji"}, "convert": {"message": "Konwertuj"}, "convert_local_files": {"message": "Konwertuj lokalne pliki"}, "converter_needed_aggregate": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> strumienie wideo i audio, w systemie musi być zainstalowany konwerter."}, "converter_needed_aggregate_why": {"message": "Dlaczego musi być zainstalowany konwerter?"}, "converter_needs_reg": {"message": "<PERSON><PERSON><PERSON><PERSON> jest rejestracja"}, "converter_queued": {"message": "<PERSON><PERSON><PERSON>a konwertera..."}, "converter_reg_audio": {"message": "Zażądano reguły automatycznej konwersji generującej tylko plik multimedialny audio. To działanie wymagana zarejestrowanego konwertera."}, "converting": {"message": "Konwertowanie…"}, "convrule_convert": {"message": "Konwertowanie"}, "convrule_domain": {"message": "<PERSON><PERSON>"}, "convrule_extension": {"message": "Rozszerzenie"}, "convrule_format": {"message": "dla formatu $1"}, "convrule_from_domain": {"message": "dla domeny $1"}, "convrule_no_convert": {"message": "<PERSON><PERSON> k<PERSON>"}, "convrule_output_format": {"message": "Formaty wyjścia"}, "convrule_refresh_formats": {"message": "Odśwież formaty wyjścia"}, "convrule_with_ext": {"message": "dla rozszerzenia $1'"}, "convrules_add_rule": {"message": "Stwórz nową zasadę konwersji"}, "convrules_edit_descr": {"message": "Zasady konwersji pozwalają na konwersję multimediów automatycznie po pobraniu"}, "convrules_empty": {"message": "Brak zasad konwersji"}, "copy_of": {"message": "Kopia z $1"}, "corrupted_media_file": {"message": "Nie można pobrać informacji na temat multimediów '$1' z pliku '$2'. Plik może być uszko<PERSON>zony."}, "create": {"message": "Stwórz"}, "custom_output": {"message": "Własny format pliku wyjściowego"}, "dash_streaming": {"message": "Strumieniowanie DASH"}, "default": {"message": "Domy<PERSON><PERSON><PERSON>"}, "details_parenthesis": {"message": "(Szczegóły)"}, "dev_build": {"message": "Kompilacja dla programistów"}, "directory_not_exist": {"message": "Nieistniejący folder"}, "directory_not_exist_body": {"message": "Folder '$1' nie is<PERSON><PERSON><PERSON>, wciśnij OK aby go stworzyć ?"}, "dlconv_download_and_convert": {"message": "Pobieranie i konwersja"}, "dlconv_output_details": {"message": "Skonfiguruj dane w<PERSON>cio<PERSON>"}, "donate": {"message": "<PERSON><PERSON><PERSON>"}, "donate_vdh": {"message": "Video DownloadHelper – pomoc"}, "download_error": {"message": "Błąd pobierania"}, "download_method": {"message": "Metoda pob<PERSON>nia"}, "download_method_not_again": {"message": "Używaj tej metody domyślnie następnym razem"}, "download_modes1": {"message": "Pobieraj przy pomocy preferowanej przeglądarki www"}, "download_modes2": {"message": "Z powodów technicznych, pobieranie przy pomocy przeglądarki może spowodować, że pobieranie zostanie odrzucone przez serwer hostujący wideo i nie jest możliwe zdefiniowanie innego domyślnego folderu docelowego."}, "download_with_browser": {"message": "Użyj przeglądarki"}, "download_with_coapp": {"message": "Użyj Firmowej Aplikacji"}, "downloading": {"message": "<PERSON><PERSON><PERSON><PERSON>…"}, "edge_req_review": {"message": "Może nie miałbyś nic przeciwko temu, aby w zamian napisać recenzję w sklepie z rozszerzeniami dla przeglądarki Microsoft Edge?"}, "error": {"message": "Błąd"}, "error_not_directory": {"message": "'$1' istnieje lecz nie jest folderem"}, "errors": {"message": "Błędy"}, "exit_natmsgsh": {"message": "Wyjdź z aplikacji firmowej"}, "explain_qr1": {"message": "Będzie ukazywała się informacja, że skonwertowany film w rogu ekranu będzie zawierał znak wodny."}, "explain_qr2": {"message": "Jest to spo<PERSON><PERSON><PERSON><PERSON> tym, że funkcja konwersji nie została zarejestrowana."}, "export": {"message": "Export<PERSON>j"}, "failed_aggregating": {"message": "Nie udało się zsumować „$1”"}, "failed_converting": {"message": "Konwersja „$1” się nie powiodła"}, "failed_getting_info": {"message": "Nie udało się pobrać informacji z „$1”"}, "failed_opening_directory": {"message": "<PERSON>e udało się otworzyć folderu"}, "failed_playing_file": {"message": "Nie można odtworzyć pliku"}, "file_dialog_date": {"message": "Data"}, "file_dialog_name": {"message": "Nazwa"}, "file_dialog_size": {"message": "Rozmiar"}, "file_generated": {"message": "Plik „$1” został wygenerowany."}, "file_ready": {"message": "„$1” jest gotowy"}, "finalizing": {"message": "Finalizowanie…"}, "from_domain": {"message": "Z $1"}, "gallery": {"message": "Galeria"}, "gallery_files_types": {"message": "$1 plików"}, "gallery_from_domain": {"message": "Galeria z $1"}, "gallery_links_from_domain": {"message": "Odnośniki z $1"}, "general": {"message": "Ogólne"}, "get_conversion_license": {"message": "Uzyskaj licencję konwersji"}, "help_translating": {"message": "<PERSON><PERSON><PERSON><PERSON> prz<PERSON>ł<PERSON>"}, "hit_details": {"message": "Szczegóły trafienia"}, "hit_go_to_tab": {"message": "Przejdź do karty"}, "hls_streaming": {"message": "Strumieniowanie HLS"}, "homepage": {"message": "Strona domowa"}, "import": {"message": "Import<PERSON>j"}, "import_invalid_format": {"message": "Nieznany format"}, "in_current_tab": {"message": "<PERSON> a<PERSON>ywnej karcie"}, "in_other_tab": {"message": "W innych kartach"}, "lic_mismatch1": {"message": "Licencja jest ważna dla $1 ale nie podano wersji rozszerzeń przeglądarki"}, "lic_mismatch2": {"message": "Licencja jest ważna dla $1 ale wersja rozszerzenia to $2"}, "lic_not_needed_linux": {"message": "<PERSON><PERSON> wkład w Linuxa: licencja nie jest wymagana"}, "lic_status_accepted": {"message": "Licencja zaakceptowana"}, "lic_status_blocked": {"message": "Licencja zablokowana"}, "lic_status_error": {"message": "Błąd licencji"}, "lic_status_locked": {"message": "Licencja zamknięta"}, "lic_status_mismatch": {"message": "Niepoprawna licencja lub przeglądarka"}, "lic_status_nocoapp": {"message": "Nie można zweryfikować licencji"}, "lic_status_unneeded": {"message": "Licencja niepotrzebna"}, "lic_status_unset": {"message": "Brak licencji"}, "lic_status_unverified": {"message": "Licencja nie zweryfikowana"}, "lic_status_verifying": {"message": "Weryfikacja licencji..."}, "license": {"message": "Licencja"}, "license_key": {"message": "Klucz licencji"}, "licensing": {"message": "Licencje"}, "logs": {"message": "Dzienniki zdarzeń"}, "media": {"message": "Media"}, "merge_error": {"message": "Błąd łączenia"}, "merge_local_files": {"message": "Połącz lokalne pliki dźwiękowe i wideo"}, "more": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>…"}, "network_error_no_response": {"message": "Błąd sieci - nie odpowiada"}, "network_error_status": {"message": "Błąd sieci - status $1"}, "new_sub_directory": {"message": "<PERSON><PERSON><PERSON><PERSON> podfolder"}, "next": {"message": "Następny"}, "no": {"message": "<PERSON><PERSON>"}, "no_audio_in_file": {"message": "Plik $1 nie zawiera strumienia dźwiękowego"}, "no_coapp_license_unverified": {"message": "Licencja nie została zweryfikowana ponieważ aplikacja firmowa nie jest zainstalowana"}, "no_license_registered": {"message": "Brak zarejestrowanej licencji"}, "no_media_current_tab": {"message": "Brak multimediów do przetwarzania na bieżącej karcie"}, "no_media_to_process": {"message": "Brak multimediów do przetwarzania"}, "no_media_to_process_descr": {"message": "Kliknij odtwarzanie, aby pomóc w rozpoznaniu plików…"}, "no_such_hit": {"message": "Nie znaleziono"}, "no_validate_without_coapp": {"message": "Aplikacja firmowa musi być zainstalowana wraz z licencją"}, "no_video_in_file": {"message": "Plik $1 nie zawiera strumienia wideo"}, "not_again_3months": {"message": "Nie przypominaj o tym przez 3 miesiące"}, "not_see_again": {"message": "<PERSON><PERSON> wyświetlaj tej informacji ponownie"}, "number_type": {"message": "$1 $2"}, "ok": {"message": "OK"}, "orphan": {"message": "Osierocone"}, "output_configuration": {"message": "Ustawienia wyjścia"}, "overwrite_file": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> plik '$1' ?"}, "per_month": {"message": "/ miesiąc"}, "per_year": {"message": "/ rok"}, "pinned": {"message": "Przypięte"}, "platform": {"message": "Platforma"}, "platform_info": {"message": "Platforma $1 $2"}, "powered_by_weh": {"message": "Wspierane przez Weh"}, "preferences": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "prod_build": {"message": "Budowa produktu"}, "quality_medium": {"message": "Średnia"}, "quality_small": {"message": "<PERSON><PERSON>"}, "queued": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…"}, "recheck_license": {"message": "Sprawd<PERSON>ie ponownie licencji"}, "register_converter": {"message": "Zarejestruj konwerter"}, "register_existing_license": {"message": "Zarejestruj istniejącą licencję"}, "registered_email": {"message": "E-mail"}, "registered_key": {"message": "<PERSON><PERSON>cz"}, "reload_addon": {"message": "Wczytaj ponownie wtyczkę"}, "reload_addon_confirm": {"message": "<PERSON><PERSON><PERSON>, że ch<PERSON>z wczytać ponownie wtyczkę?"}, "req_donate": {"message": "Wspomóż dalszy rozwój dodatku poprzez dokonanie niewielkiej dotacji."}, "req_locale": {"message": "A może pomóż tłumaczyć dodatek na '$1' (brakuje $2 linii)?"}, "req_review": {"message": "<PERSON><PERSON> nap<PERSON>z wspaniałą recenzję na witrynie dodatków Mozilli."}, "req_review_link": {"message": "Napisz opinię o Video DownloadHelper"}, "reset_settings": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "running": {"message": "Uruchamianie"}, "save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "save_as": {"message": "Zapisz do..."}, "save_file_as": {"message": "Zapisz plik do..."}, "select_audio_file_to_merge": {"message": "Wybierz plik zawierajacy strumień dźwiękowy"}, "select_files_to_convert": {"message": "Konwertuj lokalne pliki"}, "select_output_config": {"message": "<PERSON><PERSON><PERSON>rz ustawienia wyjścia..."}, "select_output_directory": {"message": "Folder wyjścia"}, "select_video_file_to_merge": {"message": "Wybierz plik zawierajacy strumień wideo"}, "selected_media": {"message": "Wybrano plik"}, "settings": {"message": "Ustawienia"}, "smartname_add_domain": {"message": "Dodaj zasadę nazywania plików"}, "smartname_create_rule": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "smartname_define": {"message": "Zdefiniuj zasadę nazywania plików"}, "smartname_edit_descr": {"message": "Edytuj zasadę nazywania plików odnośnie nazw oraz nazwy hosta"}, "smartname_empty": {"message": "Brak zasad nazywania plików"}, "smartname_update_rule": {"message": "Zaktuali<PERSON><PERSON>"}, "smartnamer_delay": {"message": "Opóźnienie nazewnictwa przechwytywania (ms)"}, "smartnamer_domain": {"message": "<PERSON><PERSON>"}, "smartnamer_get_name_from_header_url": {"message": "Pobierz nazwę z nagłówka dokumentu/adresu URL"}, "smartnamer_get_name_from_page_content": {"message": "Pobierz nazwę z treści strony"}, "smartnamer_get_name_from_page_title": {"message": "Pobierz nazwę z tytułu strony"}, "smartnamer_get_obfuscated_name": {"message": "Użyj zamaskowanej nazwy"}, "smartnamer_regexp": {"message": "Wyrażenie regularne"}, "smartnamer_selected_text": {"message": "Zaznaczony tekst"}, "smartnamer_xpath_expr": {"message": "Wyrażenie XPath"}, "smartnaming_rule": {"message": "Zasada nawigac<PERSON>"}, "smartnaming_rules": {"message": "<PERSON><PERSON><PERSON>"}, "sub_directory_name": {"message": "Nazwa podfolderu"}, "support_forum": {"message": "Forum wsparcia"}, "supported_sites": {"message": "Obsługiwane witryny"}, "tbsn_quality_hd": {"message": "Średnia jakość"}, "tbsn_quality_sd": {"message": "<PERSON><PERSON> jak<PERSON>"}, "tell_me_more": {"message": "Powiedz więcej o tym"}, "title": {"message": "Pomocnik Pobierania Wideo "}, "translation": {"message": "Tłumaczenie"}, "up": {"message": "Góra"}, "v9_about_qr": {"message": "Plik został wygenerowany"}, "v9_badge_new": {"message": "nowy"}, "v9_blacklist_glob": {"message": "<PERSON><PERSON><PERSON><PERSON> „*”, aby uzyskać szersze dopasowanie."}, "v9_checkbox_remember_action": {"message": "Zapamiętaj jako akcję <PERSON>ą"}, "v9_chrome_noyt_text2": {"message": "Mo<PERSON><PERSON><PERSON> z Video DownloadHelper dla przeglądarki Firefox, aby pob<PERSON> filmy z serwisu Youtube."}, "v9_chrome_noyt_text3": {"message": "Niestety, Chrome Web Store nie pozwala na zamieszczanie rozszerzeń pozwalajacych na pobieranie plików wideo z serwisu YouTube. Nie możemy więc dołączyć tej funkcji do tego rozszerzenia w wersji dla przeglądarki Chrome."}, "v9_chrome_premium_hls": {"message": "Bez statusu Premium, pobieranie HLS może zostać przeprowadzone tylko $1 minut po poprzednim"}, "v9_chrome_premium_required": {"message": "Status Premium wymagany"}, "v9_chrome_warning_yt": {"message": "<PERSON><PERSON><PERSON><PERSON>, rozszerzenie dla Chrome oraz YouTube"}, "v9_coapp_help": {"message": "<PERSON><PERSON><PERSON><PERSON> tutaj, a<PERSON> r<PERSON><PERSON><PERSON> swój problem."}, "v9_coapp_install": {"message": "Zainstaluj aplikacje <PERSON>ową"}, "v9_coapp_installed": {"message": "Aplikacja firmowa zainstalowana"}, "v9_coapp_not_installed": {"message": "Aplikacja firmowa nie zainstalowana"}, "v9_coapp_outdated": {"message": "Aplikacja firmowa nieaktualna - pro<PERSON><PERSON> z<PERSON>"}, "v9_coapp_recheck": {"message": "Sprawdź ponownie"}, "v9_coapp_required": {"message": "Wymagana firmowa aplikacja"}, "v9_coapp_required_text": {"message": "Ta operacja wymaga aplikacji firmowej aby zakończyć."}, "v9_coapp_unchecked": {"message": "Weryfikacja aplikacji firmowej..."}, "v9_coapp_update": {"message": "Aktualizacja aplikacji firmowej"}, "v9_converter_needs_reg": {"message": "<PERSON><PERSON><PERSON><PERSON> jest rejestracja"}, "v9_converter_reg_audio": {"message": "Zażądano reguły automatycznej konwersji generującej tylko plik multimedialny audio. To działanie wymagana zarejestrowanego konwertera."}, "v9_copy_settings_info_to_clipboard": {"message": "Skopiuj informacje do schowka"}, "v9_dialog_audio_impossible": {"message": "Ten typ multimediów nie obsługuje pobierania samego dźwięku"}, "v9_dialog_audio_impossible_title": {"message": "<PERSON>e można pobrać dźwięku"}, "v9_error": {"message": "Błąd"}, "v9_explain_qr1": {"message": "Będzie ukazywała się informacja, że skonwertowany film w rogu ekranu będzie zawierał znak wodny."}, "v9_file_ready": {"message": "„$1” jest gotowy"}, "v9_filepicker_select_download_dir": {"message": "Wybierz katalog pobierania"}, "v9_filepicker_select_file": {"message": "<PERSON><PERSON><PERSON>rz plik"}, "v9_get_conversion_license": {"message": "Uzyskaj licencję konwersji"}, "v9_lic_mismatch2": {"message": "Licencja jest ważna dla $1 ale wersja rozszerzenia to $2"}, "v9_lic_status_accepted": {"message": "Licencja zaakceptowana"}, "v9_lic_status_blocked": {"message": "Licencja zablokowana"}, "v9_lic_status_locked": {"message": "Licencja zamknięta"}, "v9_lic_status_locked2": {"message": "Licencja zablokowana (sprawdź pocztę)"}, "v9_lic_status_unset": {"message": "Brak licencji"}, "v9_lic_status_verifying": {"message": "Weryfikacja licencji..."}, "v9_menu_item_blacklist": {"message": "Czarna lista"}, "v9_menu_item_blacklist_domain": {"message": "<PERSON><PERSON>"}, "v9_menu_item_blacklist_media": {"message": "Media"}, "v9_menu_item_blacklist_page": {"message": "Strona"}, "v9_menu_item_details": {"message": "Detale"}, "v9_menu_item_download_and_convert": {"message": "Pobierz i przekonwertuj na"}, "v9_menu_item_smartnaming": {"message": "Inteligentne nazewnictwo"}, "v9_mup_max_variants": {"message": "Liczba wariantów"}, "v9_no": {"message": "<PERSON><PERSON>"}, "v9_no_license_registered": {"message": "Brak zarejestrowanej licencji"}, "v9_no_media_current_tab": {"message": "Brak multimediów do przetwarzania na bieżącej karcie"}, "v9_no_media_to_process_descr": {"message": "Kliknij odtwarzanie, aby pomóc w rozpoznaniu plików…"}, "v9_no_validate_without_coapp": {"message": "Aplikacja firmowa musi być zainstalowana wraz z licencją"}, "v9_not_see_again": {"message": "<PERSON><PERSON> wyświetlaj tej informacji ponownie"}, "v9_panel_copy_url_button_label": {"message": "Kopiuj URL"}, "v9_panel_download_as_button_label": {"message": "<PERSON><PERSON><PERSON> jako..."}, "v9_panel_download_audio_button_label": {"message": "Pobierz <PERSON>"}, "v9_panel_download_button_label": {"message": "<PERSON><PERSON><PERSON>"}, "v9_panel_downloadable_variant_no_details": {"message": "bez szczegółów"}, "v9_panel_downloaded_delete_file_tooltip": {"message": "Us<PERSON>ń plik"}, "v9_panel_downloaded_retry_tooltip": {"message": "<PERSON><PERSON>rz ponownie"}, "v9_panel_downloaded_show_dir_tooltip": {"message": "Pokaż folder pobierania"}, "v9_panel_downloading_stop": {"message": "stop"}, "v9_panel_error_coapp_failure_description": {"message": "Niestety nie udało nam się pobrać tego konkretnego nośnika. Staramy się obsługiwać jak najwięcej stron internetowych, dlatego bardzo pomocne byłoby zgłoszenie tego błędu (jest to anonimowe!)."}, "v9_panel_error_coapp_failure_title": {"message": "Pobieranie nie udane"}, "v9_panel_error_coapp_too_old_button_udpate": {"message": "Aktualizacja"}, "v9_panel_error_nocoapp_button_install": {"message": "Pobierz i zainstaluj"}, "v9_panel_error_report_button2": {"message": "Zgł<PERSON>ś"}, "v9_panel_error_reported_button": {"message": "<PERSON>gł<PERSON>zono, d<PERSON>ę<PERSON><PERSON><PERSON>!"}, "v9_panel_error_unknown_description": {"message": "Niestety dodatek napotkał nieoczekiwany błąd. <PERSON><PERSON><PERSON> by p<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> mógł zg<PERSON><PERSON> ten błąd (jest to anonimowy!)."}, "v9_panel_footer_clean_all_tooltip": {"message": "Usuń odkryte i pobrane multimedia (pliki nie są usuwane)"}, "v9_panel_footer_clean_tooltip": {"message": "Usuń odkryte multimedia"}, "v9_panel_footer_convert_local_tooltip": {"message": "Konwertuj pliki lokalne"}, "v9_panel_footer_show_in_popup_tooltip": {"message": "Pokaż w wyskakującym okienku"}, "v9_panel_footer_show_in_sidebar_tooltip": {"message": "Pokaż na pasku bocznym"}, "v9_panel_variant_menu_prefer_format": {"message": "Zawsze preferuj ten format"}, "v9_panel_variant_menu_prefer_quality": {"message": "<PERSON><PERSON><PERSON> preferuj tę jakość"}, "v9_panel_view_clean": {"message": "Pokaż przycisk Wyczyść"}, "v9_panel_view_clean_all": {"message": "Pokaż przycisk Wyczyść wszystko"}, "v9_panel_view_hide_downloaded": {"message": "Automatycznie ukryj pobrane multimedia"}, "v9_panel_view_open_settings": {"message": "Wię<PERSON>j ustawień"}, "v9_panel_view_show_all_tabs": {"message": "Pokaż wszystkie karty"}, "v9_panel_view_show_low_quality": {"message": "Pokaż multimedia o niskiej jakości"}, "v9_panel_view_sort_reverse": {"message": "Sortowanie odwrotne"}, "v9_panel_view_sort_status": {"message": "<PERSON><PERSON><PERSON><PERSON> stanu"}, "v9_reset": {"message": "Reset"}, "v9_save": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "v9_settings": {"message": "Ustawienia"}, "v9_settings_button_export": {"message": "Eksportuj ustawienia"}, "v9_settings_button_import": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "v9_settings_button_reload": {"message": "Załaduj ponownie dodatek"}, "v9_settings_button_reset": {"message": "Resetowanie ustawień"}, "v9_settings_checkbox_force_inbrowser": {"message": "<PERSON><PERSON><PERSON> to możliwe, nie korzystaj z CoApp"}, "v9_settings_checkbox_forget_on_close": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odkryte multimedia, gdy karta jest zamknięta"}, "v9_settings_checkbox_notification": {"message": "Pokaż powiadomienie po zakończeniu pobierania"}, "v9_settings_checkbox_notification_incognito": {"message": "Pokaż powiadomienia dotyczące przeglądania prywatnego"}, "v9_settings_checkbox_thumbnail_in_notification": {"message": "Pokaż miniaturę w powiadomieniach"}, "v9_settings_checkbox_use_legacy_ui": {"message": "Użyj starszego interfejsu użytkownika"}, "v9_settings_checkbox_use_wide_ui": {"message": "Zwiększ wyskakujące okienko dodatku"}, "v9_settings_checkbox_view_convert_local": {"message": "Pokaż przycisk Konwertuj Lokalnie"}, "v9_settings_download_directory": {"message": "Katalog pobierania"}, "v9_settings_download_directory_change": {"message": "Zmian<PERSON>"}, "v9_settings_license_check": {"message": "Sprawdź klucz licencyjny"}, "v9_settings_license_get": {"message": "<PERSON><PERSON><PERSON> licencj<PERSON>"}, "v9_settings_license_placeholder": {"message": "Wprowadź licencję"}, "v9_settings_theme_dark": {"message": "ciemny"}, "v9_settings_theme_light": {"message": "jasny"}, "v9_settings_theme_system": {"message": "system"}, "v9_settings_theme_title": {"message": "Schemat kolorystyczny"}, "v9_settings_variants_clear": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "v9_settings_variants_title": {"message": "Ustawienia wariantów"}, "v9_short_help": {"message": "pomoc?"}, "v9_smartnaming_max_length": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "v9_smartnaming_reset_for": {"message": "Zresetuj dla"}, "v9_smartnaming_reset_for_all": {"message": "Zresetuj wszystkie reguły dotyczące nazw hostów."}, "v9_smartnaming_result": {"message": "Rezultat"}, "v9_smartnaming_save_for": {"message": "Zapisz dla"}, "v9_smartnaming_save_for_all": {"message": "Zapisz dla wszystkich nazw hostów."}, "v9_smartnaming_selector": {"message": "Tekst z selektora CSS (opcjonalnie)"}, "v9_smartnaming_template": {"message": "Szablon. Użyj: %title %hostname %pathname %selector"}, "v9_smartnaming_test": {"message": "Test"}, "v9_smartnaming_title": {"message": "Inteligentne nazewnictwo"}, "v9_tell_me_more": {"message": "Powiedz więcej o tym"}, "v9_user_message_auto_hide_downloaded": {"message": "Automatycznie ukryć pobrane multimedia?"}, "v9_user_message_no_incognito_body": {"message": "Narzędzie Video DownloadHelper nie jest włączone w oknach prywatnych/incognito. Musisz włączyć tę opcję ręcznie (nie jest to wymagane)."}, "v9_user_message_no_incognito_open_settings": {"message": "Włącz w Ustawieniach przeglądarki"}, "v9_user_message_no_incognito_title": {"message": "Brak trybu incognito"}, "v9_user_message_one_hundred_downloads": {"message": "Pobrałeś 100 filmów!"}, "v9_user_message_one_hundred_downloads_body": {"message": "<PERSON><PERSON>, że podoba Ci się Video DownloadHelper :) <PERSON><PERSON> z<PERSON>ś napisać miłą recenzję na stronie dodatku?"}, "v9_user_message_one_hundred_downloads_leave_review": {"message": "Zostaw recenzję"}, "v9_user_message_one_hundred_downloads_never_show_again": {"message": "<PERSON><PERSON> pyt<PERSON> ponownie"}, "v9_vdh_notification": {"message": "Pomocnik Pobierania Wideo "}, "v9_weh_prefs_description_contextMenuEnabled": {"message": "Uzyskaj dostęp do komend poprzez kliknięcie prawym przyciskiem myszy na stronie"}, "v9_weh_prefs_label_downloadControlledMax": {"message": "Maksymalna liczba równoczesnych pobrań"}, "v9_yes": {"message": "Tak"}, "v9_yt_bulk_detected": {"message": "Wykryto $1 video z Youtube"}, "v9_yt_bulk_detected_trigger": {"message": "Rozpocznij pobieranie zbiorcze"}, "validate_license": {"message": "Zarejestruj licencję"}, "variants_list_adp": {"message": "Warianty adaptacyjne"}, "variants_list_full": {"message": "Warianty"}, "vdh_notification": {"message": "Pomocnik Pobierania Wideo "}, "version": {"message": "Wersja $1"}, "video_only": {"message": "<PERSON><PERSON><PERSON>o"}, "video_qualities": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>o"}, "weh_prefs_alertDialogType_option_panel": {"message": "Okno"}, "weh_prefs_alertDialogType_option_tab": {"message": "Zakładka"}, "weh_prefs_coappDownloads_option_ask": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "weh_prefs_coappDownloads_option_browser": {"message": "Przeglądarka"}, "weh_prefs_coappDownloads_option_coapp": {"message": "Aplikacja Firmowa"}, "weh_prefs_dashOnAdp_option_audio": {"message": "Pobierz audio"}, "weh_prefs_dashOnAdp_option_audio_video": {"message": "Połącz audio i wideo"}, "weh_prefs_dashOnAdp_option_video": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_description_adpHide": {"message": "Nie wyświetlaj elementów typu ADP na liście pobierania"}, "weh_prefs_description_alertDialogType": {"message": "Jak w<PERSON>świetlać dialogi alertów"}, "weh_prefs_description_autoPin": {"message": "Przypnij utwór po pobraniu."}, "weh_prefs_description_avplayEnabled": {"message": "Pozwól na odtwarzanie wideo w aplikacji firmowej"}, "weh_prefs_description_blacklistEnabled": {"message": "Zapobiega aktywowaniu rozpoznawania plików multimedialnych przez witryny."}, "weh_prefs_description_bulkEnabled": {"message": "Aktywuj pobierania"}, "weh_prefs_description_checkCoappOnStartup": {"message": "Ten dodatek uruchamia się samoczynnie, gdy dostępne są multimedia na stronie www"}, "weh_prefs_description_chunkedCoappDataRequests": {"message": "Sprawdzam użycie zasobów przez aplikacje firmową"}, "weh_prefs_description_chunkedCoappManifestsRequests": {"message": "Żądanie podzielonych manifestów za pomocą aplikacji towarzyszącej"}, "weh_prefs_description_chunksConcurrentDownloads": {"message": "Maksimum segmentów do pobrania równocześnie"}, "weh_prefs_description_chunksEnabled": {"message": "Strumieniowanie kawałków włączone"}, "weh_prefs_description_chunksPrefetchCount": {"message": "Ile segmentów pobrać z góry"}, "weh_prefs_description_coappDownloads": {"message": "Aplikacja wykonuje faktyczne pobieranie"}, "weh_prefs_description_coappIdleExit": {"message": "Zamknij automatycznie po liczbie milisekund, wyłączone jeśli 0"}, "weh_prefs_description_coappRestartDelay": {"message": "Opóźnienie w milisekundach podczas restartowania aplikacji firmowej"}, "weh_prefs_description_coappUseProxy": {"message": "Aplikacja towarzysząca używa proxy identycznego z oryginałem"}, "weh_prefs_description_contentRedirectEnabled": {"message": "Niektóre witryny mogą zwracać nowy adres URL zamiast zawartości multimedialnej"}, "weh_prefs_description_contextMenuEnabled": {"message": "Uzyskaj dostęp do komend poprzez kliknięcie prawym przyciskiem myszy na stronie"}, "weh_prefs_description_convertControlledMax": {"message": "Maks<PERSON>alna liczba jednoczesnych pobierań lub konwersji"}, "weh_prefs_description_converterAggregTuneH264": {"message": "Wymuś H264 podczas sumowania"}, "weh_prefs_description_converterKeepTmpFiles": {"message": "Nie usuwaj plików tymczasowych po przetworzeniu"}, "weh_prefs_description_converterThreads": {"message": "Liczba wątków używana podczas konwersji"}, "weh_prefs_description_dashEnabled": {"message": "Włączono dzielone przesyłanie strumieniowe DASH"}, "weh_prefs_description_dashHideM4s": {"message": "Nie wyświetlaj elementów typu .m4s na liście pobierania"}, "weh_prefs_description_dashOnAdp": {"message": "Kiedy DASH zawiera łącznie audio i wideo"}, "weh_prefs_description_dialogAutoClose": {"message": "Dialog jest zamknięty od utraty fokusa"}, "weh_prefs_description_downloadControlledMax": {"message": "Kontroluje liczbę generowanych przez dodatek jednoczesnych pobrań, by <PERSON><PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> przep<PERSON><PERSON><PERSON><PERSON><PERSON> łącza."}, "weh_prefs_description_downloadRetries": {"message": "Liczba prób pobierania"}, "weh_prefs_description_downloadRetryDelay": {"message": "Odstęp między próbami pobrania (ms)"}, "weh_prefs_description_downloadStreamControlledMax": {"message": "Kontroluje liczbę strumieni pobieranych dla pojedynczej pozycji"}, "weh_prefs_description_fileDialogType": {"message": "Jak wyświetlać dialogi plików"}, "weh_prefs_description_galleryNaming": {"message": "Jak nazywać pliki z galerii pobranych"}, "weh_prefs_description_hitsGotoTab": {"message": "Wyświetlaj link w opisie do przejścia do zakładki wideo"}, "weh_prefs_description_hlsDownloadAsM2ts": {"message": "Pobieraj strumienie HLS jako M2TS"}, "weh_prefs_description_hlsEnabled": {"message": "Włączone przesyłanie strumieniowe HLS"}, "weh_prefs_description_hlsEndTimeout": {"message": "Czas przerwania oczekiwania na kolejne składowe HLS w sekundach"}, "weh_prefs_description_hlsRememberPrevLiveChunks": {"message": "Zapamiętaj poprzednie fragmenty strumieni wideo (HLS)"}, "weh_prefs_description_iconActivation": {"message": "Kiedy aktywuje się ikona panelu"}, "weh_prefs_description_iconBadge": {"message": "Wyświetlanie znaczka ikony paska narzędzi"}, "weh_prefs_description_ignoreProtectedVariants": {"message": "<PERSON><PERSON> wyświet<PERSON>j <PERSON>, które są chronione."}, "weh_prefs_description_lastDownloadDirectory": {"message": "Używane tylko podczas procesu Companion App"}, "weh_prefs_description_mediaExtensions": {"message": "Rozszerzenia, które mają być postrzegane jako pliki multimedialne"}, "weh_prefs_description_medialinkAutoDetect": {"message": "Wykonaj przy każdym załadowaniu strony (może mieć wpływ na wydajność)"}, "weh_prefs_description_medialinkExtensions": {"message": "Rozszerzenia plików rozpoznawane sla przechwytywania galerii"}, "weh_prefs_description_medialinkMaxHits": {"message": "Limituj liczbę pozycji wykrytych jako galeria"}, "weh_prefs_description_medialinkMinFilesPerGroup": {"message": "Minimum pozycji do wykrycia jako galeria"}, "weh_prefs_description_medialinkMinImgSize": {"message": "Minimalny rozmiar obrazu postrzegany jako galeria obrazów"}, "weh_prefs_description_medialinkScanImages": {"message": "Wykrywaj obrazki na stronie"}, "weh_prefs_description_medialinkScanLinks": {"message": "Wykryj treści bezpośrednio przekierowane na tę stronę"}, "weh_prefs_description_mediaweightMinSize": {"message": "Pomijaj pobrania poniżej tego rozmiaru."}, "weh_prefs_description_mediaweightThreshold": {"message": "Wymusza wykrywanie multimediów powyżej tej wielkości."}, "weh_prefs_description_monitorNetworkRequests": {"message": "<PERSON><PERSON><PERSON>, używając oryginalnego zapytania o nagłówki"}, "weh_prefs_description_mpegtsHideTs": {"message": "Nie wyświetlaj elementów typu .ts na liście pobierania"}, "weh_prefs_description_networkFilterOut": {"message": "Wyrażenia regularne do pomijania niektórych adresów URL multimediów."}, "weh_prefs_description_networkProbe": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby w<PERSON> to"}, "weh_prefs_description_noPrivateNotification": {"message": "Brak powiadomienia dla prywatnych odsłon"}, "weh_prefs_description_notifyReady": {"message": "Powiadamiaj po przetworzeniu"}, "weh_prefs_description_orphanExpiration": {"message": "<PERSON>zas wygaśnięcia w sekundach przed usunięciem osieroconych pobrań."}, "weh_prefs_description_qualitiesMaxVariants": {"message": "Maksymalna liczba wyświetlanych wariantów dla tego samego filmu."}, "weh_prefs_description_rememberLastDir": {"message": "Użyj ostatnio używanej ścieżki pobierania jako domyślnej"}, "weh_prefs_description_smartnamerFnameMaxlen": {"message": "Zapewnia nieprzekroczenie długości generowanej nazwy pliku"}, "weh_prefs_description_smartnamerFnameSpaces": {"message": "Jak się obchodzić ze spacjami w nazwach wideo"}, "weh_prefs_description_tbsnEnabled": {"message": "Pozwól na wykrywanie i pobieranie treści video w witrynie Facebooku"}, "weh_prefs_description_titleMode": {"message": "<PERSON><PERSON> pow<PERSON>y by<PERSON> wyświetlane długie tytuły wideo w panelu głównym"}, "weh_prefs_description_toolsMenuEnabled": {"message": "Dostęp komend do menu narzędzi"}, "weh_prefs_fileDialogType_option_panel": {"message": "Okno"}, "weh_prefs_fileDialogType_option_tab": {"message": "Zakładka"}, "weh_prefs_galleryNaming_option_index_url": {"message": "Indeks - Url"}, "weh_prefs_galleryNaming_option_type_index": {"message": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, "weh_prefs_galleryNaming_option_url": {"message": "Url"}, "weh_prefs_iconActivation_option_anytab": {"message": "Trafienie z dowolnej karty"}, "weh_prefs_iconActivation_option_currenttab": {"message": "Trafienie z bieżącej karty"}, "weh_prefs_iconBadge_option_activetab": {"message": "Multimedia z aktywnej karty"}, "weh_prefs_iconBadge_option_anytab": {"message": "Multimedia z dowolnej karty"}, "weh_prefs_iconBadge_option_mixed": {"message": "Mi<PERSON><PERSON><PERSON>"}, "weh_prefs_iconBadge_option_none": {"message": "Brak"}, "weh_prefs_iconBadge_option_pinned": {"message": "Przypię<PERSON> hity"}, "weh_prefs_iconBadge_option_tasks": {"message": "Uruchomione z<PERSON>"}, "weh_prefs_label_adpHide": {"message": "Ukryj warianty ADP"}, "weh_prefs_label_alertDialogType": {"message": "Dialog alertu"}, "weh_prefs_label_autoPin": {"message": "Automatyczne przypinanie"}, "weh_prefs_label_avplayEnabled": {"message": "Odtwarzacz włączony"}, "weh_prefs_label_blacklistEnabled": {"message": "Aktywuj <PERSON>ę zabronionych"}, "weh_prefs_label_bulkEnabled": {"message": "Włącz"}, "weh_prefs_label_checkCoappOnStartup": {"message": "Sprawdź CoApp przy starcie"}, "weh_prefs_label_chunkedCoappDataRequests": {"message": "Sprawdź wymagane dane Co<PERSON>pp"}, "weh_prefs_label_chunkedCoappManifestsRequests": {"message": "Sprawdź inne wymagane CoApp"}, "weh_prefs_label_chunksConcurrentDownloads": {"message": "Jednoczesne pobieranie kawałków pliku"}, "weh_prefs_label_chunksEnabled": {"message": "Strumieniowanie kawałków"}, "weh_prefs_label_chunksPrefetchCount": {"message": "Liczba wstępnie wczytywanych kawałków"}, "weh_prefs_label_coappDownloads": {"message": "Proces pobierania"}, "weh_prefs_label_coappIdleExit": {"message": "Opuść CoApp za"}, "weh_prefs_label_coappRestartDelay": {"message": "Zrestartuj CoApp za"}, "weh_prefs_label_coappUseProxy": {"message": "CoApp proxy"}, "weh_prefs_label_contentRedirectEnabled": {"message": "Włączone redirect kontekstowe "}, "weh_prefs_label_contextMenuEnabled": {"message": "<PERSON><PERSON>"}, "weh_prefs_label_convertControlledMax": {"message": "Jednoczesne operacje konwertera"}, "weh_prefs_label_converterAggregTuneH264": {"message": "Dostrajanie formatu H264"}, "weh_prefs_label_converterKeepTmpFiles": {"message": "Zachowaj pliki tymczasowe"}, "weh_prefs_label_converterThreads": {"message": "Wątki konwersji"}, "weh_prefs_label_dashEnabled": {"message": "DASH włączone"}, "weh_prefs_label_dashHideM4s": {"message": "Ukryj .m4s"}, "weh_prefs_label_dashOnAdp": {"message": "Stream DASH"}, "weh_prefs_label_dialogAutoClose": {"message": "Automatycznie zamykaj dialogi"}, "weh_prefs_label_downloadControlledMax": {"message": "Maksymalna liczba równoczesnych pobrań"}, "weh_prefs_label_downloadRetries": {"message": "Powtórka pobierania"}, "weh_prefs_label_downloadRetryDelay": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "weh_prefs_label_downloadStreamControlledMax": {"message": "Max konkurencyjnych streamów"}, "weh_prefs_label_fileDialogType": {"message": "Dialog pliku"}, "weh_prefs_label_galleryNaming": {"message": "Nazywanie plików galerii"}, "weh_prefs_label_hitsGotoTab": {"message": "Wyświetlaj „Przejdź do karty”"}, "weh_prefs_label_hlsDownloadAsM2ts": {"message": "HLS jako M2TS"}, "weh_prefs_label_hlsEnabled": {"message": "HLS włączone"}, "weh_prefs_label_hlsEndTimeout": {"message": "Limit czasu HLS"}, "weh_prefs_label_hlsRememberPrevLiveChunks": {"message": "Zapamiętane fragmenty strumieni wideo (HLS)"}, "weh_prefs_label_iconActivation": {"message": "Aktywacja ikony"}, "weh_prefs_label_iconBadge": {"message": "Ikona przycisku"}, "weh_prefs_label_ignoreProtectedVariants": {"message": "Pomijaj zabezpieczone warianty"}, "weh_prefs_label_lastDownloadDirectory": {"message": "Domyślna lokalizacja pobierania"}, "weh_prefs_label_mediaExtensions": {"message": "Wykryj rozszerzenia"}, "weh_prefs_label_medialinkAutoDetect": {"message": "Automatyczne wykrywanie galerii"}, "weh_prefs_label_medialinkExtensions": {"message": "Rozszerzenia audio"}, "weh_prefs_label_medialinkMaxHits": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "weh_prefs_label_medialinkMinFilesPerGroup": {"message": "Minimum wejść"}, "weh_prefs_label_medialinkMinImgSize": {"message": "Minimalny rozmiar obrazka"}, "weh_prefs_label_medialinkScanImages": {"message": "Wykrywaj osadzone obrazki"}, "weh_prefs_label_medialinkScanLinks": {"message": "Wykrywaj odnośniki do multimediów"}, "weh_prefs_label_mediaweightMinSize": {"message": "Minimalny rozmiar"}, "weh_prefs_label_mediaweightThreshold": {"message": "Próg zadziałania"}, "weh_prefs_label_monitorNetworkRequests": {"message": "Sprawdzam nagłówki"}, "weh_prefs_label_mpegtsHideTs": {"message": "ukryj .ts"}, "weh_prefs_label_networkFilterOut": {"message": "Filtr wyjś<PERSON> sieci"}, "weh_prefs_label_networkProbe": {"message": "<PERSON><PERSON> sieci"}, "weh_prefs_label_noPrivateNotification": {"message": "Powiadomienia pry<PERSON>ne"}, "weh_prefs_label_notifyReady": {"message": "Powiadomienia"}, "weh_prefs_label_orphanExpiration": {"message": "Limit czasu wygaśnięcia osieroconych"}, "weh_prefs_label_qualitiesMaxVariants": {"message": "Maksymalna liczba wariantów"}, "weh_prefs_label_rememberLastDir": {"message": "Pamiętaj ostatni folder"}, "weh_prefs_label_smartnamerFnameMaxlen": {"message": "Maksymalna długość nazwy pliku"}, "weh_prefs_label_smartnamerFnameSpaces": {"message": "Długie tytuły panelu głównego"}, "weh_prefs_label_tbsnEnabled": {"message": "Wsparcie przez <PERSON>"}, "weh_prefs_label_titleMode": {"message": "Długie tytuły panelu głównego"}, "weh_prefs_label_toolsMenuEnabled": {"message": "<PERSON><PERSON>"}, "weh_prefs_smartnamerFnameSpaces_option_hyphen": {"message": "Zamień na dywizy"}, "weh_prefs_smartnamerFnameSpaces_option_keep": {"message": "<PERSON><PERSON><PERSON>"}, "weh_prefs_smartnamerFnameSpaces_option_remove": {"message": "Usuń"}, "weh_prefs_smartnamerFnameSpaces_option_underscore": {"message": "Zamień na podkreślenia"}, "weh_prefs_titleMode_option_left": {"message": "Wielokropek po lewej stronie"}, "weh_prefs_titleMode_option_multiline": {"message": "W wielu w<PERSON><PERSON>h"}, "weh_prefs_titleMode_option_right": {"message": "Wielokropek po prawej stronie"}, "yes": {"message": "Tak"}, "you_downloaded_n_videos": {"message": "Za pomocą Video DownloadHelpera został pobrany %ty plik."}}