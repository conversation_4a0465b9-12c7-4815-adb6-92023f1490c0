"use strict";(()=>{var Fo=Object.create;var qi=Object.defineProperty;var Lo=Object.getOwnPropertyDescriptor;var jo=Object.getOwnPropertyNames;var Xo=Object.getPrototypeOf,Ho=Object.prototype.hasOwnProperty;var m=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Qo=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of jo(t))!Ho.call(e,s)&&s!==r&&qi(e,s,{get:()=>t[s],enumerable:!(i=Lo(t,s))||i.enumerable});return e};var Er=(e,t,r)=>(r=e!=null?Fo(Xo(e)):{},Qo(t||!e||!e.__esModule?qi(r,"default",{value:e,enumerable:!0}):r,e));var Hi=m(Xi=>{"use strict";Object.defineProperty(Xi,"__esModule",{value:!0})});var Ke=m(ct=>{"use strict";Object.defineProperty(ct,"__esModule",{value:!0});ct.THROW_THE_ERROR=void 0;ct.THROW_THE_ERROR=e=>{throw e}});var Je=m(be=>{"use strict";var zo=be&&be.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),$o=be&&be.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&zo(t,e,r)};Object.defineProperty(be,"__esModule",{value:!0});$o(Ke(),be)});var Qi=m(lt=>{"use strict";Object.defineProperty(lt,"__esModule",{value:!0});lt.httpStatusCodeFrom=void 0;var Zo=ft(),ea=Je();function ta(e,t=ea.THROW_THE_ERROR){return Zo.mustBeHttpStatusCode(e,t),e}lt.httpStatusCodeFrom=ta});var Ui=m(mt=>{"use strict";Object.defineProperty(mt,"__esModule",{value:!0});mt.isHttpStatusCode=void 0;function ra(e){return e>=100&&e<=599}mt.isHttpStatusCode=ra});var Te=m(gt=>{"use strict";Object.defineProperty(gt,"__esModule",{value:!0});gt.AppError=void 0;var wr=class extends Error{constructor(t){super(t.detail),this.details=t,this.name=this.details.packageName+"/"+this.details.errorName}};gt.AppError=wr});var Sr=m(ht=>{"use strict";Object.defineProperty(ht,"__esModule",{value:!0});ht.ValueObject=void 0;var Nr=class{constructor(t){this.value=t}valueOf(){return this.value}isValue(){return!0}};ht.ValueObject=Nr});var Ee=m(vt=>{"use strict";Object.defineProperty(vt,"__esModule",{value:!0});vt.StructuredProblemReport=void 0;var ia=Sr(),Pr=class e extends ia.ValueObject{static from(t){return new e(t)}get detail(){return this.value.template.detail}get errorId(){var t;return(t=this.value.errorId)!==null&&t!==void 0?t:null}get errorName(){return this.value.template.errorName}get extra(){return this.value.extra}get fqErrorName(){return this.packageName+"/"+this.errorName}get packageName(){return this.value.template.packageName}get status(){return this.value.template.status}get template(){return this.value.template}};vt.StructuredProblemReport=Pr});var xe=m(X=>{"use strict";Object.defineProperty(X,"__esModule",{value:!0});X.ERROR_TABLE=X.PackageErrorTable=X.PACKAGE_NAME=void 0;X.PACKAGE_NAME="@ganbarodigital/ts-lib-error-reporting/lib/v1";var _t=class{constructor(){this["http-status-code-out-of-range"]={packageName:X.PACKAGE_NAME,errorName:"http-status-code-out-of-range",detail:"input falls outside the range of a valid HTTP status code",status:422},this["invalid-package-name"]={packageName:X.PACKAGE_NAME,errorName:"invalid-package-name",detail:"package name does not meet spec 'isPackageName()'",status:422},this["not-an-integer"]={packageName:X.PACKAGE_NAME,errorName:"not-an-integer",detail:"input must be an integer; was a float",status:422},this["not-implemented"]={packageName:X.PACKAGE_NAME,errorName:"not-implemented",detail:"this function or feature has not been implemented",status:500},this["unreachable-code"]={packageName:X.PACKAGE_NAME,errorName:"unreachable-code",status:500,detail:"this code should never execute"}}};X.PackageErrorTable=_t;X.ERROR_TABLE=new _t});var Dr=m(At=>{"use strict";Object.defineProperty(At,"__esModule",{value:!0});At.HttpStatusCodeOutOfRangeError=void 0;var na=Te(),sa=Ee(),oa=xe(),Ir=class extends na.AppError{constructor(t){let r={template:oa.ERROR_TABLE["http-status-code-out-of-range"],errorId:t.errorId,extra:{public:t.public}};super(sa.StructuredProblemReport.from(r))}};At.HttpStatusCodeOutOfRangeError=Ir});var Cr=m(yt=>{"use strict";Object.defineProperty(yt,"__esModule",{value:!0});yt.NotAnIntegerError=void 0;var aa=Te(),ua=Ee(),da=xe(),Mr=class extends aa.AppError{constructor(t){let r={template:da.ERROR_TABLE["not-an-integer"],errorId:t.errorId,extra:{public:t.public}};super(ua.StructuredProblemReport.from(r))}};yt.NotAnIntegerError=Mr});var Gi=m(bt=>{"use strict";Object.defineProperty(bt,"__esModule",{value:!0});bt.mustBeHttpStatusCode=void 0;var pa=ft(),ca=Dr(),la=Cr(),fa=Je();function ma(e,t=fa.THROW_THE_ERROR){e>>>0!==e&&t(new la.NotAnIntegerError({public:{input:e}})),pa.isHttpStatusCode(e)||t(new ca.HttpStatusCodeOutOfRangeError({public:{input:e}}))}bt.mustBeHttpStatusCode=ma});var ft=m(W=>{"use strict";var ga=W&&W.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Tt=W&&W.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&ga(t,e,r)};Object.defineProperty(W,"__esModule",{value:!0});Tt(Hi(),W);Tt(Qi(),W);Tt(Ui(),W);Tt(Gi(),W)});var Wi=m(Ye=>{"use strict";Object.defineProperty(Ye,"__esModule",{value:!0});var Br=ft();Object.defineProperty(Ye,"isHttpStatusCode",{enumerable:!0,get:function(){return Br.isHttpStatusCode}});Object.defineProperty(Ye,"mustBeHttpStatusCode",{enumerable:!0,get:function(){return Br.mustBeHttpStatusCode}});Object.defineProperty(Ye,"httpStatusCodeFrom",{enumerable:!0,get:function(){return Br.httpStatusCodeFrom}})});var Ki=m(Oe=>{"use strict";var ha=Oe&&Oe.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),va=Oe&&Oe.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&ha(t,e,r)};Object.defineProperty(Oe,"__esModule",{value:!0});va(Wi(),Oe)});var Yi=m(Ji=>{"use strict";Object.defineProperty(Ji,"__esModule",{value:!0})});var $i=m(zi=>{"use strict";Object.defineProperty(zi,"__esModule",{value:!0})});var Zi=m(Z=>{"use strict";var _a=Z&&Z.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),qr=Z&&Z.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&_a(t,e,r)};Object.defineProperty(Z,"__esModule",{value:!0});qr(Te(),Z);qr(Yi(),Z);qr($i(),Z)});var tn=m(en=>{"use strict";Object.defineProperty(en,"__esModule",{value:!0})});var rn=m(Re=>{"use strict";var Aa=Re&&Re.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ya=Re&&Re.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Aa(t,e,r)};Object.defineProperty(Re,"__esModule",{value:!0});ya(tn(),Re)});var sn=m(nn=>{"use strict";Object.defineProperty(nn,"__esModule",{value:!0})});var on=m(we=>{"use strict";var ba=we&&we.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Ta=we&&we.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&ba(t,e,r)};Object.defineProperty(we,"__esModule",{value:!0});Ta(sn(),we)});var an=m(Et=>{"use strict";Object.defineProperty(Et,"__esModule",{value:!0});Et.InvalidPackageNameError=void 0;var Ea=Te(),xa=Ee(),Oa=xe(),Vr=class extends Ea.AppError{constructor(t){let r={template:Oa.ERROR_TABLE["invalid-package-name"],errorId:t.errorId,extra:{public:t.public}};super(xa.StructuredProblemReport.from(r))}};Et.InvalidPackageNameError=Vr});var un=m(xt=>{"use strict";Object.defineProperty(xt,"__esModule",{value:!0});xt.NotImplementedError=void 0;var Ra=Te(),wa=Ee(),Na=xe(),kr=class extends Ra.AppError{constructor(t={}){let r={template:Na.ERROR_TABLE["not-implemented"],errorId:t.errorId};super(wa.StructuredProblemReport.from(r))}};xt.NotImplementedError=kr});var dn=m(Ot=>{"use strict";Object.defineProperty(Ot,"__esModule",{value:!0});Ot.UnreachableCodeError=void 0;var Sa=Te(),Pa=Ee(),Ia=xe(),Fr=class extends Sa.AppError{constructor(t){let r={template:Ia.ERROR_TABLE["unreachable-code"],errorId:t.errorId,extra:{logsOnly:t.logsOnly}};super(Pa.StructuredProblemReport.from(r))}};Ot.UnreachableCodeError=Fr});var Lr=m(Ne=>{"use strict";Object.defineProperty(Ne,"__esModule",{value:!0});var Da=Dr();Object.defineProperty(Ne,"HttpStatusCodeOutOfRangeError",{enumerable:!0,get:function(){return Da.HttpStatusCodeOutOfRangeError}});var Ma=an();Object.defineProperty(Ne,"InvalidPackageNameError",{enumerable:!0,get:function(){return Ma.InvalidPackageNameError}});var Ca=Cr();Object.defineProperty(Ne,"NotAnIntegerError",{enumerable:!0,get:function(){return Ca.NotAnIntegerError}});var Ba=un();Object.defineProperty(Ne,"NotImplementedError",{enumerable:!0,get:function(){return Ba.NotImplementedError}});var qa=dn();Object.defineProperty(Ne,"UnreachableCodeError",{enumerable:!0,get:function(){return qa.UnreachableCodeError}})});var cn=m(pn=>{"use strict";Object.defineProperty(pn,"__esModule",{value:!0})});var fn=m(ln=>{"use strict";Object.defineProperty(ln,"__esModule",{value:!0})});var gn=m(mn=>{"use strict";Object.defineProperty(mn,"__esModule",{value:!0})});var vn=m(hn=>{"use strict";Object.defineProperty(hn,"__esModule",{value:!0})});var An=m(_n=>{"use strict";Object.defineProperty(_n,"__esModule",{value:!0})});var yn=m(U=>{"use strict";var Va=U&&U.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ze=U&&U.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Va(t,e,r)};Object.defineProperty(U,"__esModule",{value:!0});ze(cn(),U);ze(fn(),U);ze(gn(),U);ze(vn(),U);ze(An(),U)});var bn=m(Se=>{"use strict";Object.defineProperty(Se,"__esModule",{value:!0});Se.extractReasonFromCaught=Se.DEFAULT_ERROR_REASON=void 0;Se.DEFAULT_ERROR_REASON="no error information available";function ka(e,{stackTrace:t=!1}={}){let r=Se.DEFAULT_ERROR_REASON;return e instanceof Error?(t&&e.stack?r=e.stack:r=e.toString(),r):(e===null||e===void 0||typeof e=="number"&&isNaN(e)||typeof e=="boolean"||e.toString!==void 0&&typeof e.toString=="function"&&e.toString!==Object.prototype.toString&&(r=e.toString()),r)}Se.extractReasonFromCaught=ka});var Tn=m(Rt=>{"use strict";Object.defineProperty(Rt,"__esModule",{value:!0});Rt.extractStackFromCaught=void 0;function Fa(e){return e instanceof Error?e.stack.substring(e.stack.indexOf(`
`)+1):""}Rt.extractStackFromCaught=Fa});var xn=m(ce=>{"use strict";var La=ce&&ce.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),En=ce&&ce.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&La(t,e,r)};Object.defineProperty(ce,"__esModule",{value:!0});En(bn(),ce);En(Tn(),ce)});var Rn=m(On=>{"use strict";Object.defineProperty(On,"__esModule",{value:!0})});var Nn=m(wn=>{"use strict";Object.defineProperty(wn,"__esModule",{value:!0})});var Sn=m(ee=>{"use strict";var ja=ee&&ee.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),jr=ee&&ee.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&ja(t,e,r)};Object.defineProperty(ee,"__esModule",{value:!0});jr(Ee(),ee);jr(Rn(),ee);jr(Nn(),ee)});var V=m(q=>{"use strict";var Xa=q&&q.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),le=q&&q.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Xa(t,e,r)};Object.defineProperty(q,"__esModule",{value:!0});le(Zi(),q);le(rn(),q);le(on(),q);le(Lr(),q);le(yn(),q);le(xn(),q);le(Je(),q);le(Sn(),q)});var Pn=m(wt=>{"use strict";Object.defineProperty(wt,"__esModule",{value:!0});var Ha=xe();Object.defineProperty(wt,"PackageErrorTable",{enumerable:!0,get:function(){return Ha.PackageErrorTable}});var Qa=V();Object.defineProperty(wt,"InvalidPackageNameError",{enumerable:!0,get:function(){return Qa.InvalidPackageNameError}})});var Dn=m(In=>{"use strict";Object.defineProperty(In,"__esModule",{value:!0})});var Xr=m(Pe=>{"use strict";Object.defineProperty(Pe,"__esModule",{value:!0});Pe.isPackageNameData=Pe.PackageNameDataRegex=void 0;Pe.PackageNameDataRegex=new RegExp("^(?:@[a-z0-9-*~][a-z0-9-*._~]*/)?[a-z0-9-~][a-z0-9-._~]+(/[A-Za-z0-9-~][A-Za-z0-9-._~]+)*$");function Ua(e){return Pe.PackageNameDataRegex.test(e)}Pe.isPackageNameData=Ua});var Hr=m(Nt=>{"use strict";Object.defineProperty(Nt,"__esModule",{value:!0});Nt.mustBePackageNameData=void 0;var Ga=Lr(),Wa=Ke(),Ka=Xr();function Ja(e,t=Wa.THROW_THE_ERROR){Ka.isPackageNameData(e)||t(new Ga.InvalidPackageNameError({public:{packageName:e}}))}Nt.mustBePackageNameData=Ja});var Mn=m(St=>{"use strict";Object.defineProperty(St,"__esModule",{value:!0});St.packageNameFrom=void 0;var Ya=Je(),za=Hr();function $a(e,t=Ya.THROW_THE_ERROR){return za.mustBePackageNameData(e,t),e}St.packageNameFrom=$a});var Cn=m(K=>{"use strict";var Za=K&&K.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),Pt=K&&K.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Za(t,e,r)};Object.defineProperty(K,"__esModule",{value:!0});Pt(Dn(),K);Pt(Xr(),K);Pt(Hr(),K);Pt(Mn(),K)});var Bn=m(Ie=>{"use strict";var eu=Ie&&Ie.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),tu=Ie&&Ie.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&eu(t,e,r)};Object.defineProperty(Ie,"__esModule",{value:!0});tu(Cn(),Ie)});var Vn=m(fe=>{"use strict";var ru=fe&&fe.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),qn=fe&&fe.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&ru(t,e,r)};Object.defineProperty(fe,"__esModule",{value:!0});qn(Pn(),fe);qn(Bn(),fe)});var Qr=m(Ve=>{"use strict";Object.defineProperty(Ve,"__esModule",{value:!0});Ve.ERROR_TABLE=Ve.PackageErrorTable=void 0;var It=Ki(),iu=Vn(),Dt=iu.packageNameFrom("@ganbarodigital/ts-lib-mediatypes"),Mt=class{constructor(){this["mediatypematchregex-is-broken"]={packageName:Dt,errorName:"mediatypematchregex-is-broken",detail:"the MediaTypeMatchRegex no longer returns the expected named groups",status:It.httpStatusCodeFrom(500)},this["not-a-content-type"]={packageName:Dt,errorName:"not-a-content-type",detail:"the given string does not have the structure of a ContentType",status:It.httpStatusCodeFrom(422)},this["not-a-media-type"]={packageName:Dt,errorName:"not-a-media-type",detail:"the given string does not have the structure of a MediaType",status:It.httpStatusCodeFrom(422)},this["unexpected-content-type"]={packageName:Dt,errorName:"unexpected-content-type",detail:"the given MediaType does not match any of the expected content types",status:It.httpStatusCodeFrom(422)}}};Ve.PackageErrorTable=Mt;Ve.ERROR_TABLE=new Mt});var Fn=m(Ct=>{"use strict";Object.defineProperty(Ct,"__esModule",{value:!0});Ct.NotAContentTypeError=void 0;var kn=V(),nu=J(),Ur=class extends kn.AppError{constructor(t){let r={template:nu.ERROR_TABLE["not-a-content-type"],errorId:t.errorId,extra:{public:t.public}};super(kn.StructuredProblemReport.from(r))}};Ct.NotAContentTypeError=Ur});var jn=m(Bt=>{"use strict";Object.defineProperty(Bt,"__esModule",{value:!0});Bt.NotAMediaTypeError=void 0;var Ln=V(),su=J(),Gr=class extends Ln.AppError{constructor(t){let r={template:su.ERROR_TABLE["not-a-media-type"],errorId:t.errorId,extra:{public:t.public}};super(Ln.StructuredProblemReport.from(r))}};Bt.NotAMediaTypeError=Gr});var Vt=m(qt=>{"use strict";Object.defineProperty(qt,"__esModule",{value:!0});qt.MediaTypeMatchRegexIsBrokenError=void 0;var Xn=V(),ou=Qr(),Wr=class extends Xn.AppError{constructor(t){let r={template:ou.ERROR_TABLE["mediatypematchregex-is-broken"],errorId:t.errorId,extra:null};super(Xn.StructuredProblemReport.from(r))}};qt.MediaTypeMatchRegexIsBrokenError=Wr});var Jr=m(kt=>{"use strict";Object.defineProperty(kt,"__esModule",{value:!0});kt.UnexpectedContentTypeError=void 0;var Hn=V(),au=J(),Kr=class extends Hn.AppError{constructor(t){let r={template:au.ERROR_TABLE["unexpected-content-type"],errorId:t.errorId,extra:{public:t.public}};super(Hn.StructuredProblemReport.from(r))}};kt.UnexpectedContentTypeError=Kr});var J=m(De=>{"use strict";Object.defineProperty(De,"__esModule",{value:!0});var uu=Qr();Object.defineProperty(De,"ERROR_TABLE",{enumerable:!0,get:function(){return uu.ERROR_TABLE}});var du=Fn();Object.defineProperty(De,"NotAContentTypeError",{enumerable:!0,get:function(){return du.NotAContentTypeError}});var pu=jn();Object.defineProperty(De,"NotAMediaTypeError",{enumerable:!0,get:function(){return pu.NotAMediaTypeError}});var cu=Vt();Object.defineProperty(De,"MediaTypeMatchRegexIsBrokenError",{enumerable:!0,get:function(){return cu.MediaTypeMatchRegexIsBrokenError}});var lu=Jr();Object.defineProperty(De,"UnexpectedContentTypeError",{enumerable:!0,get:function(){return lu.UnexpectedContentTypeError}})});var Un=m(Qn=>{"use strict";Object.defineProperty(Qn,"__esModule",{value:!0})});var ke=m(me=>{"use strict";Object.defineProperty(me,"__esModule",{value:!0});me.MediaTypeParamRegex=me.MediaTypeMatchRegex=me.ContentTypeMatchRegex=void 0;me.ContentTypeMatchRegex=/^(?<contentType>(?<type>[A-Za-z0-9][-\w!#$&^]*)\/((?<tree>[A-Za-z0-9][\w\d-!#$&^]*)\.){0,1}(?<subtype>[^+()<>@,;:\\/"[\]?=+]+)(\+(?<suffix>[\w\d]+)){0,1})$/;me.MediaTypeMatchRegex=/^(?<contentType>(?<type>[A-Za-z0-9][-\w!#$&^]*)\/((?<tree>[A-Za-z0-9][\w\d-!#$&^]*)\.){0,1}(?<subtype>[^+()<>@,;:\\/"[\]?=+]+)(\+(?<suffix>[\w\d]+)){0,1})(;[\s]+(?<parameter>[\w\d]+=([^+()<>@,;:\\/"[\]?=]+|"[^"]*\")))*$/;me.MediaTypeParamRegex=/(;[\s]+((?<parameterName>[\w\d]+)=((?<parameterValueA>[^+()<>@,;:\\/"[\]?=+]+)|"(?<parameterValueB>[^"]*)")))/g});var Yr=m(Ft=>{"use strict";Object.defineProperty(Ft,"__esModule",{value:!0});Ft.isContentType=void 0;var fu=ke();function mu(e){return fu.ContentTypeMatchRegex.test(e)}Ft.isContentType=mu});var zr=m(Lt=>{"use strict";Object.defineProperty(Lt,"__esModule",{value:!0});Lt.mustBeContentType=void 0;var gu=V(),hu=J(),vu=Yr();function _u(e,t=gu.THROW_THE_ERROR){vu.isContentType(e)||t(new hu.NotAContentTypeError({public:{input:e}}))}Lt.mustBeContentType=_u});var jt=m(Fe=>{"use strict";Object.defineProperty(Fe,"__esModule",{value:!0});Fe._contentTypeFrom=Fe.contentTypeFrom=void 0;var Au=V(),yu=zr();Fe.contentTypeFrom=Gn.bind(null,e=>e.toLowerCase());function Gn(e,t,r=Au.THROW_THE_ERROR){return yu.mustBeContentType(t,r),e(t)}Fe._contentTypeFrom=Gn});var Kn=m(Le=>{"use strict";Object.defineProperty(Le,"__esModule",{value:!0});Le._contentTypeFromMediaType=Le.contentTypeFromMediaType=void 0;var bu=V(),Tu=J(),Eu=ke(),xu=Vt(),Ou=jt();Le.contentTypeFromMediaType=Wn.bind(null,Eu.MediaTypeMatchRegex,e=>e.toLowerCase());function Wn(e,t,r,i=bu.THROW_THE_ERROR){let s=r.valueOf(),n=e.exec(s);if(n===null)throw i(new Tu.NotAMediaTypeError({public:{input:s}}));if(n.groups===void 0)throw i(new xu.MediaTypeMatchRegexIsBrokenError({}));return Ou._contentTypeFrom(t,n.groups.contentType)}Le._contentTypeFromMediaType=Wn});var Zr=m(G=>{"use strict";var Ru=G&&G.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),$r=G&&G.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Ru(t,e,r)};Object.defineProperty(G,"__esModule",{value:!0});$r(Un(),G);var wu=jt();Object.defineProperty(G,"contentTypeFrom",{enumerable:!0,get:function(){return wu.contentTypeFrom}});var Nu=Kn();Object.defineProperty(G,"contentTypeFromMediaType",{enumerable:!0,get:function(){return Nu.contentTypeFromMediaType}});$r(Yr(),G);$r(zr(),G)});var ei=m(Xt=>{"use strict";Object.defineProperty(Xt,"__esModule",{value:!0});Xt.isMediaType=void 0;var Su=ke();function Pu(e){return Su.MediaTypeMatchRegex.test(e)}Xt.isMediaType=Pu});var Yn=m(Jn=>{"use strict";Object.defineProperty(Jn,"__esModule",{value:!0})});var zn=m(Ht=>{"use strict";Object.defineProperty(Ht,"__esModule",{value:!0});Ht.resolveToContentType=void 0;var Iu=Qt(),Du=Zr();function Mu(e){return e instanceof Iu.MediaType?Du.contentTypeFromMediaType(e):e}Ht.resolveToContentType=Mu});var Zn=m(Ut=>{"use strict";Object.defineProperty(Ut,"__esModule",{value:!0});Ut.resolveToMediaType=void 0;var $n=Qt();function Cu(e){return e instanceof $n.MediaType?e:new $n.MediaType(e)}Ut.resolveToMediaType=Cu});var Gt=m(te=>{"use strict";var Bu=te&&te.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ti=te&&te.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Bu(t,e,r)};Object.defineProperty(te,"__esModule",{value:!0});ti(Yn(),te);ti(zn(),te);ti(Zn(),te)});var ri=m(Wt=>{"use strict";Object.defineProperty(Wt,"__esModule",{value:!0});Wt.matchesContentType=void 0;var es=Gt();function qu(e,t){let r=es.resolveToContentType(e);return t.some(i=>{let s=es.resolveToContentType(i);return r===s})}Wt.matchesContentType=qu});var ii=m(Kt=>{"use strict";Object.defineProperty(Kt,"__esModule",{value:!0});Kt.mustBeMediaType=void 0;var Vu=V(),ku=J(),Fu=ei();function Lu(e,t=Vu.THROW_THE_ERROR){Fu.isMediaType(e)||t(new ku.NotAMediaTypeError({public:{input:e}}))}Kt.mustBeMediaType=Lu});var rs=m(Jt=>{"use strict";Object.defineProperty(Jt,"__esModule",{value:!0});Jt.mustMatchContentType=void 0;var ju=V(),Xu=Jr(),Hu=ri(),ts=Gt();function Qu(e,t,r=ju.THROW_THE_ERROR){if(Hu.matchesContentType(e,t))return;let i=t.map(s=>ts.resolveToContentType(s));r(new Xu.UnexpectedContentTypeError({public:{input:ts.resolveToContentType(e),required:{anyOf:i}}}))}Jt.mustMatchContentType=Qu});var ns=m(is=>{"use strict";Object.defineProperty(is,"__esModule",{value:!0})});var os=m(ss=>{"use strict";Object.defineProperty(ss,"__esModule",{value:!0})});var us=m(as=>{"use strict";Object.defineProperty(as,"__esModule",{value:!0})});var ds=m(Yt=>{"use strict";Object.defineProperty(Yt,"__esModule",{value:!0});Yt.EntityObject=void 0;var ni=class{constructor(t){this.value=t}valueOf(){return this.value}isEntity(){return!0}};Yt.EntityObject=ni});var cs=m(ps=>{"use strict";Object.defineProperty(ps,"__esModule",{value:!0})});var fs=m(ls=>{"use strict";Object.defineProperty(ls,"__esModule",{value:!0})});var gs=m(ms=>{"use strict";Object.defineProperty(ms,"__esModule",{value:!0})});var hs=m(re=>{"use strict";var Uu=re&&re.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),si=re&&re.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Uu(t,e,r)};Object.defineProperty(re,"__esModule",{value:!0});si(fs(),re);si(gs(),re);si(Sr(),re)});var oi=m(Q=>{"use strict";var Gu=Q&&Q.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),$e=Q&&Q.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&Gu(t,e,r)};Object.defineProperty(Q,"__esModule",{value:!0});$e(ns(),Q);$e(os(),Q);$e(us(),Q);$e(ds(),Q);$e(cs(),Q);var Wu=hs();Object.defineProperty(Q,"ValueObject",{enumerable:!0,get:function(){return Wu.ValueObject}})});var ui=m(zt=>{"use strict";Object.defineProperty(zt,"__esModule",{value:!0});zt.RefinedType=void 0;var Ku=oi(),ai=class extends Ku.ValueObject{constructor(t,r,i){r(t,i),super(t)}};zt.RefinedType=ai});var Zt=m($t=>{"use strict";Object.defineProperty($t,"__esModule",{value:!0});$t.RefinedPrimitive=void 0;var Ju=ui(),di=class extends Ju.RefinedType{};$t.RefinedPrimitive=di});var vs=m(er=>{"use strict";Object.defineProperty(er,"__esModule",{value:!0});er.RefinedNumber=void 0;var Yu=Zt(),pi=class extends Yu.RefinedPrimitive{[Symbol.toPrimitive](t){return t==="string"?this.value.toString():this.value}};er.RefinedNumber=pi});var _s=m(tr=>{"use strict";Object.defineProperty(tr,"__esModule",{value:!0});tr.RefinedString=void 0;var zu=Zt(),ci=class extends zu.RefinedPrimitive{[Symbol.toPrimitive](t){return t==="number"?null:this.value}};tr.RefinedString=ci});var As=m(Y=>{"use strict";var $u=Y&&Y.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),rr=Y&&Y.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&$u(t,e,r)};Object.defineProperty(Y,"__esModule",{value:!0});rr(vs(),Y);rr(Zt(),Y);rr(_s(),Y);rr(ui(),Y)});var bs=m(ys=>{"use strict";Object.defineProperty(ys,"__esModule",{value:!0})});var Ts=m(ir=>{"use strict";Object.defineProperty(ir,"__esModule",{value:!0});ir.makeRefinedTypeFactory=void 0;var Zu=Ke();ir.makeRefinedTypeFactory=(e,t=Zu.THROW_THE_ERROR)=>(r,i=t)=>(e(r,i),r)});var Es=m(nr=>{"use strict";Object.defineProperty(nr,"__esModule",{value:!0});nr.makeRefinedTypeFactoryWithFormatter=void 0;var ed=Ke();nr.makeRefinedTypeFactoryWithFormatter=(e,t,r=ed.THROW_THE_ERROR)=>(i,s=r)=>(e(i,s),t(i))});var Os=m(xs=>{"use strict";Object.defineProperty(xs,"__esModule",{value:!0})});var Rs=m(z=>{"use strict";var td=z&&z.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),sr=z&&z.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&td(t,e,r)};Object.defineProperty(z,"__esModule",{value:!0});sr(bs(),z);sr(Ts(),z);sr(Es(),z);sr(Os(),z)});var ws=m(Me=>{"use strict";var rd=Me&&Me.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),id=Me&&Me.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&rd(t,e,r)};Object.defineProperty(Me,"__esModule",{value:!0});id(Rs(),Me)});var Ns=m(ie=>{"use strict";var nd=ie&&ie.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),li=ie&&ie.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&nd(t,e,r)};Object.defineProperty(ie,"__esModule",{value:!0});li(As(),ie);li(ws(),ie);li(oi(),ie)});var fi=m(je=>{"use strict";Object.defineProperty(je,"__esModule",{value:!0});je._parseContentType=je.parseContentType=void 0;var sd=V(),Ss=J(),od=ke(),ad=jt();je.parseContentType=Ps.bind(null,od.MediaTypeMatchRegex,e=>e.toLowerCase());function Ps(e,t,r,i=sd.THROW_THE_ERROR){let s=e.exec(r);if(s===null)throw i(new Ss.NotAMediaTypeError({public:{input:r}}));if(s.groups===void 0)throw i(new Ss.MediaTypeMatchRegexIsBrokenError({}));return ad._contentTypeFrom(t,s.groups.contentType)}je._parseContentType=Ps});var mi=m(Xe=>{"use strict";Object.defineProperty(Xe,"__esModule",{value:!0});Xe.parseMediaTypeUnbound=Xe.parseMediaType=void 0;var ud=V(),dd=J(),pd=Vt(),Is=ke();Xe.parseMediaType=Ds.bind(null,Is.MediaTypeMatchRegex,Is.MediaTypeParamRegex);function Ds(e,t,r,i=ud.THROW_THE_ERROR,s=n=>n.toLocaleLowerCase()){let n=e.exec(r);if(n===null)throw i(new dd.NotAMediaTypeError({public:{input:r}}));if(n.groups===void 0)throw i(new pd.MediaTypeMatchRegexIsBrokenError({}));let a={type:s(n.groups.type),subtype:s(n.groups.subtype)};n.groups.tree&&(a.tree=s(n.groups.tree)),n.groups.suffix&&(a.suffix=s(n.groups.suffix));let o=t.exec(r);if(o!==null)for(a.parameters={};o!==null&&o.groups!==void 0;){let d=s(o.groups.parameterName);a.parameters[d]=o.groups.parameterValueA||o.groups.parameterValueB,o=t.exec(r)}return a}Xe.parseMediaTypeUnbound=Ds});var vi=m(ge=>{"use strict";var or=ge&&ge.__classPrivateFieldGet||function(e,t){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return t.get(e)},Ms=ge&&ge.__classPrivateFieldSet||function(e,t,r){if(!t.has(e))throw new TypeError("attempted to set private field on non-instance");return t.set(e,r),r},Ze,et;Object.defineProperty(ge,"__esModule",{value:!0});ge.MediaType=void 0;var gi=V(),cd=Ns(),ld=ii(),fd=fi(),md=mi(),hi=class e extends cd.RefinedString{constructor(t,r=gi.THROW_THE_ERROR){super(t,ld.mustBeMediaType,r),Ze.set(this,void 0),et.set(this,void 0)}static from(t,r=gi.THROW_THE_ERROR){return new e(t,r)}getContentType(){return or(this,Ze)||Ms(this,Ze,fd.parseContentType(this.valueOf())),or(this,Ze)}parse(){return or(this,et)||Ms(this,et,md.parseMediaType(this.value,gi.THROW_THE_ERROR)),or(this,et)}};ge.MediaType=hi;Ze=new WeakMap,et=new WeakMap});var Cs=m(ar=>{"use strict";Object.defineProperty(ar,"__esModule",{value:!0});ar.mediaTypeFrom=void 0;var gd=vi();ar.mediaTypeFrom=gd.MediaType.from});var qs=m(Bs=>{"use strict";Object.defineProperty(Bs,"__esModule",{value:!0})});var Qt=m(B=>{"use strict";var hd=B&&B.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ne=B&&B.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&hd(t,e,r)};Object.defineProperty(B,"__esModule",{value:!0});ne(ei(),B);ne(ri(),B);ne(ii(),B);ne(rs(),B);ne(Cs(),B);ne(vi(),B);ne(qs(),B);ne(fi(),B);ne(mi(),B)});var Vs=m($=>{"use strict";var vd=$&&$.__createBinding||(Object.create?function(e,t,r,i){i===void 0&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]}),ur=$&&$.__exportStar||function(e,t){for(var r in e)r!=="default"&&!t.hasOwnProperty(r)&&vd(t,e,r)};Object.defineProperty($,"__esModule",{value:!0});ur(J(),$);ur(Zr(),$);ur(Qt(),$);ur(Gt(),$)});var Js=m((If,Ks)=>{var st;typeof window<"u"?st=window:typeof global<"u"?st=global:typeof self<"u"?st=self:st={};Ks.exports=st});var hr=m(ae=>{"use strict";var to=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",Cd=to+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",ro="["+to+"]["+Cd+"]*",Bd=new RegExp("^"+ro+"$"),qd=function(e,t){let r=[],i=t.exec(e);for(;i;){let s=[];s.startIndex=t.lastIndex-i[0].length;let n=i.length;for(let a=0;a<n;a++)s.push(i[a]);r.push(s),i=t.exec(e)}return r},Vd=function(e){let t=Bd.exec(e);return!(t===null||typeof t>"u")};ae.isExist=function(e){return typeof e<"u"};ae.isEmptyObject=function(e){return Object.keys(e).length===0};ae.merge=function(e,t,r){if(t){let i=Object.keys(t),s=i.length;for(let n=0;n<s;n++)r==="strict"?e[i[n]]=[t[i[n]]]:e[i[n]]=t[i[n]]}};ae.getValue=function(e){return ae.isExist(e)?e:""};ae.isName=Vd;ae.getAllMatches=qd;ae.nameRegexp=ro});var Ni=m(ao=>{"use strict";var wi=hr(),kd={allowBooleanAttributes:!1,unpairedTags:[]};ao.validate=function(e,t){t=Object.assign({},kd,t);let r=[],i=!1,s=!1;e[0]==="\uFEFF"&&(e=e.substr(1));for(let n=0;n<e.length;n++)if(e[n]==="<"&&e[n+1]==="?"){if(n+=2,n=no(e,n),n.err)return n}else if(e[n]==="<"){let a=n;if(n++,e[n]==="!"){n=so(e,n);continue}else{let o=!1;e[n]==="/"&&(o=!0,n++);let d="";for(;n<e.length&&e[n]!==">"&&e[n]!==" "&&e[n]!=="	"&&e[n]!==`
`&&e[n]!=="\r";n++)d+=e[n];if(d=d.trim(),d[d.length-1]==="/"&&(d=d.substring(0,d.length-1),n--),!Gd(d)){let v;return d.trim().length===0?v="Invalid space after '<'.":v="Tag '"+d+"' is an invalid name.",D("InvalidTag",v,j(e,n))}let c=jd(e,n);if(c===!1)return D("InvalidAttr","Attributes for '"+d+"' have open quote.",j(e,n));let g=c.value;if(n=c.index,g[g.length-1]==="/"){let v=n-g.length;g=g.substring(0,g.length-1);let y=oo(g,t);if(y===!0)i=!0;else return D(y.err.code,y.err.msg,j(e,v+y.err.line))}else if(o)if(c.tagClosed){if(g.trim().length>0)return D("InvalidTag","Closing tag '"+d+"' can't have attributes or invalid starting.",j(e,a));{let v=r.pop();if(d!==v.tagName){let y=j(e,v.tagStartPos);return D("InvalidTag","Expected closing tag '"+v.tagName+"' (opened in line "+y.line+", col "+y.col+") instead of closing tag '"+d+"'.",j(e,a))}r.length==0&&(s=!0)}}else return D("InvalidTag","Closing tag '"+d+"' doesn't have proper closing.",j(e,n));else{let v=oo(g,t);if(v!==!0)return D(v.err.code,v.err.msg,j(e,n-g.length+v.err.line));if(s===!0)return D("InvalidXml","Multiple possible root nodes found.",j(e,n));t.unpairedTags.indexOf(d)!==-1||r.push({tagName:d,tagStartPos:a}),i=!0}for(n++;n<e.length;n++)if(e[n]==="<")if(e[n+1]==="!"){n++,n=so(e,n);continue}else if(e[n+1]==="?"){if(n=no(e,++n),n.err)return n}else break;else if(e[n]==="&"){let v=Qd(e,n);if(v==-1)return D("InvalidChar","char '&' is not expected.",j(e,n));n=v}else if(s===!0&&!io(e[n]))return D("InvalidXml","Extra text at the end",j(e,n));e[n]==="<"&&n--}}else{if(io(e[n]))continue;return D("InvalidChar","char '"+e[n]+"' is not expected.",j(e,n))}if(i){if(r.length==1)return D("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",j(e,r[0].tagStartPos));if(r.length>0)return D("InvalidXml","Invalid '"+JSON.stringify(r.map(n=>n.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1})}else return D("InvalidXml","Start tag expected.",1);return!0};function io(e){return e===" "||e==="	"||e===`
`||e==="\r"}function no(e,t){let r=t;for(;t<e.length;t++)if(e[t]=="?"||e[t]==" "){let i=e.substr(r,t-r);if(t>5&&i==="xml")return D("InvalidXml","XML declaration allowed only at the start of the document.",j(e,t));if(e[t]=="?"&&e[t+1]==">"){t++;break}else continue}return t}function so(e,t){if(e.length>t+5&&e[t+1]==="-"&&e[t+2]==="-"){for(t+=3;t<e.length;t++)if(e[t]==="-"&&e[t+1]==="-"&&e[t+2]===">"){t+=2;break}}else if(e.length>t+8&&e[t+1]==="D"&&e[t+2]==="O"&&e[t+3]==="C"&&e[t+4]==="T"&&e[t+5]==="Y"&&e[t+6]==="P"&&e[t+7]==="E"){let r=1;for(t+=8;t<e.length;t++)if(e[t]==="<")r++;else if(e[t]===">"&&(r--,r===0))break}else if(e.length>t+9&&e[t+1]==="["&&e[t+2]==="C"&&e[t+3]==="D"&&e[t+4]==="A"&&e[t+5]==="T"&&e[t+6]==="A"&&e[t+7]==="["){for(t+=8;t<e.length;t++)if(e[t]==="]"&&e[t+1]==="]"&&e[t+2]===">"){t+=2;break}}return t}var Fd='"',Ld="'";function jd(e,t){let r="",i="",s=!1;for(;t<e.length;t++){if(e[t]===Fd||e[t]===Ld)i===""?i=e[t]:i!==e[t]||(i="");else if(e[t]===">"&&i===""){s=!0;break}r+=e[t]}return i!==""?!1:{value:r,index:t,tagClosed:s}}var Xd=new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`,"g");function oo(e,t){let r=wi.getAllMatches(e,Xd),i={};for(let s=0;s<r.length;s++){if(r[s][1].length===0)return D("InvalidAttr","Attribute '"+r[s][2]+"' has no space in starting.",at(r[s]));if(r[s][3]!==void 0&&r[s][4]===void 0)return D("InvalidAttr","Attribute '"+r[s][2]+"' is without value.",at(r[s]));if(r[s][3]===void 0&&!t.allowBooleanAttributes)return D("InvalidAttr","boolean attribute '"+r[s][2]+"' is not allowed.",at(r[s]));let n=r[s][2];if(!Ud(n))return D("InvalidAttr","Attribute '"+n+"' is an invalid name.",at(r[s]));if(!i.hasOwnProperty(n))i[n]=1;else return D("InvalidAttr","Attribute '"+n+"' is repeated.",at(r[s]))}return!0}function Hd(e,t){let r=/\d/;for(e[t]==="x"&&(t++,r=/[\da-fA-F]/);t<e.length;t++){if(e[t]===";")return t;if(!e[t].match(r))break}return-1}function Qd(e,t){if(t++,e[t]===";")return-1;if(e[t]==="#")return t++,Hd(e,t);let r=0;for(;t<e.length;t++,r++)if(!(e[t].match(/\w/)&&r<20)){if(e[t]===";")break;return-1}return t}function D(e,t,r){return{err:{code:e,msg:t,line:r.line||r,col:r.col}}}function Ud(e){return wi.isName(e)}function Gd(e){return wi.isName(e)}function j(e,t){let r=e.substring(0,t).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function at(e){return e.startIndex+e[1].length}});var po=m(Si=>{var uo={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,r){return e}},Wd=function(e){return Object.assign({},uo,e)};Si.buildOptions=Wd;Si.defaultOptions=uo});var lo=m((Om,co)=>{"use strict";var Pi=class{constructor(t){this.tagname=t,this.child=[],this[":@"]={}}add(t,r){t==="__proto__"&&(t="#__proto__"),this.child.push({[t]:r})}addChild(t){t.tagname==="__proto__"&&(t.tagname="#__proto__"),t[":@"]&&Object.keys(t[":@"]).length>0?this.child.push({[t.tagname]:t.child,":@":t[":@"]}):this.child.push({[t.tagname]:t.child})}};co.exports=Pi});var mo=m((Rm,fo)=>{var Kd=hr();function Jd(e,t){let r={};if(e[t+3]==="O"&&e[t+4]==="C"&&e[t+5]==="T"&&e[t+6]==="Y"&&e[t+7]==="P"&&e[t+8]==="E"){t=t+9;let i=1,s=!1,n=!1,a="";for(;t<e.length;t++)if(e[t]==="<"&&!n){if(s&&$d(e,t))t+=7,[entityName,val,t]=Yd(e,t+1),val.indexOf("&")===-1&&(r[rp(entityName)]={regx:RegExp(`&${entityName};`,"g"),val});else if(s&&Zd(e,t))t+=8;else if(s&&ep(e,t))t+=8;else if(s&&tp(e,t))t+=9;else if(zd)n=!0;else throw new Error("Invalid DOCTYPE");i++,a=""}else if(e[t]===">"){if(n?e[t-1]==="-"&&e[t-2]==="-"&&(n=!1,i--):i--,i===0)break}else e[t]==="["?s=!0:a+=e[t];if(i!==0)throw new Error("Unclosed DOCTYPE")}else throw new Error("Invalid Tag instead of DOCTYPE");return{entities:r,i:t}}function Yd(e,t){let r="";for(;t<e.length&&e[t]!=="'"&&e[t]!=='"';t++)r+=e[t];if(r=r.trim(),r.indexOf(" ")!==-1)throw new Error("External entites are not supported");let i=e[t++],s="";for(;t<e.length&&e[t]!==i;t++)s+=e[t];return[r,s,t]}function zd(e,t){return e[t+1]==="!"&&e[t+2]==="-"&&e[t+3]==="-"}function $d(e,t){return e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="N"&&e[t+4]==="T"&&e[t+5]==="I"&&e[t+6]==="T"&&e[t+7]==="Y"}function Zd(e,t){return e[t+1]==="!"&&e[t+2]==="E"&&e[t+3]==="L"&&e[t+4]==="E"&&e[t+5]==="M"&&e[t+6]==="E"&&e[t+7]==="N"&&e[t+8]==="T"}function ep(e,t){return e[t+1]==="!"&&e[t+2]==="A"&&e[t+3]==="T"&&e[t+4]==="T"&&e[t+5]==="L"&&e[t+6]==="I"&&e[t+7]==="S"&&e[t+8]==="T"}function tp(e,t){return e[t+1]==="!"&&e[t+2]==="N"&&e[t+3]==="O"&&e[t+4]==="T"&&e[t+5]==="A"&&e[t+6]==="T"&&e[t+7]==="I"&&e[t+8]==="O"&&e[t+9]==="N"}function rp(e){if(Kd.isName(e))return e;throw new Error(`Invalid entity name ${e}`)}fo.exports=Jd});var ho=m((wm,go)=>{var ip=/^[-+]?0x[a-fA-F0-9]+$/,np=/^([\-\+])?(0*)(\.[0-9]+([eE]\-?[0-9]+)?|[0-9]+(\.[0-9]+([eE]\-?[0-9]+)?)?)$/;!Number.parseInt&&window.parseInt&&(Number.parseInt=window.parseInt);!Number.parseFloat&&window.parseFloat&&(Number.parseFloat=window.parseFloat);var sp={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function op(e,t={}){if(t=Object.assign({},sp,t),!e||typeof e!="string")return e;let r=e.trim();if(t.skipLike!==void 0&&t.skipLike.test(r))return e;if(t.hex&&ip.test(r))return Number.parseInt(r,16);{let i=np.exec(r);if(i){let s=i[1],n=i[2],a=ap(i[3]),o=i[4]||i[6];if(!t.leadingZeros&&n.length>0&&s&&r[2]!==".")return e;if(!t.leadingZeros&&n.length>0&&!s&&r[1]!==".")return e;{let d=Number(r),c=""+d;return c.search(/[eE]/)!==-1||o?t.eNotation?d:e:r.indexOf(".")!==-1?c==="0"&&a===""||c===a||s&&c==="-"+a?d:e:n?a===c||s+a===c?d:e:r===c||r===s+c?d:e}}else return e}}function ap(e){return e&&e.indexOf(".")!==-1&&(e=e.replace(/0+$/,""),e==="."?e="0":e[0]==="."?e="0"+e:e[e.length-1]==="."&&(e=e.substr(0,e.length-1))),e}go.exports=op});var _o=m((Sm,vo)=>{"use strict";var Ci=hr(),ut=lo(),up=mo(),dp=ho(),Nm="<((!\\[CDATA\\[([\\s\\S]*?)(]]>))|((NAME:)?(NAME))([^>]*)>|((\\/)(NAME)\\s*>))([^<]*)".replace(/NAME/g,Ci.nameRegexp),Ii=class{constructor(t){this.options=t,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xA2"},pound:{regex:/&(pound|#163);/g,val:"\xA3"},yen:{regex:/&(yen|#165);/g,val:"\xA5"},euro:{regex:/&(euro|#8364);/g,val:"\u20AC"},copyright:{regex:/&(copy|#169);/g,val:"\xA9"},reg:{regex:/&(reg|#174);/g,val:"\xAE"},inr:{regex:/&(inr|#8377);/g,val:"\u20B9"}},this.addExternalEntities=pp,this.parseXml=gp,this.parseTextData=cp,this.resolveNameSpace=lp,this.buildAttributesMap=mp,this.isItStopNode=Ap,this.replaceEntitiesValue=vp,this.readStopNodeData=bp,this.saveTextToParentTag=_p,this.addChild=hp}};function pp(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let i=t[r];this.lastEntities[i]={regex:new RegExp("&"+i+";","g"),val:e[i]}}}function cp(e,t,r,i,s,n,a){if(e!==void 0&&(this.options.trimValues&&!i&&(e=e.trim()),e.length>0)){a||(e=this.replaceEntitiesValue(e));let o=this.options.tagValueProcessor(t,e,r,s,n);return o==null?e:typeof o!=typeof e||o!==e?o:this.options.trimValues?Mi(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?Mi(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function lp(e){if(this.options.removeNSPrefix){let t=e.split(":"),r=e.charAt(0)==="/"?"/":"";if(t[0]==="xmlns")return"";t.length===2&&(e=r+t[1])}return e}var fp=new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`,"gm");function mp(e,t,r){if(!this.options.ignoreAttributes&&typeof e=="string"){let i=Ci.getAllMatches(e,fp),s=i.length,n={};for(let a=0;a<s;a++){let o=this.resolveNameSpace(i[a][1]),d=i[a][4],c=this.options.attributeNamePrefix+o;if(o.length)if(this.options.transformAttributeName&&(c=this.options.transformAttributeName(c)),c==="__proto__"&&(c="#__proto__"),d!==void 0){this.options.trimValues&&(d=d.trim()),d=this.replaceEntitiesValue(d);let g=this.options.attributeValueProcessor(o,d,t);g==null?n[c]=d:typeof g!=typeof d||g!==d?n[c]=g:n[c]=Mi(d,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(n[c]=!0)}if(!Object.keys(n).length)return;if(this.options.attributesGroupName){let a={};return a[this.options.attributesGroupName]=n,a}return n}}var gp=function(e){e=e.replace(/\r\n?/g,`
`);let t=new ut("!xml"),r=t,i="",s="";for(let n=0;n<e.length;n++)if(e[n]==="<")if(e[n+1]==="/"){let o=Be(e,">",n,"Closing Tag is not closed."),d=e.substring(n+2,o).trim();if(this.options.removeNSPrefix){let v=d.indexOf(":");v!==-1&&(d=d.substr(v+1))}this.options.transformTagName&&(d=this.options.transformTagName(d)),r&&(i=this.saveTextToParentTag(i,r,s));let c=s.substring(s.lastIndexOf(".")+1);if(d&&this.options.unpairedTags.indexOf(d)!==-1)throw new Error(`Unpaired tag can not be used as closing tag: </${d}>`);let g=0;c&&this.options.unpairedTags.indexOf(c)!==-1?(g=s.lastIndexOf(".",s.lastIndexOf(".")-1),this.tagsNodeStack.pop()):g=s.lastIndexOf("."),s=s.substring(0,g),r=this.tagsNodeStack.pop(),i="",n=o}else if(e[n+1]==="?"){let o=Di(e,n,!1,"?>");if(!o)throw new Error("Pi Tag is not closed.");if(i=this.saveTextToParentTag(i,r,s),!(this.options.ignoreDeclaration&&o.tagName==="?xml"||this.options.ignorePiTags)){let d=new ut(o.tagName);d.add(this.options.textNodeName,""),o.tagName!==o.tagExp&&o.attrExpPresent&&(d[":@"]=this.buildAttributesMap(o.tagExp,s,o.tagName)),this.addChild(r,d,s)}n=o.closeIndex+1}else if(e.substr(n+1,3)==="!--"){let o=Be(e,"-->",n+4,"Comment is not closed.");if(this.options.commentPropName){let d=e.substring(n+4,o-2);i=this.saveTextToParentTag(i,r,s),r.add(this.options.commentPropName,[{[this.options.textNodeName]:d}])}n=o}else if(e.substr(n+1,2)==="!D"){let o=up(e,n);this.docTypeEntities=o.entities,n=o.i}else if(e.substr(n+1,2)==="!["){let o=Be(e,"]]>",n,"CDATA is not closed.")-2,d=e.substring(n+9,o);if(i=this.saveTextToParentTag(i,r,s),this.options.cdataPropName)r.add(this.options.cdataPropName,[{[this.options.textNodeName]:d}]);else{let c=this.parseTextData(d,r.tagname,s,!0,!1,!0);c==null&&(c=""),r.add(this.options.textNodeName,c)}n=o+2}else{let o=Di(e,n,this.options.removeNSPrefix),d=o.tagName,c=o.rawTagName,g=o.tagExp,v=o.attrExpPresent,y=o.closeIndex;this.options.transformTagName&&(d=this.options.transformTagName(d)),r&&i&&r.tagname!=="!xml"&&(i=this.saveTextToParentTag(i,r,s,!1));let O=r;if(O&&this.options.unpairedTags.indexOf(O.tagname)!==-1&&(r=this.tagsNodeStack.pop(),s=s.substring(0,s.lastIndexOf("."))),d!==t.tagname&&(s+=s?"."+d:d),this.isItStopNode(this.options.stopNodes,s,d)){let p="";if(g.length>0&&g.lastIndexOf("/")===g.length-1)n=o.closeIndex;else if(this.options.unpairedTags.indexOf(d)!==-1)n=o.closeIndex;else{let _=this.readStopNodeData(e,c,y+1);if(!_)throw new Error(`Unexpected end of ${c}`);n=_.i,p=_.tagContent}let x=new ut(d);d!==g&&v&&(x[":@"]=this.buildAttributesMap(g,s,d)),p&&(p=this.parseTextData(p,d,s,!0,v,!0,!0)),s=s.substr(0,s.lastIndexOf(".")),x.add(this.options.textNodeName,p),this.addChild(r,x,s)}else{if(g.length>0&&g.lastIndexOf("/")===g.length-1){d[d.length-1]==="/"?(d=d.substr(0,d.length-1),s=s.substr(0,s.length-1),g=d):g=g.substr(0,g.length-1),this.options.transformTagName&&(d=this.options.transformTagName(d));let p=new ut(d);d!==g&&v&&(p[":@"]=this.buildAttributesMap(g,s,d)),this.addChild(r,p,s),s=s.substr(0,s.lastIndexOf("."))}else{let p=new ut(d);this.tagsNodeStack.push(r),d!==g&&v&&(p[":@"]=this.buildAttributesMap(g,s,d)),this.addChild(r,p,s),r=p}i="",n=y}}else i+=e[n];return t.child};function hp(e,t,r){let i=this.options.updateTag(t.tagname,r,t[":@"]);i===!1||(typeof i=="string"&&(t.tagname=i),e.addChild(t))}var vp=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){let r=this.docTypeEntities[t];e=e.replace(r.regx,r.val)}for(let t in this.lastEntities){let r=this.lastEntities[t];e=e.replace(r.regex,r.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){let r=this.htmlEntities[t];e=e.replace(r.regex,r.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function _p(e,t,r,i){return e&&(i===void 0&&(i=Object.keys(t.child).length===0),e=this.parseTextData(e,t.tagname,r,!1,t[":@"]?Object.keys(t[":@"]).length!==0:!1,i),e!==void 0&&e!==""&&t.add(this.options.textNodeName,e),e=""),e}function Ap(e,t,r){let i="*."+r;for(let s in e){let n=e[s];if(i===n||t===n)return!0}return!1}function yp(e,t,r=">"){let i,s="";for(let n=t;n<e.length;n++){let a=e[n];if(i)a===i&&(i="");else if(a==='"'||a==="'")i=a;else if(a===r[0])if(r[1]){if(e[n+1]===r[1])return{data:s,index:n}}else return{data:s,index:n};else a==="	"&&(a=" ");s+=a}}function Be(e,t,r,i){let s=e.indexOf(t,r);if(s===-1)throw new Error(i);return s+t.length-1}function Di(e,t,r,i=">"){let s=yp(e,t+1,i);if(!s)return;let n=s.data,a=s.index,o=n.search(/\s/),d=n,c=!0;o!==-1&&(d=n.substr(0,o).replace(/\s\s*$/,""),n=n.substr(o+1));let g=d;if(r){let v=d.indexOf(":");v!==-1&&(d=d.substr(v+1),c=d!==s.data.substr(v+1))}return{tagName:d,tagExp:n,closeIndex:a,attrExpPresent:c,rawTagName:g}}function bp(e,t,r){let i=r,s=1;for(;r<e.length;r++)if(e[r]==="<")if(e[r+1]==="/"){let n=Be(e,">",r,`${t} is not closed`);if(e.substring(r+2,n).trim()===t&&(s--,s===0))return{tagContent:e.substring(i,r),i:n};r=n}else if(e[r+1]==="?")r=Be(e,"?>",r+1,"StopNode is not closed.");else if(e.substr(r+1,3)==="!--")r=Be(e,"-->",r+3,"StopNode is not closed.");else if(e.substr(r+1,2)==="![")r=Be(e,"]]>",r,"StopNode is not closed.")-2;else{let n=Di(e,r,">");n&&((n&&n.tagName)===t&&n.tagExp[n.tagExp.length-1]!=="/"&&s++,r=n.closeIndex)}}function Mi(e,t,r){if(t&&typeof e=="string"){let i=e.trim();return i==="true"?!0:i==="false"?!1:dp(e,r)}else return Ci.isExist(e)?e:""}vo.exports=Ii});var bo=m(yo=>{"use strict";function Tp(e,t){return Ao(e,t)}function Ao(e,t,r){let i,s={};for(let n=0;n<e.length;n++){let a=e[n],o=Ep(a),d="";if(r===void 0?d=o:d=r+"."+o,o===t.textNodeName)i===void 0?i=a[o]:i+=""+a[o];else{if(o===void 0)continue;if(a[o]){let c=Ao(a[o],t,d),g=Op(c,t);a[":@"]?xp(c,a[":@"],d,t):Object.keys(c).length===1&&c[t.textNodeName]!==void 0&&!t.alwaysCreateTextNode?c=c[t.textNodeName]:Object.keys(c).length===0&&(t.alwaysCreateTextNode?c[t.textNodeName]="":c=""),s[o]!==void 0&&s.hasOwnProperty(o)?(Array.isArray(s[o])||(s[o]=[s[o]]),s[o].push(c)):t.isArray(o,d,g)?s[o]=[c]:s[o]=c}}}return typeof i=="string"?i.length>0&&(s[t.textNodeName]=i):i!==void 0&&(s[t.textNodeName]=i),s}function Ep(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let i=t[r];if(i!==":@")return i}}function xp(e,t,r,i){if(t){let s=Object.keys(t),n=s.length;for(let a=0;a<n;a++){let o=s[a];i.isArray(o,r+"."+o,!0,!0)?e[o]=[t[o]]:e[o]=t[o]}}}function Op(e,t){let{textNodeName:r}=t,i=Object.keys(e).length;return!!(i===0||i===1&&(e[r]||typeof e[r]=="boolean"||e[r]===0))}yo.prettify=Tp});var Eo=m((Im,To)=>{var{buildOptions:Rp}=po(),wp=_o(),{prettify:Np}=bo(),Sp=Ni(),Bi=class{constructor(t){this.externalEntities={},this.options=Rp(t)}parse(t,r){if(typeof t!="string")if(t.toString)t=t.toString();else throw new Error("XML data is accepted in String or Bytes[] form.");if(r){r===!0&&(r={});let n=Sp.validate(t,r);if(n!==!0)throw Error(`${n.err.msg}:${n.err.line}:${n.err.col}`)}let i=new wp(this.options);i.addExternalEntities(this.externalEntities);let s=i.parseXml(t);return this.options.preserveOrder||s===void 0?s:Np(s,this.options)}addEntity(t,r){if(r.indexOf("&")!==-1)throw new Error("Entity value can't have '&'");if(t.indexOf("&")!==-1||t.indexOf(";")!==-1)throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if(r==="&")throw new Error("An entity with value '&' is not permitted");this.externalEntities[t]=r}};To.exports=Bi});var No=m((Dm,wo)=>{var Pp=`
`;function Ip(e,t){let r="";return t.format&&t.indentBy.length>0&&(r=Pp),Oo(e,t,"",r)}function Oo(e,t,r,i){let s="",n=!1;for(let a=0;a<e.length;a++){let o=e[a],d=Dp(o);if(d===void 0)continue;let c="";if(r.length===0?c=d:c=`${r}.${d}`,d===t.textNodeName){let p=o[d];Mp(c,t)||(p=t.tagValueProcessor(d,p),p=Ro(p,t)),n&&(s+=i),s+=p,n=!1;continue}else if(d===t.cdataPropName){n&&(s+=i),s+=`<![CDATA[${o[d][0][t.textNodeName]}]]>`,n=!1;continue}else if(d===t.commentPropName){s+=i+`<!--${o[d][0][t.textNodeName]}-->`,n=!0;continue}else if(d[0]==="?"){let p=xo(o[":@"],t),x=d==="?xml"?"":i,_=o[d][0][t.textNodeName];_=_.length!==0?" "+_:"",s+=x+`<${d}${_}${p}?>`,n=!0;continue}let g=i;g!==""&&(g+=t.indentBy);let v=xo(o[":@"],t),y=i+`<${d}${v}`,O=Oo(o[d],t,c,g);t.unpairedTags.indexOf(d)!==-1?t.suppressUnpairedNode?s+=y+">":s+=y+"/>":(!O||O.length===0)&&t.suppressEmptyNode?s+=y+"/>":O&&O.endsWith(">")?s+=y+`>${O}${i}</${d}>`:(s+=y+">",O&&i!==""&&(O.includes("/>")||O.includes("</"))?s+=i+t.indentBy+O+i:s+=O,s+=`</${d}>`),n=!0}return s}function Dp(e){let t=Object.keys(e);for(let r=0;r<t.length;r++){let i=t[r];if(e.hasOwnProperty(i)&&i!==":@")return i}}function xo(e,t){let r="";if(e&&!t.ignoreAttributes)for(let i in e){if(!e.hasOwnProperty(i))continue;let s=t.attributeValueProcessor(i,e[i]);s=Ro(s,t),s===!0&&t.suppressBooleanAttributes?r+=` ${i.substr(t.attributeNamePrefix.length)}`:r+=` ${i.substr(t.attributeNamePrefix.length)}="${s}"`}return r}function Mp(e,t){e=e.substr(0,e.length-t.textNodeName.length-1);let r=e.substr(e.lastIndexOf(".")+1);for(let i in t.stopNodes)if(t.stopNodes[i]===e||t.stopNodes[i]==="*."+r)return!0;return!1}function Ro(e,t){if(e&&e.length>0&&t.processEntities)for(let r=0;r<t.entities.length;r++){let i=t.entities[r];e=e.replace(i.regex,i.val)}return e}wo.exports=Ip});var Po=m((Mm,So)=>{"use strict";var Cp=No(),Bp={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function Ae(e){this.options=Object.assign({},Bp,e),this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=kp),this.processTextOrObjNode=qp,this.options.format?(this.indentate=Vp,this.tagEndChar=`>
`,this.newLine=`
`):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}Ae.prototype.build=function(e){return this.options.preserveOrder?Cp(e,this.options):(Array.isArray(e)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(e={[this.options.arrayNodeName]:e}),this.j2x(e,0).val)};Ae.prototype.j2x=function(e,t){let r="",i="";for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s))if(typeof e[s]>"u")this.isAttribute(s)&&(i+="");else if(e[s]===null)this.isAttribute(s)?i+="":s[0]==="?"?i+=this.indentate(t)+"<"+s+"?"+this.tagEndChar:i+=this.indentate(t)+"<"+s+"/"+this.tagEndChar;else if(e[s]instanceof Date)i+=this.buildTextValNode(e[s],s,"",t);else if(typeof e[s]!="object"){let n=this.isAttribute(s);if(n)r+=this.buildAttrPairStr(n,""+e[s]);else if(s===this.options.textNodeName){let a=this.options.tagValueProcessor(s,""+e[s]);i+=this.replaceEntitiesValue(a)}else i+=this.buildTextValNode(e[s],s,"",t)}else if(Array.isArray(e[s])){let n=e[s].length,a="";for(let o=0;o<n;o++){let d=e[s][o];typeof d>"u"||(d===null?s[0]==="?"?i+=this.indentate(t)+"<"+s+"?"+this.tagEndChar:i+=this.indentate(t)+"<"+s+"/"+this.tagEndChar:typeof d=="object"?this.options.oneListGroup?a+=this.j2x(d,t+1).val:a+=this.processTextOrObjNode(d,s,t):a+=this.buildTextValNode(d,s,"",t))}this.options.oneListGroup&&(a=this.buildObjectNode(a,s,"",t)),i+=a}else if(this.options.attributesGroupName&&s===this.options.attributesGroupName){let n=Object.keys(e[s]),a=n.length;for(let o=0;o<a;o++)r+=this.buildAttrPairStr(n[o],""+e[s][n[o]])}else i+=this.processTextOrObjNode(e[s],s,t);return{attrStr:r,val:i}};Ae.prototype.buildAttrPairStr=function(e,t){return t=this.options.attributeValueProcessor(e,""+t),t=this.replaceEntitiesValue(t),this.options.suppressBooleanAttributes&&t==="true"?" "+e:" "+e+'="'+t+'"'};function qp(e,t,r){let i=this.j2x(e,r+1);return e[this.options.textNodeName]!==void 0&&Object.keys(e).length===1?this.buildTextValNode(e[this.options.textNodeName],t,i.attrStr,r):this.buildObjectNode(i.val,t,i.attrStr,r)}Ae.prototype.buildObjectNode=function(e,t,r,i){if(e==="")return t[0]==="?"?this.indentate(i)+"<"+t+r+"?"+this.tagEndChar:this.indentate(i)+"<"+t+r+this.closeTag(t)+this.tagEndChar;{let s="</"+t+this.tagEndChar,n="";return t[0]==="?"&&(n="?",s=""),(r||r==="")&&e.indexOf("<")===-1?this.indentate(i)+"<"+t+r+n+">"+e+s:this.options.commentPropName!==!1&&t===this.options.commentPropName&&n.length===0?this.indentate(i)+`<!--${e}-->`+this.newLine:this.indentate(i)+"<"+t+r+n+this.tagEndChar+e+this.indentate(i)+s}};Ae.prototype.closeTag=function(e){let t="";return this.options.unpairedTags.indexOf(e)!==-1?this.options.suppressUnpairedNode||(t="/"):this.options.suppressEmptyNode?t="/":t=`></${e}`,t};Ae.prototype.buildTextValNode=function(e,t,r,i){if(this.options.cdataPropName!==!1&&t===this.options.cdataPropName)return this.indentate(i)+`<![CDATA[${e}]]>`+this.newLine;if(this.options.commentPropName!==!1&&t===this.options.commentPropName)return this.indentate(i)+`<!--${e}-->`+this.newLine;if(t[0]==="?")return this.indentate(i)+"<"+t+r+"?"+this.tagEndChar;{let s=this.options.tagValueProcessor(t,e);return s=this.replaceEntitiesValue(s),s===""?this.indentate(i)+"<"+t+r+this.closeTag(t)+this.tagEndChar:this.indentate(i)+"<"+t+r+">"+s+"</"+t+this.tagEndChar}};Ae.prototype.replaceEntitiesValue=function(e){if(e&&e.length>0&&this.options.processEntities)for(let t=0;t<this.options.entities.length;t++){let r=this.options.entities[t];e=e.replace(r.regex,r.val)}return e};function Vp(e){return this.options.indentBy.repeat(e)}function kp(e){return e.startsWith(this.options.attributeNamePrefix)&&e!==this.options.textNodeName?e.substr(this.attrPrefixLen):!1}So.exports=Ae});var Do=m((Cm,Io)=>{"use strict";var Fp=Ni(),Lp=Eo(),jp=Po();Io.exports={XMLParser:Lp,XMLValidator:Fp,XMLBuilder:jp}});function ue(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var Uo=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return w(t)},e.prototype.toString=function(){return"None"},e}(),l=new Uo;Object.freeze(l);var Go=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return u(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return T(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(ue(this.value),")")},e.EMPTY=new e(void 0),e}(),u=Go,de;(function(e){function t(){for(var s=[],n=0;n<arguments.length;n++)s[n]=arguments[n];for(var a=[],o=0,d=s;o<d.length;o++){var c=d[o];if(c.isSome())a.push(c.value);else return c}return u(a)}e.all=t;function r(){for(var s=[],n=0;n<arguments.length;n++)s[n]=arguments[n];for(var a=0,o=s;a<o.length;a++){var d=o[a];return d.isSome(),d}return l}e.any=r;function i(s){return s instanceof u||s===l}e.isOption=i})(de||(de={}));var Wo=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(ue(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(ue(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new w(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return l},e.prototype.toString=function(){return"Err(".concat(ue(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new xr(this)},e.EMPTY=new e(void 0),e}();var w=Wo,Ko=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(ue(this.value)),{cause:this.value})},e.prototype.map=function(t){return new T(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return u(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(ue(this.value),")")},e.prototype.toAsyncResult=function(){return new xr(this)},e.EMPTY=new e(void 0),e}();var T=Ko,pe;(function(e){function t(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];for(var d=[],c=0,g=a;c<g.length;c++){var v=g[c];if(v.isOk())d.push(v.value);else return v}return new T(d)}e.all=t;function r(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];for(var d=[],c=0,g=a;c<g.length;c++){var v=g[c];if(v.isOk())return v;d.push(v.error)}return new w(d)}e.any=r;function i(a){try{return new T(a())}catch(o){return new w(o)}}e.wrap=i;function s(a){try{return a().then(function(o){return new T(o)}).catch(function(o){return new w(o)})}catch(o){return Promise.resolve(new w(o))}}e.wrapAsync=s;function n(a){return a instanceof w||a instanceof T}e.isResult=n})(pe||(pe={}));var Vi=function(e,t,r,i){function s(n){return n instanceof r?n:new r(function(a){a(n)})}return new(r||(r=Promise))(function(n,a){function o(g){try{c(i.next(g))}catch(v){a(v)}}function d(g){try{c(i.throw(g))}catch(v){a(v)}}function c(g){g.done?n(g.value):s(g.value).then(o,d)}c((i=i.apply(e,t||[])).next())})},ki=function(e,t){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},i,s,n,a;return a={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function o(c){return function(g){return d([c,g])}}function d(c){if(i)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(r=0)),r;)try{if(i=1,s&&(n=c[0]&2?s.return:c[0]?s.throw||((n=s.return)&&n.call(s),0):s.next)&&!(n=n.call(s,c[1])).done)return n;switch(s=0,n&&(c=[c[0]&2,n.value]),c[0]){case 0:case 1:n=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,s=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(n=r.trys,!(n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){r.label=c[1];break}if(c[0]===6&&r.label<n[1]){r.label=n[1],n=c;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(c);break}n[2]&&r.ops.pop(),r.trys.pop();continue}c=t.call(e,r)}catch(g){c=[6,g],s=0}finally{i=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}},xr=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(i){return Vi(r,void 0,void 0,function(){var s;return ki(this,function(n){return i.isErr()?[2,i]:(s=t(i.value),[2,s instanceof e?s.promise:s])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(i){return Vi(r,void 0,void 0,function(){var s;return ki(this,function(n){switch(n.label){case 0:return i.isErr()?[2,i]:(s=T,[4,t(i.value)]);case 1:return[2,s.apply(void 0,[n.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();var Or=0,Rr=0,Li="none",Fi="\x1B[0m",Jo="\x1B[31m",Yo="\x1B[32m";function ji(){Or>0?(console.error(`${Jo} ======= ${Or} out of ${Rr} tests failed.${Fi}`),process.exit(1)):console.log(`${Yo} ======= All ${Rr} tests passed.${Fi}`)}function I(e){Li=e}function f(e,t){function r(s,n){if(typeof s!=typeof n)return w(`different type (${s}, ${n})`);if(s===n)return T(!0);if(s===null||n===null)return w("null value was unexpected");if(s instanceof Set&&n instanceof Set)return r([...s.entries()],[...n.entries()]);if(s instanceof Map&&n instanceof Map)return r([...s.entries()],[...n.entries()]);if(pe.isResult(s)&&pe.isResult(n))return s.isOk()&&n.isOk()?r(s.unwrap(),n.unwrap()):s.isErr()&&n.isErr()?r(s.unwrapErr(),n.unwrapErr()):w("Err and Ok result");if(de.isOption(s)&&de.isOption(n))return s.isSome()&&n.isSome()?r(s.unwrap(),n.unwrap()):s.isNone()&&n.isNone()?T(!0):w("None and Some option");if(Array.isArray(s)&&Array.isArray(n)){if(s.length!=n.length)return w("Array of different size");for(let a=0;a<s.length;a++){let o=r(s[a],n[a]);if(o.isErr())return o.mapErr(d=>`Item[${a}] different: ${d}`)}return T(!0)}if(typeof s=="boolean"||typeof s=="number"||typeof s=="string")return s==n?T(!0):w(`different value: ${s} != ${n}`);if(typeof s=="object"){let a=s,o=n;for(let d of Object.keys(a))if(d in o){let c=a[d],g=o[d];if(typeof c<"u"&&typeof g<"u"){let v=r(c,g);if(v.isErr())return v.mapErr(y=>`Item["${d}"] different: ${y}`)}else if(c!=g)return w(`Item["${d}"] different`)}else return w(`Missing key: ${d}`);return T(!0)}return w("Unexpected value type")}let i=r(e,t);i.isErr()&&(Or++,console.error(`Context: ${Li}:`),console.error(i.unwrapErr())),Rr++}var Hs=Er(Vs(),1);function k(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return u(r);return l},count:function(t){return this.reduce((r,i)=>(t(i)&&r++,r),0)},reduce:function(t,r){let i=r;for(let s of this)i=t(i,s);return i},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>u(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?u(r):l)},enumerate:function(){let t=this;return k(function*(){let r=0;for(let i of t)yield[r,i],r++})()},filterMap:function(t){let r=this;return k(function*(){for(let i of r){let s=t(i);s.isSome()&&(yield s.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return k(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return k(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return k(function*(){for(let t of e)yield t})()});function ks(){I("iterators.mts");let e=t=>t%2==1;f(["a","b"].as_iter().find(t=>!1),l),f(["a","b"].as_iter().find(t=>t=="b"),u("b")),f([..."foobar"].as_iter().filter(t=>t!="o").toArray().join(""),"fbar"),f([..."foo"].as_iter().map(t=>t+"x").toArray().join(""),"fxoxox"),f([1,3,5].as_iter().every(e),!0),f([2,4,6].as_iter().every(e),!1),f([2,4,6].as_iter().any(e),!1),f([4,6,7].as_iter().any(e),!0),f([4,6,7].as_iter().reduce((t,r)=>t+r,0),17),f([4,6,7,1,0].as_iter().count(e),2)}var He=/.^/,dr={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:He,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:He,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:He,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:He,defacto_container:"Mp4"}},pr={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:He,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:He,defacto_container:"Mp4"}},Qe=k(function*(){for(let e of Object.keys(dr))yield dr[e]}),Ue=k(function*(){for(let e of Object.keys(pr))yield pr[e]});function R(e){return dr[e]}function M(e){return pr[e]}function tt(e){return typeof e=="string"&&e in dr?u(e):l}function Fs(e){return typeof e=="string"&&e in pr?u(e):l}function Ls(){I("codecs.mts"),f(tt("foo"),l),f(tt("H264"),u("H264")),f(Fs("foo"),l),f(Fs("AAC"),u("AAC"))}var cr={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:Qe().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:Ue().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:l,video:l},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:u("MP3"),video:u("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:u("AAC"),video:l},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:u("FLAC"),video:l},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:u("MP3"),video:u("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:l,video:l},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:u("Wav"),video:l},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:l,video:l},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},js=k(function*(){for(let e of Object.keys(cr))yield e}),_i=k(function*(){for(let e of js())yield cr[e]});function it(e){return typeof e=="string"&&e in cr?u(e):l}function rt(e){for(let t of _i()){let i=t.supported_video_codecs.length==0?"audio_only":"whole";if(t.extension===e)return u([t,i]);if(t.audio_only_extension&&t.audio_only_extension===e)return u([t,"audio_only"]);if(t.other_extensions){for(let s of t.other_extensions)if(s==e)return u([t,i])}}return l}function E(e){return cr[e]}function Xs(){I("containers.mts"),f(js().toArray().as_iter().toArray(),["Mp4","Mkv","WebM","M2TS","MP2T","Flash","M4V","M4A","Flac","Mpeg","Ogg","Wav","3gp","QuickTime"]),f(it("foo"),l),f(it("Mp4"),u("Mp4")),f(rt("webm").map(([e,t])=>e.name),u("WebM")),f(rt("foo").map(([e,t])=>e.name),l),f(rt("mp3").map(([e,t])=>[e.name,t]),u(["Mp4","audio_only"])),f(rt("mp4").map(([e,t])=>[e.name,t]),u(["Mp4","whole"])),f(rt("aac").map(([e,t])=>[e.name,t]),u(["M4A","audio_only"]))}function se(e,t,r){let i=t.andThen(a=>a.defacto_codecs.audio),s=t.andThen(a=>a.defacto_codecs.video),n=e.split(",");if(n.length>1){let a=Qe().find(y=>y.mimetype.test(n[0])),o=Qe().find(y=>y.mimetype.test(n[1])),d=Ue().find(y=>y.mimetype.test(n[0])),c=Ue().find(y=>y.mimetype.test(n[1])),g=i.unwrapOr("unknown"),v=s.unwrapOr("unknown");return{audio:d.or(c).map(y=>y.name).unwrapOr(g),video:a.or(o).map(y=>y.name).unwrapOr(v)}}else{let a=Qe().find(c=>c.mimetype.test(n[0])).map(c=>c.name),o=Ue().find(c=>c.mimetype.test(n[0])).map(c=>c.name);return o.isSome()?{video:!1,audio:o.unwrap()}:a.isSome()?{video:a.unwrap(),audio:!1}:r.map(c=>c==="audio").unwrapOr(!1)?{video:!1,audio:o.or(i).unwrapOr("unknown")}:{video:a.or(s).unwrapOr("unknown"),audio:!1}}}function S(e){let t;try{t=(0,Hs.mediaTypeFrom)(e).parse()}catch(o){return w("parse error:"+o)}let r=l;t.type=="video"&&(r=u("video")),t.type=="audio"&&(r=u("audio"));let i=_i().find(o=>o.mimetype.test(t.subtype));if(i.isNone())return w(`Unknown container (parsed from ${t.subtype})`);let s=t.parameters?.codecs??"",n=se(s,i,r),a=i.unwrap();return T({container:a.name,av_codecs:n})}function Qs(){I("mimetype.mts"),f(S("").isErr(),!0),f(se("vorbis",l,l),{video:!1,audio:"Vorbis"}),f(se("vp8, vorbis",l,l),{video:"VP8",audio:"Vorbis"}),f(se("vp9, xx",l,l),{video:"VP9",audio:"unknown"}),f(se("vp8",l,l),{video:"VP8",audio:!1}),f(se("foobar",l,l),{audio:!1,video:"unknown"}),f(se("foo,bar",l,l),{video:"unknown",audio:"unknown"}),f(S("video/flv").isOk(),!0),f(S("video/flv"),S("video/x-flv")),f(S('video/webm; codecs="vp8, vorbis"').unwrap(),{container:"WebM",av_codecs:{audio:"Vorbis",video:"VP8"}}),f(S("video/x-flac").unwrap(),{container:"Flac",av_codecs:{video:"unknown",audio:!1}}),f(S("audio/mpeg").unwrap(),{container:"Mpeg",av_codecs:{video:!1,audio:"MP3"}}),f(S("audio/x-matroska").unwrap(),{container:"Mkv",av_codecs:{video:!1,audio:"unknown"}}),f(S("video/matroska").unwrap(),{container:"Mkv",av_codecs:{video:"unknown",audio:!1}}),f(S('audio/ogg; codecs="flac"').unwrap(),{container:"Ogg",av_codecs:{video:!1,audio:"FLAC"}}),f(S('audio/ogg; codecs="vorbis"').unwrap(),{container:"Ogg",av_codecs:{video:!1,audio:"Vorbis"}}),f(S('audio/ogg; codecs="opus"').unwrap(),{container:"Ogg",av_codecs:{video:!1,audio:"Opus"}}),f(S('audio/wav; codecs="1"').unwrap(),{container:"Wav",av_codecs:{video:!1,audio:"Wav"}}),f(S('audio/webm; codecs="vorbis"').unwrap(),{container:"WebM",av_codecs:{video:!1,audio:"Vorbis"}}),f(S('audio/webm; codecs="opus"').unwrap(),{container:"WebM",av_codecs:{video:!1,audio:"Opus"}}),f(S('audio/mp4; codecs="mp4a.40.2"').unwrap(),{container:"Mp4",av_codecs:{video:!1,audio:"AAC"}}),f(S('video/mp4; codecs="flac"').unwrap(),{container:"Mp4",av_codecs:{video:!1,audio:"FLAC"}}),f(S('video/ogg; codecs="theora"').unwrap(),{container:"Ogg",av_codecs:{audio:!1,video:"Theora"}}),f(S('video/ogg; codecs="opus"').unwrap(),{container:"Ogg",av_codecs:{video:!1,audio:"Opus"}}),f(S('video/webm; codecs="vp9, opus"').unwrap(),{container:"WebM",av_codecs:{audio:"Opus",video:"VP9"}}),f(S('video/webm; codecs="vp8, vorbis"').unwrap(),{container:"WebM",av_codecs:{audio:"Vorbis",video:"VP8"}})}var Td="240",Us={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};function lr(e,t){let r=parseInt(e),i=parseInt(t);return r<i}function Ge(e,t){let r=parseInt(e),i=parseInt(t);return r>i}var nt=k(function*(){for(let e of Object.keys(Us))yield e}),Rf=k(function*(){for(let e of nt())yield Us[e]});function Ai(e){for(let t of nt())if(e.includes(t))return u(t);return l}function he(e){let t=nt().map(r=>parseInt(r)).toArray();t.sort((r,i)=>r-i),t.reverse();for(let r of t)if(e>=r)return r.toString();return Td}function oe(e){if(typeof e=="string")return nt().find(t=>t==e);if(typeof e=="number"){let t=e.toString();return oe(t)}return l}function Gs(){I("video_qualities.mts"),f(lr("480","1080"),!0),f(lr("480","240"),!1),f(Ge("480","240"),!0),f(Ge("2160","2160"),!1),f(nt().toArray().as_iter().toArray(),["240","360","480","720","1080","1440","2160","4320"]),f(Ai("1080p"),u("1080")),f(Ai("240p"),u("240")),f(Ai("foobar"),l),f(he(1080),"1080"),f(he(500),"480"),f(he(100),"240"),f(oe(480),u("480")),f(oe(100),l),f(oe("100"),l),f(oe(void 0),l)}function Ws(e,t){let r=!!e.audio,i=!!t.audio,s=!!e.video,n=!!t.video;return r===i&&s&&n}function We(e,t,r){if(e.audio&&e.video)return{audio:t(e.audio),video:r(e.video)};if(e.video)return{video:r(e.video),audio:!1};if(e.audio)return{audio:t(e.audio),video:!1};throw"unreachable"}var fr=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(i,s){this.listeners[i]||(this.listeners[i]=[]),this.listeners[i].push(s)},t.off=function(i,s){if(!this.listeners[i])return!1;var n=this.listeners[i].indexOf(s);return this.listeners[i]=this.listeners[i].slice(0),this.listeners[i].splice(n,1),n>-1},t.trigger=function(i){var s=this.listeners[i];if(s)if(arguments.length===2)for(var n=s.length,a=0;a<n;++a)s[a].call(this,arguments[1]);else for(var o=Array.prototype.slice.call(arguments,1),d=s.length,c=0;c<d;++c)s[c].apply(this,o)},t.dispose=function(){this.listeners={}},t.pipe=function(i){this.on("data",function(s){i.push(s)})},e}();function ve(){return ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},ve.apply(this,arguments)}var yi=Er(Js()),xd=function(t){return yi.default.atob?yi.default.atob(t):Buffer.from(t,"base64").toString("binary")};function bi(e){for(var t=xd(e),r=new Uint8Array(t.length),i=0;i<t.length;i++)r[i]=t.charCodeAt(i);return r}var xi=class extends fr{constructor(){super(),this.buffer=""}push(t){let r;for(this.buffer+=t,r=this.buffer.indexOf(`
`);r>-1;r=this.buffer.indexOf(`
`))this.trigger("data",this.buffer.substring(0,r)),this.buffer=this.buffer.substring(r+1)}},Od="	",Ti=function(e){let t=/([0-9.]*)?@?([0-9.]*)?/.exec(e||""),r={};return t[1]&&(r.length=parseInt(t[1],10)),t[2]&&(r.offset=parseInt(t[2],10)),r},Rd=function(){let r="(?:"+"[^=]*"+")=(?:"+'"[^"]*"|[^,]*'+")";return new RegExp("(?:^|,)("+r+")")},H=function(e){let t={};if(!e)return t;let r=e.split(Rd()),i=r.length,s;for(;i--;)r[i]!==""&&(s=/([^=]*)=(.*)/.exec(r[i]).slice(1),s[0]=s[0].replace(/^\s+|\s+$/g,""),s[1]=s[1].replace(/^\s+|\s+$/g,""),s[1]=s[1].replace(/^['"](.*)['"]$/g,"$1"),t[s[0]]=s[1]);return t},Oi=class extends fr{constructor(){super(),this.customParsers=[],this.tagMappers=[]}push(t){let r,i;if(t=t.trim(),t.length===0)return;if(t[0]!=="#"){this.trigger("data",{type:"uri",uri:t});return}this.tagMappers.reduce((n,a)=>{let o=a(t);return o===t?n:n.concat([o])},[t]).forEach(n=>{for(let a=0;a<this.customParsers.length;a++)if(this.customParsers[a].call(this,n))return;if(n.indexOf("#EXT")!==0){this.trigger("data",{type:"comment",text:n.slice(1)});return}if(n=n.replace("\r",""),r=/^#EXTM3U/.exec(n),r){this.trigger("data",{type:"tag",tagType:"m3u"});return}if(r=/^#EXTINF:([0-9\.]*)?,?(.*)?$/.exec(n),r){i={type:"tag",tagType:"inf"},r[1]&&(i.duration=parseFloat(r[1])),r[2]&&(i.title=r[2]),this.trigger("data",i);return}if(r=/^#EXT-X-TARGETDURATION:([0-9.]*)?/.exec(n),r){i={type:"tag",tagType:"targetduration"},r[1]&&(i.duration=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-VERSION:([0-9.]*)?/.exec(n),r){i={type:"tag",tagType:"version"},r[1]&&(i.version=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-MEDIA-SEQUENCE:(\-?[0-9.]*)?/.exec(n),r){i={type:"tag",tagType:"media-sequence"},r[1]&&(i.number=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-DISCONTINUITY-SEQUENCE:(\-?[0-9.]*)?/.exec(n),r){i={type:"tag",tagType:"discontinuity-sequence"},r[1]&&(i.number=parseInt(r[1],10)),this.trigger("data",i);return}if(r=/^#EXT-X-PLAYLIST-TYPE:(.*)?$/.exec(n),r){i={type:"tag",tagType:"playlist-type"},r[1]&&(i.playlistType=r[1]),this.trigger("data",i);return}if(r=/^#EXT-X-BYTERANGE:(.*)?$/.exec(n),r){i=ve(Ti(r[1]),{type:"tag",tagType:"byterange"}),this.trigger("data",i);return}if(r=/^#EXT-X-ALLOW-CACHE:(YES|NO)?/.exec(n),r){i={type:"tag",tagType:"allow-cache"},r[1]&&(i.allowed=!/NO/.test(r[1])),this.trigger("data",i);return}if(r=/^#EXT-X-MAP:(.*)$/.exec(n),r){if(i={type:"tag",tagType:"map"},r[1]){let a=H(r[1]);a.URI&&(i.uri=a.URI),a.BYTERANGE&&(i.byterange=Ti(a.BYTERANGE))}this.trigger("data",i);return}if(r=/^#EXT-X-STREAM-INF:(.*)$/.exec(n),r){if(i={type:"tag",tagType:"stream-inf"},r[1]){if(i.attributes=H(r[1]),i.attributes.RESOLUTION){let a=i.attributes.RESOLUTION.split("x"),o={};a[0]&&(o.width=parseInt(a[0],10)),a[1]&&(o.height=parseInt(a[1],10)),i.attributes.RESOLUTION=o}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["FRAME-RATE"]&&(i.attributes["FRAME-RATE"]=parseFloat(i.attributes["FRAME-RATE"])),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}this.trigger("data",i);return}if(r=/^#EXT-X-MEDIA:(.*)$/.exec(n),r){i={type:"tag",tagType:"media"},r[1]&&(i.attributes=H(r[1])),this.trigger("data",i);return}if(r=/^#EXT-X-ENDLIST/.exec(n),r){this.trigger("data",{type:"tag",tagType:"endlist"});return}if(r=/^#EXT-X-DISCONTINUITY/.exec(n),r){this.trigger("data",{type:"tag",tagType:"discontinuity"});return}if(r=/^#EXT-X-PROGRAM-DATE-TIME:(.*)$/.exec(n),r){i={type:"tag",tagType:"program-date-time"},r[1]&&(i.dateTimeString=r[1],i.dateTimeObject=new Date(r[1])),this.trigger("data",i);return}if(r=/^#EXT-X-KEY:(.*)$/.exec(n),r){i={type:"tag",tagType:"key"},r[1]&&(i.attributes=H(r[1]),i.attributes.IV&&(i.attributes.IV.substring(0,2).toLowerCase()==="0x"&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),this.trigger("data",i);return}if(r=/^#EXT-X-START:(.*)$/.exec(n),r){i={type:"tag",tagType:"start"},r[1]&&(i.attributes=H(r[1]),i.attributes["TIME-OFFSET"]=parseFloat(i.attributes["TIME-OFFSET"]),i.attributes.PRECISE=/YES/.test(i.attributes.PRECISE)),this.trigger("data",i);return}if(r=/^#EXT-X-CUE-OUT-CONT:(.*)?$/.exec(n),r){i={type:"tag",tagType:"cue-out-cont"},r[1]?i.data=r[1]:i.data="",this.trigger("data",i);return}if(r=/^#EXT-X-CUE-OUT:(.*)?$/.exec(n),r){i={type:"tag",tagType:"cue-out"},r[1]?i.data=r[1]:i.data="",this.trigger("data",i);return}if(r=/^#EXT-X-CUE-IN:(.*)?$/.exec(n),r){i={type:"tag",tagType:"cue-in"},r[1]?i.data=r[1]:i.data="",this.trigger("data",i);return}if(r=/^#EXT-X-SKIP:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"skip"},i.attributes=H(r[1]),i.attributes.hasOwnProperty("SKIPPED-SEGMENTS")&&(i.attributes["SKIPPED-SEGMENTS"]=parseInt(i.attributes["SKIPPED-SEGMENTS"],10)),i.attributes.hasOwnProperty("RECENTLY-REMOVED-DATERANGES")&&(i.attributes["RECENTLY-REMOVED-DATERANGES"]=i.attributes["RECENTLY-REMOVED-DATERANGES"].split(Od)),this.trigger("data",i);return}if(r=/^#EXT-X-PART:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"part"},i.attributes=H(r[1]),["DURATION"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=parseFloat(i.attributes[a]))}),["INDEPENDENT","GAP"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=/YES/.test(i.attributes[a]))}),i.attributes.hasOwnProperty("BYTERANGE")&&(i.attributes.byterange=Ti(i.attributes.BYTERANGE)),this.trigger("data",i);return}if(r=/^#EXT-X-SERVER-CONTROL:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"server-control"},i.attributes=H(r[1]),["CAN-SKIP-UNTIL","PART-HOLD-BACK","HOLD-BACK"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=parseFloat(i.attributes[a]))}),["CAN-SKIP-DATERANGES","CAN-BLOCK-RELOAD"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=/YES/.test(i.attributes[a]))}),this.trigger("data",i);return}if(r=/^#EXT-X-PART-INF:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"part-inf"},i.attributes=H(r[1]),["PART-TARGET"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=parseFloat(i.attributes[a]))}),this.trigger("data",i);return}if(r=/^#EXT-X-PRELOAD-HINT:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"preload-hint"},i.attributes=H(r[1]),["BYTERANGE-START","BYTERANGE-LENGTH"].forEach(function(a){if(i.attributes.hasOwnProperty(a)){i.attributes[a]=parseInt(i.attributes[a],10);let o=a==="BYTERANGE-LENGTH"?"length":"offset";i.attributes.byterange=i.attributes.byterange||{},i.attributes.byterange[o]=i.attributes[a],delete i.attributes[a]}}),this.trigger("data",i);return}if(r=/^#EXT-X-RENDITION-REPORT:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"rendition-report"},i.attributes=H(r[1]),["LAST-MSN","LAST-PART"].forEach(function(a){i.attributes.hasOwnProperty(a)&&(i.attributes[a]=parseInt(i.attributes[a],10))}),this.trigger("data",i);return}if(r=/^#EXT-X-DATERANGE:(.*)$/.exec(n),r&&r[1]){i={type:"tag",tagType:"daterange"},i.attributes=H(r[1]),["ID","CLASS"].forEach(function(o){i.attributes.hasOwnProperty(o)&&(i.attributes[o]=String(i.attributes[o]))}),["START-DATE","END-DATE"].forEach(function(o){i.attributes.hasOwnProperty(o)&&(i.attributes[o]=new Date(i.attributes[o]))}),["DURATION","PLANNED-DURATION"].forEach(function(o){i.attributes.hasOwnProperty(o)&&(i.attributes[o]=parseFloat(i.attributes[o]))}),["END-ON-NEXT"].forEach(function(o){i.attributes.hasOwnProperty(o)&&(i.attributes[o]=/YES/i.test(i.attributes[o]))}),["SCTE35-CMD"," SCTE35-OUT","SCTE35-IN"].forEach(function(o){i.attributes.hasOwnProperty(o)&&(i.attributes[o]=i.attributes[o].toString(16))});let a=/^X-([A-Z]+-)+[A-Z]+$/;for(let o in i.attributes){if(!a.test(o))continue;let d=/[0-9A-Fa-f]{6}/g.test(i.attributes[o]),c=/^\d+(\.\d+)?$/.test(i.attributes[o]);i.attributes[o]=d?i.attributes[o].toString(16):c?parseFloat(i.attributes[o]):String(i.attributes[o])}this.trigger("data",i);return}if(r=/^#EXT-X-INDEPENDENT-SEGMENTS/.exec(n),r){this.trigger("data",{type:"tag",tagType:"independent-segments"});return}if(r=/^#EXT-X-CONTENT-STEERING:(.*)$/.exec(n),r){i={type:"tag",tagType:"content-steering"},i.attributes=H(r[1]),this.trigger("data",i);return}this.trigger("data",{type:"tag",data:n.slice(4)})})}addParser({expression:t,customType:r,dataParser:i,segment:s}){typeof i!="function"&&(i=n=>n),this.customParsers.push(n=>{if(t.exec(n))return this.trigger("data",{type:"custom",data:i(n),customType:r,segment:s}),!0})}addTagMapper({expression:t,map:r}){let i=s=>t.test(s)?r(s):s;this.tagMappers.push(i)}},wd=e=>e.toLowerCase().replace(/-(\w)/g,t=>t[1].toUpperCase()),_e=function(e){let t={};return Object.keys(e).forEach(function(r){t[wd(r)]=e[r]}),t},Ei=function(e){let{serverControl:t,targetDuration:r,partTargetDuration:i}=e;if(!t)return;let s="#EXT-X-SERVER-CONTROL",n="holdBack",a="partHoldBack",o=r&&r*3,d=i&&i*2;r&&!t.hasOwnProperty(n)&&(t[n]=o,this.trigger("info",{message:`${s} defaulting HOLD-BACK to targetDuration * 3 (${o}).`})),o&&t[n]<o&&(this.trigger("warn",{message:`${s} clamping HOLD-BACK (${t[n]}) to targetDuration * 3 (${o})`}),t[n]=o),i&&!t.hasOwnProperty(a)&&(t[a]=i*3,this.trigger("info",{message:`${s} defaulting PART-HOLD-BACK to partTargetDuration * 3 (${t[a]}).`})),i&&t[a]<d&&(this.trigger("warn",{message:`${s} clamping PART-HOLD-BACK (${t[a]}) to partTargetDuration * 2 (${d}).`}),t[a]=d)},mr=class extends fr{constructor(){super(),this.lineStream=new xi,this.parseStream=new Oi,this.lineStream.pipe(this.parseStream),this.lastProgramDateTime=null;let t=this,r=[],i={},s,n,a=!1,o=function(){},d={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},c="urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",g=0;this.manifest={allowCache:!0,discontinuityStarts:[],dateRanges:[],segments:[]};let v=0,y=0,O={};this.on("end",()=>{i.uri||!i.parts&&!i.preloadHints||(!i.map&&s&&(i.map=s),!i.key&&n&&(i.key=n),!i.timeline&&typeof g=="number"&&(i.timeline=g),this.manifest.preloadSegment=i)}),this.parseStream.on("data",function(p){let x,_;({tag(){({version(){p.version&&(this.manifest.version=p.version)},"allow-cache"(){this.manifest.allowCache=p.allowed,"allowed"in p||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange(){let A={};"length"in p&&(i.byterange=A,A.length=p.length,"offset"in p||(p.offset=v)),"offset"in p&&(i.byterange=A,A.offset=p.offset),v=A.offset+A.length},endlist(){this.manifest.endList=!0},inf(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),p.title&&(i.title=p.title),p.duration>0&&(i.duration=p.duration),p.duration===0&&(i.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=r},key(){if(!p.attributes){this.trigger("warn",{message:"ignoring key declaration without attribute list"});return}if(p.attributes.METHOD==="NONE"){n=null;return}if(!p.attributes.URI){this.trigger("warn",{message:"ignoring key declaration without URI"});return}if(p.attributes.KEYFORMAT==="com.apple.streamingkeydelivery"){this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.apple.fps.1_0"]={attributes:p.attributes};return}if(p.attributes.KEYFORMAT==="com.microsoft.playready"){this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.microsoft.playready"]={uri:p.attributes.URI};return}if(p.attributes.KEYFORMAT===c){if(["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(p.attributes.METHOD)===-1){this.trigger("warn",{message:"invalid key method provided for Widevine"});return}if(p.attributes.METHOD==="SAMPLE-AES-CENC"&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),p.attributes.URI.substring(0,23)!=="data:text/plain;base64,"){this.trigger("warn",{message:"invalid key URI provided for Widevine"});return}if(!(p.attributes.KEYID&&p.attributes.KEYID.substring(0,2)==="0x")){this.trigger("warn",{message:"invalid key ID provided for Widevine"});return}this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.widevine.alpha"]={attributes:{schemeIdUri:p.attributes.KEYFORMAT,keyId:p.attributes.KEYID.substring(2)},pssh:bi(p.attributes.URI.split(",")[1])};return}p.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),n={method:p.attributes.METHOD||"AES-128",uri:p.attributes.URI},typeof p.attributes.IV<"u"&&(n.iv=p.attributes.IV)},"media-sequence"(){if(!isFinite(p.number)){this.trigger("warn",{message:"ignoring invalid media sequence: "+p.number});return}this.manifest.mediaSequence=p.number},"discontinuity-sequence"(){if(!isFinite(p.number)){this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+p.number});return}this.manifest.discontinuitySequence=p.number,g=p.number},"playlist-type"(){if(!/VOD|EVENT/.test(p.playlistType)){this.trigger("warn",{message:"ignoring unknown playlist type: "+p.playlist});return}this.manifest.playlistType=p.playlistType},map(){s={},p.uri&&(s.uri=p.uri),p.byterange&&(s.byterange=p.byterange),n&&(s.key=n)},"stream-inf"(){if(this.manifest.playlists=r,this.manifest.mediaGroups=this.manifest.mediaGroups||d,!p.attributes){this.trigger("warn",{message:"ignoring empty stream-inf attributes"});return}i.attributes||(i.attributes={}),ve(i.attributes,p.attributes)},media(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||d,!(p.attributes&&p.attributes.TYPE&&p.attributes["GROUP-ID"]&&p.attributes.NAME)){this.trigger("warn",{message:"ignoring incomplete or missing media group"});return}let A=this.manifest.mediaGroups[p.attributes.TYPE];A[p.attributes["GROUP-ID"]]=A[p.attributes["GROUP-ID"]]||{},x=A[p.attributes["GROUP-ID"]],_={default:/yes/i.test(p.attributes.DEFAULT)},_.default?_.autoselect=!0:_.autoselect=/yes/i.test(p.attributes.AUTOSELECT),p.attributes.LANGUAGE&&(_.language=p.attributes.LANGUAGE),p.attributes.URI&&(_.uri=p.attributes.URI),p.attributes["INSTREAM-ID"]&&(_.instreamId=p.attributes["INSTREAM-ID"]),p.attributes.CHARACTERISTICS&&(_.characteristics=p.attributes.CHARACTERISTICS),p.attributes.FORCED&&(_.forced=/yes/i.test(p.attributes.FORCED)),x[p.attributes.NAME]=_},discontinuity(){g+=1,i.discontinuity=!0,this.manifest.discontinuityStarts.push(r.length)},"program-date-time"(){typeof this.manifest.dateTimeString>"u"&&(this.manifest.dateTimeString=p.dateTimeString,this.manifest.dateTimeObject=p.dateTimeObject),i.dateTimeString=p.dateTimeString,i.dateTimeObject=p.dateTimeObject;let{lastProgramDateTime:A}=this;this.lastProgramDateTime=new Date(p.dateTimeString).getTime(),A===null&&this.manifest.segments.reduceRight((h,N)=>(N.programDateTime=h-N.duration*1e3,N.programDateTime),this.lastProgramDateTime)},targetduration(){if(!isFinite(p.duration)||p.duration<0){this.trigger("warn",{message:"ignoring invalid target duration: "+p.duration});return}this.manifest.targetDuration=p.duration,Ei.call(this,this.manifest)},start(){if(!p.attributes||isNaN(p.attributes["TIME-OFFSET"])){this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"});return}this.manifest.start={timeOffset:p.attributes["TIME-OFFSET"],precise:p.attributes.PRECISE}},"cue-out"(){i.cueOut=p.data},"cue-out-cont"(){i.cueOutCont=p.data},"cue-in"(){i.cueIn=p.data},skip(){this.manifest.skip=_e(p.attributes),this.warnOnMissingAttributes_("#EXT-X-SKIP",p.attributes,["SKIPPED-SEGMENTS"])},part(){a=!0;let A=this.manifest.segments.length,h=_e(p.attributes);i.parts=i.parts||[],i.parts.push(h),h.byterange&&(h.byterange.hasOwnProperty("offset")||(h.byterange.offset=y),y=h.byterange.offset+h.byterange.length);let N=i.parts.length-1;this.warnOnMissingAttributes_(`#EXT-X-PART #${N} for segment #${A}`,p.attributes,["URI","DURATION"]),this.manifest.renditionReports&&this.manifest.renditionReports.forEach((C,P)=>{C.hasOwnProperty("lastPart")||this.trigger("warn",{message:`#EXT-X-RENDITION-REPORT #${P} lacks required attribute(s): LAST-PART`})})},"server-control"(){let A=this.manifest.serverControl=_e(p.attributes);A.hasOwnProperty("canBlockReload")||(A.canBlockReload=!1,this.trigger("info",{message:"#EXT-X-SERVER-CONTROL defaulting CAN-BLOCK-RELOAD to false"})),Ei.call(this,this.manifest),A.canSkipDateranges&&!A.hasOwnProperty("canSkipUntil")&&this.trigger("warn",{message:"#EXT-X-SERVER-CONTROL lacks required attribute CAN-SKIP-UNTIL which is required when CAN-SKIP-DATERANGES is set"})},"preload-hint"(){let A=this.manifest.segments.length,h=_e(p.attributes),N=h.type&&h.type==="PART";i.preloadHints=i.preloadHints||[],i.preloadHints.push(h),h.byterange&&(h.byterange.hasOwnProperty("offset")||(h.byterange.offset=N?y:0,N&&(y=h.byterange.offset+h.byterange.length)));let C=i.preloadHints.length-1;if(this.warnOnMissingAttributes_(`#EXT-X-PRELOAD-HINT #${C} for segment #${A}`,p.attributes,["TYPE","URI"]),!!h.type)for(let P=0;P<i.preloadHints.length-1;P++){let qe=i.preloadHints[P];qe.type&&qe.type===h.type&&this.trigger("warn",{message:`#EXT-X-PRELOAD-HINT #${C} for segment #${A} has the same TYPE ${h.type} as preload hint #${P}`})}},"rendition-report"(){let A=_e(p.attributes);this.manifest.renditionReports=this.manifest.renditionReports||[],this.manifest.renditionReports.push(A);let h=this.manifest.renditionReports.length-1,N=["LAST-MSN","URI"];a&&N.push("LAST-PART"),this.warnOnMissingAttributes_(`#EXT-X-RENDITION-REPORT #${h}`,p.attributes,N)},"part-inf"(){this.manifest.partInf=_e(p.attributes),this.warnOnMissingAttributes_("#EXT-X-PART-INF",p.attributes,["PART-TARGET"]),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),Ei.call(this,this.manifest)},daterange(){this.manifest.dateRanges.push(_e(p.attributes));let A=this.manifest.dateRanges.length-1;this.warnOnMissingAttributes_(`#EXT-X-DATERANGE #${A}`,p.attributes,["ID","START-DATE"]);let h=this.manifest.dateRanges[A];h.endDate&&h.startDate&&new Date(h.endDate)<new Date(h.startDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE END-DATE must be equal to or later than the value of the START-DATE"}),h.duration&&h.duration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE DURATION must not be negative"}),h.plannedDuration&&h.plannedDuration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE PLANNED-DURATION must not be negative"});let N=!!h.endOnNext;if(N&&!h.class&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must have a CLASS attribute"}),N&&(h.duration||h.endDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must not contain DURATION or END-DATE attributes"}),h.duration&&h.endDate){let P=h.startDate.getTime()+h.duration*1e3;this.manifest.dateRanges[A].endDate=new Date(P)}if(!O[h.id])O[h.id]=h;else{for(let P in O[h.id])if(h[P]&&JSON.stringify(O[h.id][P])!==JSON.stringify(h[P])){this.trigger("warn",{message:"EXT-X-DATERANGE tags with the same ID in a playlist must have the same attributes values"});break}let C=this.manifest.dateRanges.findIndex(P=>P.id===h.id);this.manifest.dateRanges[C]=ve(this.manifest.dateRanges[C],h),O[h.id]=ve(O[h.id],h),this.manifest.dateRanges.pop()}},"independent-segments"(){this.manifest.independentSegments=!0},"content-steering"(){this.manifest.contentSteering=_e(p.attributes),this.warnOnMissingAttributes_("#EXT-X-CONTENT-STEERING",p.attributes,["SERVER-URI"])}}[p.tagType]||o).call(t)},uri(){i.uri=p.uri,r.push(i),this.manifest.targetDuration&&!("duration"in i)&&(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),i.duration=this.manifest.targetDuration),n&&(i.key=n),i.timeline=g,s&&(i.map=s),y=0,this.lastProgramDateTime!==null&&(i.programDateTime=this.lastProgramDateTime,this.lastProgramDateTime+=i.duration*1e3),i={}},comment(){},custom(){p.segment?(i.custom=i.custom||{},i.custom[p.customType]=p.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[p.customType]=p.data)}})[p.type].call(t)})}warnOnMissingAttributes_(t,r,i){let s=[];i.forEach(function(n){r.hasOwnProperty(n)||s.push(n)}),s.length&&this.trigger("warn",{message:`${t} lacks required attribute(s): ${s.join(", ")}`})}push(t){this.lineStream.push(t)}end(){this.lineStream.push(`
`),this.manifest.dateRanges.length&&this.lastProgramDateTime===null&&this.trigger("warn",{message:"A playlist with EXT-X-DATERANGE tag must contain atleast one EXT-X-PROGRAM-DATE-TIME tag"}),this.lastProgramDateTime=null,this.trigger("end")}addParser(t){this.parseStream.addParser(t)}addTagMapper(t){this.parseStream.addTagMapper(t)}};function gr(e,t){let r=null;try{let n=new mr;n.push(e),n.end(),r=n.manifest}catch{}if(!r||!r.playlists)return w("Parsing error");let i=r.playlists,s=r.mediaGroups.AUDIO;return T(i.map(({uri:n,attributes:a})=>{let o=new URL(n,t).href,d=l;if(a.AUDIO&&s){let x=s[a.AUDIO]?.Default?.uri;if(!x){for(let _ of Object.keys(s[a.AUDIO]??{}))if(x=s[a.AUDIO]?.[_]?.uri,x)break}x&&(d=u(new URL(x,t).href))}let c={audio:!1,video:"unknown"};a.CODECS&&(c=se(a.CODECS,l,l));let g=We(c,x=>({codec:M(x),bitrate:l}),x=>{let _=l,A=l,h=l,N=l;return a.BANDWIDTH&&(N=u(a.BANDWIDTH)),a["FRAME-RATE"]&&(_=u(a["FRAME-RATE"])),a.RESOLUTION&&(A=u(a.RESOLUTION),h=u(he(a.RESOLUTION.height))),{codec:R(x),bitrate:N,fps:_,dimensions:A,quality:h}}),y=d.isSome()?{audio:d.unwrap(),video:o}:g.video?{audio:!1,video:o}:{video:!1,audio:o},O=[E("Mp4"),E("WebM"),E("Mkv")];O=O.filter(x=>{if(g.video&&g.audio){let _=g.audio.codec.name,A=g.video.codec.name,h=!!x.supported_video_codecs.find(C=>C==A),N=!!x.supported_audio_codecs.find(C=>C==_);return h&&N}else if(g.video){let _=g.video.codec.name;return!!x.supported_video_codecs.find(A=>A==_)}else if(g.audio){let _=g.audio.codec.name;return!!x.supported_audio_codecs.find(A=>A==_)}return!1});let p=O[0]??E("Mkv");return[{builder:"Hls",protocol:"hls",content_length:l,duration:"unknown",av:g,container:p},y]}))}function Ys(){I("m3u8.mts");let e=`
#EXTM3U
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=2149280,CODECS="mp4a.40.2,avc1.64001f",RESOLUTION=1280x720,NAME="720"
url_0/193039199_mp4_h264_aac_hd_7.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=246440,CODECS="mp4a.40.5,avc1.42000d",RESOLUTION=320x184,NAME="240"
url_2/193039199_mp4_h264_aac_ld_7.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=460560,CODECS="mp4a.40.5,avc1.420016",RESOLUTION=512x288,NAME="380"
url_4/193039199_mp4_h264_aac_7.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=836280,CODECS="mp4a.40.2,avc1.64001f",RESOLUTION=848x480,NAME="480"
url_6/193039199_mp4_h264_aac_hq_7.m3u8
#EXT-X-STREAM-INF:PROGRAM-ID=1,BANDWIDTH=6221600,CODECS="mp4a.40.2,avc1.640028",RESOLUTION=1920x1080,NAME="1080"
url_8/193039199_mp4_h264_aac_fhd_7.m3u8`,t=`
#EXTM3U
#EXT-X-VERSION:3
#EXT-X-PLAYLIST-TYPE:VOD
#EXT-X-TARGETDURATION:11
#EXTINF:10.000,
url_462/193039199_mp4_h264_aac_hd_7.ts
#EXTINF:10.000,
url_463/193039199_mp4_h264_aac_hd_7.ts
#EXTINF:10.000,
url_464/193039199_mp4_h264_aac_hd_7.ts
#EXTINF:10.000,
url_465/193039199_mp4_h264_aac_hd_7.ts
#EXTINF:10.000,
url_466/193039199_mp4_h264_aac_hd_7.ts
#EXT-X-ENDLIST`,r=`#EXTM3U
#EXT-X-INDEPENDENT-SEGMENTS
#EXT-X-MEDIA:URI="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/233/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/goi/133/sgoap/clen%3D6605123%3Bdur%3D1083.025%3Bgir%3Dyes%3Bitag%3D139%3Blmt%3D1571629531019304/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,goi,sgoap,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAPnE8h7jDC0_ydzjAMwORIUfvcZQ3o7p8bKKdx2Tq_DLAiEAyqmdRIKyGXOPqJ8VeISaW4L8S9s3Xg9U0snBmCwcwMU%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgAOvew7ZJ3FVYWA_rNOUZCd51kr2rJI1rZLBv5ODj4a8CIQC1hb_BK3hwpKz6gGhVAanZGm8BXHCP8i-ypG5gjicypw%3D%3D/playlist/index.m3u8",TYPE=AUDIO,GROUP-ID="233",NAME="Default",DEFAULT=YES,AUTOSELECT=YES
#EXT-X-MEDIA:URI="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/234/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/goi/133/sgoap/clen%3D17527302%3Bdur%3D1082.955%3Bgir%3Dyes%3Bitag%3D140%3Blmt%3D1571629525313221/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,goi,sgoap,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAI9bVVD9pCkEFpJ99ZuTx6C1qwFRlWzVRrjNcCUtygpwAiB1DtiFPkWfCvc_8ORRhOtBYOCAwCS3WpyKq6H2lPFc0g%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgQgbfCj_K04fm5h9S9AH3rKs57sGuVvQ2TRzL9yrl0wICIQCXnZgNRGkRiDsKd1czXUiC19hYfeyRAMUtRTJOftWJVw%3D%3D/playlist/index.m3u8",TYPE=AUDIO,GROUP-ID="234",NAME="Default",DEFAULT=YES,AUTOSELECT=YES
#EXT-X-STREAM-INF:BANDWIDTH=234107,CODECS="avc1.4D4015,mp4a.40.5",RESOLUTION=426x240,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="233",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/229/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D11918725%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D133%3Blmt%3D1571629723378125/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhALPuaiX5Hj5Pb4lOv36DrrTVznjAzQK79Q9LeJdD8Cf1AiEAj7nCIW9XFzV84SIeBgZ_kx3EtXG3JpKfiGe5a5dErFg%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAJVRhrI3K9FbXln3gSgnw6aPdrH4ZjKxH340X56OxzXTAiAxY-KIACA0Qp3Lcs1gXpZ7hkDgFmvCa4HQlHHoE5MlzQ%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=318477,CODECS="avc1.4D4015,mp4a.40.2",RESOLUTION=426x240,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/229/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D11918725%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D133%3Blmt%3D1571629723378125/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhALPuaiX5Hj5Pb4lOv36DrrTVznjAzQK79Q9LeJdD8Cf1AiEAj7nCIW9XFzV84SIeBgZ_kx3EtXG3JpKfiGe5a5dErFg%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAJVRhrI3K9FbXln3gSgnw6aPdrH4ZjKxH340X56OxzXTAiAxY-KIACA0Qp3Lcs1gXpZ7hkDgFmvCa4HQlHHoE5MlzQ%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=591758,CODECS="avc1.4D401E,mp4a.40.2",RESOLUTION=640x360,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/230/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D29373115%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D134%3Blmt%3D1571629723378451/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgIRljIW-XWDdvOJv6pOIL6vocFyfYpGQ3NEID0ZeAfuQCIE_KsiAkF3PreAeXFjIThnsWwy8SigUWIuFl6AIe-VfM/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgJ8ikWsed7UzvN9UBrvZOeJ_clNmrJBTY6-zTkVYM68oCIQDF7ivscvq9uE24Tncj324ucpmKOEbCUiLOrkBWnAoU9g%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=976850,CODECS="avc1.4D401F,mp4a.40.2",RESOLUTION=854x480,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/231/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D57637129%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D135%3Blmt%3D1571629723378011/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgThOSqXf_v9qkXcII_9s09RHxY2Vn-fTj_foFglww3OQCICLQoWfPcWh9cicGlBbSU-SSDmLCGtDXpzniQH0Utgty/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRAIgLbseKnorX6bF6NjHF5XAg3J_r3b3ZWraTObN57qG_8gCIFCB1She_7SzuTRrLkbbkm_e8mB0HQUU5JtfYhAeI-uG/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=1681198,CODECS="avc1.4D401F,mp4a.40.2",RESOLUTION=1280x720,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/232/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D114300787%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D136%3Blmt%3D1571629723376495/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgD8TjJSPpGXR7PGBfat0YDdMKCH5oj6pkl_p9dEa2ObcCICYdFnHYrQQYlBionu3osVg_ClwOfc8BkN8JUxoCfN2O/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAJfaGF2q3E6YA9FPSe8BRKL_4bLsa3Oa_-pk_fBQQbnfAiB5X748W0_LLyMT-5Aw32BgI-TzPkuK-jnUxDnsUFM5hw%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=149562,CODECS="avc1.4D400C,mp4a.40.5",RESOLUTION=256x144,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="233",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/269/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D6602239%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D160%3Blmt%3D1571629723377629/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAIuDkxKaivuvViRQV6qh0fngNZxpAwhlSmV4jcUHnLrDAiEAkx7eaRB3hVVRwOMyQgjYbrFK2NOO8XJIGalnSIbwbg4%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRgIhAPgXMawLk63-KyPbCjXxTnrCxLJXVXgRwe85BT4aZY6KAiEAxxBRvnAOAq37EVummcBwGwveHzS9tuAJaWm8Bsm0vJ8%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=3058612,CODECS="avc1.640028,mp4a.40.2",RESOLUTION=1920x1080,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/270/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D218021954%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D137%3Blmt%3D1571629598365204/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgNaVO2chqv5it3v4apX-JXvUIcOor5uTZpthe2UhekQMCIFPw5SatPTf3_iJzXm81hSUci6NNjG2Xa-L4g5mSY4tG/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRAIgHG-P_oDtpWh-qWqz4pioLmQm0pAZnMOX74OfAUM9DgUCIDwlHOzcRESF2HdhEyFUGoglaVm37dZ2YC0xhHBzsvcp/playlist/index.m3u8

#EXT-X-STREAM-INF:BANDWIDTH=156374,CODECS="vp09.00.11.08,mp4a.40.5",RESOLUTION=256x144,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="233",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/603/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D12622952%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D278%3Blmt%3D1571629972496559/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAJGLZmJeZa9PiKWs-DVvw7ch3uBks8BD2uTMJOyLWfHGAiEA1NFd1f2XcVapPqtdhkvSMBNaWAK0C71_Pso3k0QBERU%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRAIgPTKg4dqQba4Q8K8C7Kf6Cv7QVZix7CucZUW8jTY2NcECIEp6Cxq-A0Hf0GGTHps9TcYFahvevHuTWzp_fCopwmiT/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=286033,CODECS="vp09.00.20.08,mp4a.40.5",RESOLUTION=426x240,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="233",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/604/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D18141535%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D242%3Blmt%3D1571629972497594/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAKBNwFeTqrplmWHGvi-5UVuPDGO9mvvDruW_36Wr-MItAiEAybzfHFSJukwuYXcCyA32qqcce3dNsOHYMXb9InV8F04%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAKV4CvCFOVV7XdqLGlSAT48WwU5yLHOyLOFQ-XuGr2mJAiB5dDuPevAZjHioPxn-c-hmpunFcFQJBOL4MSFhJekMMA%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=370403,CODECS="vp09.00.20.08,mp4a.40.2",RESOLUTION=426x240,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/604/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D18141535%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D242%3Blmt%3D1571629972497594/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAKBNwFeTqrplmWHGvi-5UVuPDGO9mvvDruW_36Wr-MItAiEAybzfHFSJukwuYXcCyA32qqcce3dNsOHYMXb9InV8F04%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAKV4CvCFOVV7XdqLGlSAT48WwU5yLHOyLOFQ-XuGr2mJAiB5dDuPevAZjHioPxn-c-hmpunFcFQJBOL4MSFhJekMMA%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=567283,CODECS="vp09.00.21.08,mp4a.40.2",RESOLUTION=640x360,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/605/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D33210526%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D243%3Blmt%3D1571629972501960/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIgVzJIN9aHmjMIpr8u_h2b4vuTGBTDpGmrmuIX5R1yySICIQCSeVSeDk7y_ULcsd0iqrzj7LaHy1596jdIbEOJGi_ofA%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAIh7R8KJ2vRW5ndwMP2A2Qb_ykfwwEff7BkygW08bDz2AiASj8B098t2cd4v0EzyxBTX7jobUznT_vf1jPBxaDmKfA%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=902798,CODECS="vp09.00.30.08,mp4a.40.2",RESOLUTION=854x480,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/606/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D57867001%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D244%3Blmt%3D1571629972497230/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAM7WHAgMz0xl3mEAeRN6EcABV6wiVzXtNpGzENiyQmDkAiEA29h0rOm5dBplvu-dua2bO63bAGoqv9c3Tikjsqu_ck8%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgK3QF6oY6kYGNRbPbNxbN1D63RatNhrodHmvluMlTVowCIQDDtgpnm4fs-AENgE-eTyT_LXtDvQxzkvNNGMZKd41HBg%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=1700136,CODECS="vp09.00.31.08,mp4a.40.2",RESOLUTION=1280x720,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/609/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D121515907%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D247%3Blmt%3D1571629972501181/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAPMDwiLR7XlqxR_08mQDBgIjw_PiZr6EFFykWEGK3VHWAiBdI2HhCfF66FBPaqfljv_jL4hbBq3BtLsk_A5GJae7rg%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAPqF_2__Hve-UjCuNj5_iPpXeKTVGjdd67ZdTzVFsuRcAiBXaMW3HTUY6FO05ozIoKb4jytVuMdOPrtfinjn99l7JQ%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=2911438,CODECS="vp09.00.40.08,mp4a.40.2",RESOLUTION=1920x1080,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/614/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D231846831%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D248%3Blmt%3D1571629795837732/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAMTFD3eXF9BFERUDcLlpXQfDJDPXjvtTvzqPgyLtCXYEAiAF-SlKTIkbtBbdmv8jgBAarMuenPkavYYi28wTxd8rvQ%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRgIhAI6PBOh9J3kE-C-VVw0ePiXz9PQypG9Z9iLf6kEvqaiuAiEA_snY52pxFbEByu0ymNLgmG4KaUP7mVIJcAaeAJst0Us%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=8010714,CODECS="vp09.00.50.08,mp4a.40.2",RESOLUTION=2560x1440,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/620/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D548165830%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D271%3Blmt%3D1571630047472254/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAOq8qo_2j86UqdCKfwmbJeA-XEXDmpTYQFf6afh8pld7AiBz2ebxJr3GH1oRwhy1IFb26cpYlEjyiUX_LQfHLbRFEA%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAMPiIln2_clvF8rAvk_MPa6EHgpi4SxRdWilhFXd6orlAiA3s42Q0gt7XbgxqCn-nNvkkj_eQ_tMGKloGcIlzkDApA%3D%3D/playlist/index.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=18560951,CODECS="vp09.00.50.08,mp4a.40.2",RESOLUTION=3840x2160,FRAME-RATE=30,VIDEO-RANGE=SDR,AUDIO="234",CLOSED-CAPTIONS=NONE
https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/625/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D1741594579%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D313%3Blmt%3D1571630715330215/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgPGhF7AnZy3A_kBvdh31eQ6kTFs0MAFWLGlyHew_ZcAkCIGGEFJqBQuT9q4BYZd4C4kjRunY-T8_1vu_Nj6RauFgb/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAMgLbP4UadgXgokuSV82pCo_ZlQgQiM3ylIBHy4xgXRQAiBouK6djcFT_MS5LzhqGVLZE-AnMQA4ihZZjPXzT9t8Dw%3D%3D/playlist/index.m3u8`,i=gr("xxx","https://foobar.com/x/a.m3u8");f(i.isErr(),!0);let s=gr(t,"https://foobar.com/x/a.m3u8");f(s.isErr(),!0);let n=gr(e,"https://foobar.com/x/a.m3u8").unwrap();f(n.length,5);let a="https://foobar.com/x/url_0/193039199_mp4_h264_aac_hd_7.m3u8",o="https://foobar.com/x/url_2/193039199_mp4_h264_aac_ld_7.m3u8",d="https://foobar.com/x/url_4/193039199_mp4_h264_aac_7.m3u8",c="https://foobar.com/x/url_6/193039199_mp4_h264_aac_hq_7.m3u8",g="https://foobar.com/x/url_8/193039199_mp4_h264_aac_fhd_7.m3u8",v={builder:"Hls",content_length:l,protocol:"hls",duration:"unknown",container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:l},video:{codec:R("H264"),bitrate:u(2149280),fps:l,dimensions:u({width:1280,height:720}),quality:u("720")}}},y={builder:"Hls",content_length:l,protocol:"hls",duration:"unknown",container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:l},video:{codec:R("H264"),bitrate:u(246440),fps:l,dimensions:u({width:320,height:184}),quality:u("240")}}},O={builder:"Hls",content_length:l,protocol:"hls",duration:"unknown",container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:l},video:{codec:R("H264"),bitrate:u(460560),fps:l,dimensions:u({width:512,height:288}),quality:u("240")}}},p={builder:"Hls",content_length:l,protocol:"hls",duration:"unknown",container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:l},video:{codec:R("H264"),bitrate:u(836280),fps:l,dimensions:u({width:848,height:480}),quality:u("480")}}},x={builder:"Hls",content_length:l,protocol:"hls",duration:"unknown",container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:l},video:{codec:R("H264"),bitrate:u(6221600),fps:l,dimensions:u({width:1920,height:1080}),quality:u("1080")}}};f(n[0],[v,{video:a,audio:!1}]),f(n[1],[y,{video:o,audio:!1}]),f(n[2],[O,{video:d,audio:!1}]),f(n[3],[p,{video:c,audio:!1}]),f(n[4],[x,{video:g,audio:!1}]);let _=gr(r,"https://foobar.com/x/a.m3u8").unwrap(),A="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/233/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/goi/133/sgoap/clen%3D6605123%3Bdur%3D1083.025%3Bgir%3Dyes%3Bitag%3D139%3Blmt%3D1571629531019304/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,goi,sgoap,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAPnE8h7jDC0_ydzjAMwORIUfvcZQ3o7p8bKKdx2Tq_DLAiEAyqmdRIKyGXOPqJ8VeISaW4L8S9s3Xg9U0snBmCwcwMU%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgAOvew7ZJ3FVYWA_rNOUZCd51kr2rJI1rZLBv5ODj4a8CIQC1hb_BK3hwpKz6gGhVAanZGm8BXHCP8i-ypG5gjicypw%3D%3D/playlist/index.m3u8",h="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/234/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/goi/133/sgoap/clen%3D17527302%3Bdur%3D1082.955%3Bgir%3Dyes%3Bitag%3D140%3Blmt%3D1571629525313221/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,goi,sgoap,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAI9bVVD9pCkEFpJ99ZuTx6C1qwFRlWzVRrjNcCUtygpwAiB1DtiFPkWfCvc_8ORRhOtBYOCAwCS3WpyKq6H2lPFc0g%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgQgbfCj_K04fm5h9S9AH3rKs57sGuVvQ2TRzL9yrl0wICIQCXnZgNRGkRiDsKd1czXUiC19hYfeyRAMUtRTJOftWJVw%3D%3D/playlist/index.m3u8",N="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/229/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D11918725%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D133%3Blmt%3D1571629723378125/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhALPuaiX5Hj5Pb4lOv36DrrTVznjAzQK79Q9LeJdD8Cf1AiEAj7nCIW9XFzV84SIeBgZ_kx3EtXG3JpKfiGe5a5dErFg%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAJVRhrI3K9FbXln3gSgnw6aPdrH4ZjKxH340X56OxzXTAiAxY-KIACA0Qp3Lcs1gXpZ7hkDgFmvCa4HQlHHoE5MlzQ%3D%3D/playlist/index.m3u8",C="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/229/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D11918725%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D133%3Blmt%3D1571629723378125/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhALPuaiX5Hj5Pb4lOv36DrrTVznjAzQK79Q9LeJdD8Cf1AiEAj7nCIW9XFzV84SIeBgZ_kx3EtXG3JpKfiGe5a5dErFg%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAJVRhrI3K9FbXln3gSgnw6aPdrH4ZjKxH340X56OxzXTAiAxY-KIACA0Qp3Lcs1gXpZ7hkDgFmvCa4HQlHHoE5MlzQ%3D%3D/playlist/index.m3u8",P="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/230/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D29373115%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D134%3Blmt%3D1571629723378451/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgIRljIW-XWDdvOJv6pOIL6vocFyfYpGQ3NEID0ZeAfuQCIE_KsiAkF3PreAeXFjIThnsWwy8SigUWIuFl6AIe-VfM/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgJ8ikWsed7UzvN9UBrvZOeJ_clNmrJBTY6-zTkVYM68oCIQDF7ivscvq9uE24Tncj324ucpmKOEbCUiLOrkBWnAoU9g%3D%3D/playlist/index.m3u8",qe="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/231/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D57637129%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D135%3Blmt%3D1571629723378011/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgThOSqXf_v9qkXcII_9s09RHxY2Vn-fTj_foFglww3OQCICLQoWfPcWh9cicGlBbSU-SSDmLCGtDXpzniQH0Utgty/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRAIgLbseKnorX6bF6NjHF5XAg3J_r3b3ZWraTObN57qG_8gCIFCB1She_7SzuTRrLkbbkm_e8mB0HQUU5JtfYhAeI-uG/playlist/index.m3u8",_r="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/232/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D114300787%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D136%3Blmt%3D1571629723376495/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgD8TjJSPpGXR7PGBfat0YDdMKCH5oj6pkl_p9dEa2ObcCICYdFnHYrQQYlBionu3osVg_ClwOfc8BkN8JUxoCfN2O/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAJfaGF2q3E6YA9FPSe8BRKL_4bLsa3Oa_-pk_fBQQbnfAiB5X748W0_LLyMT-5Aw32BgI-TzPkuK-jnUxDnsUFM5hw%3D%3D/playlist/index.m3u8",Ar="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/269/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D6602239%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D160%3Blmt%3D1571629723377629/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAIuDkxKaivuvViRQV6qh0fngNZxpAwhlSmV4jcUHnLrDAiEAkx7eaRB3hVVRwOMyQgjYbrFK2NOO8XJIGalnSIbwbg4%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRgIhAPgXMawLk63-KyPbCjXxTnrCxLJXVXgRwe85BT4aZY6KAiEAxxBRvnAOAq37EVummcBwGwveHzS9tuAJaWm8Bsm0vJ8%3D/playlist/index.m3u8",yr="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/270/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/sgovp/clen%3D218021954%3Bdur%3D1082.881%3Bgir%3Dyes%3Bitag%3D137%3Blmt%3D1571629598365204/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5532432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgNaVO2chqv5it3v4apX-JXvUIcOor5uTZpthe2UhekQMCIFPw5SatPTf3_iJzXm81hSUci6NNjG2Xa-L4g5mSY4tG/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRAIgHG-P_oDtpWh-qWqz4pioLmQm0pAZnMOX74OfAUM9DgUCIDwlHOzcRESF2HdhEyFUGoglaVm37dZ2YC0xhHBzsvcp/playlist/index.m3u8",ye="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/603/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D12622952%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D278%3Blmt%3D1571629972496559/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAJGLZmJeZa9PiKWs-DVvw7ch3uBks8BD2uTMJOyLWfHGAiEA1NFd1f2XcVapPqtdhkvSMBNaWAK0C71_Pso3k0QBERU%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRAIgPTKg4dqQba4Q8K8C7Kf6Cv7QVZix7CucZUW8jTY2NcECIEp6Cxq-A0Hf0GGTHps9TcYFahvevHuTWzp_fCopwmiT/playlist/index.m3u8",br="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/604/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D18141535%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D242%3Blmt%3D1571629972497594/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAKBNwFeTqrplmWHGvi-5UVuPDGO9mvvDruW_36Wr-MItAiEAybzfHFSJukwuYXcCyA32qqcce3dNsOHYMXb9InV8F04%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAKV4CvCFOVV7XdqLGlSAT48WwU5yLHOyLOFQ-XuGr2mJAiB5dDuPevAZjHioPxn-c-hmpunFcFQJBOL4MSFhJekMMA%3D%3D/playlist/index.m3u8",Tr="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/604/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D18141535%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D242%3Blmt%3D1571629972497594/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAKBNwFeTqrplmWHGvi-5UVuPDGO9mvvDruW_36Wr-MItAiEAybzfHFSJukwuYXcCyA32qqcce3dNsOHYMXb9InV8F04%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAKV4CvCFOVV7XdqLGlSAT48WwU5yLHOyLOFQ-XuGr2mJAiB5dDuPevAZjHioPxn-c-hmpunFcFQJBOL4MSFhJekMMA%3D%3D/playlist/index.m3u8",dt="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/605/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D33210526%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D243%3Blmt%3D1571629972501960/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIgVzJIN9aHmjMIpr8u_h2b4vuTGBTDpGmrmuIX5R1yySICIQCSeVSeDk7y_ULcsd0iqrzj7LaHy1596jdIbEOJGi_ofA%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAIh7R8KJ2vRW5ndwMP2A2Qb_ykfwwEff7BkygW08bDz2AiASj8B098t2cd4v0EzyxBTX7jobUznT_vf1jPBxaDmKfA%3D%3D/playlist/index.m3u8",pt="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/606/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D57867001%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D244%3Blmt%3D1571629972497230/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRgIhAM7WHAgMz0xl3mEAeRN6EcABV6wiVzXtNpGzENiyQmDkAiEA29h0rOm5dBplvu-dua2bO63bAGoqv9c3Tikjsqu_ck8%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIgK3QF6oY6kYGNRbPbNxbN1D63RatNhrodHmvluMlTVowCIQDDtgpnm4fs-AENgE-eTyT_LXtDvQxzkvNNGMZKd41HBg%3D%3D/playlist/index.m3u8",Bo="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/609/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D121515907%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D247%3Blmt%3D1571629972501181/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAPMDwiLR7XlqxR_08mQDBgIjw_PiZr6EFFykWEGK3VHWAiBdI2HhCfF66FBPaqfljv_jL4hbBq3BtLsk_A5GJae7rg%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAPqF_2__Hve-UjCuNj5_iPpXeKTVGjdd67ZdTzVFsuRcAiBXaMW3HTUY6FO05ozIoKb4jytVuMdOPrtfinjn99l7JQ%3D%3D/playlist/index.m3u8",qo="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/614/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D231846831%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D248%3Blmt%3D1571629795837732/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAMTFD3eXF9BFERUDcLlpXQfDJDPXjvtTvzqPgyLtCXYEAiAF-SlKTIkbtBbdmv8jgBAarMuenPkavYYi28wTxd8rvQ%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRgIhAI6PBOh9J3kE-C-VVw0ePiXz9PQypG9Z9iLf6kEvqaiuAiEA_snY52pxFbEByu0ymNLgmG4KaUP7mVIJcAaeAJst0Us%3D/playlist/index.m3u8",Vo="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/620/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D548165830%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D271%3Blmt%3D1571630047472254/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRQIhAOq8qo_2j86UqdCKfwmbJeA-XEXDmpTYQFf6afh8pld7AiBz2ebxJr3GH1oRwhy1IFb26cpYlEjyiUX_LQfHLbRFEA%3D%3D/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAMPiIln2_clvF8rAvk_MPa6EHgpi4SxRdWilhFXd6orlAiA3s42Q0gt7XbgxqCn-nNvkkj_eQ_tMGKloGcIlzkDApA%3D%3D/playlist/index.m3u8",ko="https://manifest.googlevideo.com/api/manifest/hls_playlist/expire/1703248535/ei/Ny6FZYLPLp-RvcAPxdK14Ao/ip/2401:e180:88b1:21ef:5840:b4c0:440:a3f3/id/ebea223c57a7882c/itag/625/source/youtube/requiressl/yes/ratebypass/yes/pfa/1/wft/1/sgovp/clen%3D1741594579%3Bdur%3D1082.882%3Bgir%3Dyes%3Bitag%3D313%3Blmt%3D1571630715330215/rqh/1/hls_chunk_host/rr6---sn-45gx5nuvox-u2xk.googlevideo.com/xpc/EgVo2aDSNQ%3D%3D/mh/-B/mm/31,29/mn/sn-45gx5nuvox-u2xk,sn-45gx5nuvox-u2xe7/ms/au,rdu/mv/m/mvi/6/pl/44/initcwndbps/786250/vprv/1/playlist_type/DVR/dover/13/txp/5531432/mt/1703226764/fvip/5/short_key/1/keepalive/yes/fexp/24007246/sparams/expire,ei,ip,id,itag,source,requiressl,ratebypass,pfa,wft,sgovp,rqh,xpc,vprv,playlist_type/sig/AJfQdSswRAIgPGhF7AnZy3A_kBvdh31eQ6kTFs0MAFWLGlyHew_ZcAkCIGGEFJqBQuT9q4BYZd4C4kjRunY-T8_1vu_Nj6RauFgb/lsparams/hls_chunk_host,mh,mm,mn,ms,mv,mvi,pl,initcwndbps/lsig/AAO5W4owRQIhAMgLbP4UadgXgokuSV82pCo_ZlQgQiM3ylIBHy4xgXRQAiBouK6djcFT_MS5LzhqGVLZE-AnMQA4ihZZjPXzT9t8Dw%3D%3D/playlist/index.m3u8";f(_.length,16);let b={builder:"Hls",content_length:l,protocol:"hls",duration:"unknown",container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:l},video:{codec:R("H264"),bitrate:l,fps:l,dimensions:l,quality:l}}};b.av.video={...b.av.video,bitrate:u(234107),dimensions:u({width:426,height:240}),fps:u(30),quality:u("240")},f(_[0],[b,{video:N,audio:A}]),b.av.video={...b.av.video,bitrate:u(318477),dimensions:u({width:426,height:240}),fps:u(30),quality:u("240")},f(_[1],[b,{video:C,audio:h}]),b.av.video={...b.av.video,bitrate:u(591758),dimensions:u({width:640,height:360}),fps:u(30),quality:u("360")},f(_[2],[b,{video:P,audio:h}]),b.av.video={...b.av.video,bitrate:u(976850),dimensions:u({width:854,height:480}),fps:u(30),quality:u("480")},f(_[3],[b,{video:qe,audio:h}]),b.av.video={...b.av.video,bitrate:u(1681198),dimensions:u({width:1280,height:720}),fps:u(30),quality:u("720")},f(_[4],[b,{video:_r,audio:h}]),b.av.video={...b.av.video,bitrate:u(149562),dimensions:u({width:256,height:144}),fps:u(30),quality:u("240")},f(_[5],[b,{video:Ar,audio:A}]),b.av.video={...b.av.video,bitrate:u(3058612),dimensions:u({width:1920,height:1080}),fps:u(30),quality:u("1080")},f(_[6],[b,{video:yr,audio:h}]),b.av.video.codec=R("VP9"),b.container=E("Mkv"),b.av.video={...b.av.video,bitrate:u(156374),dimensions:u({width:256,height:144}),fps:u(30),quality:u("240")},f(_[7],[b,{video:ye,audio:A}]),b.av.video={...b.av.video,bitrate:u(286033),dimensions:u({width:426,height:240}),fps:u(30),quality:u("240")},f(_[8],[b,{video:br,audio:A}]),b.av.video={...b.av.video,bitrate:u(370403),dimensions:u({width:426,height:240}),fps:u(30),quality:u("240")},f(_[9],[b,{video:Tr,audio:h}]),b.av.video={...b.av.video,bitrate:u(567283),dimensions:u({width:640,height:360}),fps:u(30),quality:u("360")},f(_[10],[b,{video:dt,audio:h}]),b.av.video={...b.av.video,bitrate:u(902798),dimensions:u({width:854,height:480}),fps:u(30),quality:u("480")},f(_[11],[b,{video:pt,audio:h}]),b.av.video={...b.av.video,bitrate:u(1700136),dimensions:u({width:1280,height:720}),fps:u(30),quality:u("720")},f(_[12],[b,{video:Bo,audio:h}]),b.av.video={...b.av.video,bitrate:u(2911438),dimensions:u({width:1920,height:1080}),fps:u(30),quality:u("1080")},f(_[13],[b,{video:qo,audio:h}]),b.av.video={...b.av.video,bitrate:u(8010714),dimensions:u({width:2560,height:1440}),fps:u(30),quality:u("1440")},f(_[14],[b,{video:Vo,audio:h}]),b.av.video={...b.av.video,bitrate:u(18560951),dimensions:u({width:3840,height:2160}),fps:u(30),quality:u("2160")},f(_[15],[b,{video:ko,audio:h}])}var ot=(e,t)=>typeof e[t]=="string",Ce=(e,t)=>typeof e[t]=="number";function F(e){try{if(ot(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return T(e.__serializer_value);if(e.__serializer_tag==="regex"){let i=new RegExp(e.__serializer_value);return T(i)}else if(e.__serializer_tag==="array"){let i=[];for(let s of e.__serializer_value){let n=F(s);if(n.isErr())return n;i.push(n.unwrap())}return T(i)}else if(e.__serializer_tag==="map"){let i=[];for(let s of e.__serializer_value){let n=F(s);if(n.isErr())return n;i.push(n.unwrap())}return T(new Map(i))}else if(e.__serializer_tag==="set"){let i=[];for(let s of e.__serializer_value){let n=F(s);if(n.isErr())return n;i.push(n.unwrap())}return T(new Set(i))}else if(e.__serializer_tag==="result_ok"){let i=e.__serializer_value,s=F(i);return s.isErr()?s:T(T(s.unwrap()))}else if(e.__serializer_tag==="result_err"){let i=e.__serializer_value,s=F(i);return s.isErr()?s:T(w(s.unwrap()))}else if(e.__serializer_tag==="option_some"){let i=e.__serializer_value,s=F(i);return s.isErr()?s:T(u(s.unwrap()))}else if(e.__serializer_tag==="option_none")return T(l)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return w("This object was not serialized with Serialize");let r={};for(let i of Object.keys(e))if(typeof i=="string"){let s=F(e[i]);if(s.isErr())return s;r[i]=s.unwrap()}return T(r)}catch{return w("Failed to inspect object. Not JSON?")}}function L(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return T({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return T({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(n=>L(n)),i=r.as_iter().find(n=>n.isErr());if(i.isSome())return i.unwrap();let s=r.as_iter().map(n=>n.unwrap()).toArray();return T({__serializer_tag:"array",__serializer_value:s})}else if(e instanceof Map){let r=[...e.entries()].map(n=>L(n)),i=r.as_iter().find(n=>n.isErr());if(i.isSome())return i.unwrap();let s=r.as_iter().map(n=>n.unwrap()).toArray();return T({__serializer_tag:"map",__serializer_value:s})}else if(e instanceof Set){let r=[...e.values()].map(n=>L(n)),i=r.as_iter().find(n=>n.isErr());if(i.isSome())return i.unwrap();let s=r.as_iter().map(n=>n.unwrap()).toArray();return T({__serializer_tag:"set",__serializer_value:s})}else if(pe.isResult(e))if(e.isOk()){let r=e.unwrap(),i=L(r);return i.isErr()?i:T({__serializer_tag:"result_ok",__serializer_value:i.unwrap()})}else{let r=e.unwrapErr(),i=L(r);return i.isErr()?i:T({__serializer_tag:"result_err",__serializer_value:i.unwrap()})}else if(de.isOption(e))if(e.isSome()){let r=e.unwrap(),i=L(r);return i.isErr()?i:T({__serializer_tag:"option_some",__serializer_value:i.unwrap()})}else return T({__serializer_tag:"option_none"});else if(t==="object"){let r={},i=e;for(let s of Object.keys(e)){let n=i[s],a=L(n);if(a.isErr())continue;let o=a.unwrap();r[s]=o}return T(r)}else return w("Unsupported value")}function zs(){I("deser.mts");let e={a:1,b:"xx",c:u({d:null,e:"xx",f:l}),g:u(u(u(void 0))),h:[u(1),l,"foo"],i:T(T("abc"))},t={c:u({d:null}),g:u(u(u(void 0))),h:[u(1),null,u(null)],i:new Map([["foo",e],["bar",e]]),j:new Set([1,2,3,4]),k:w(T("abc")),l:/[^h]+ [(.*)]/};f(F(L(u("x")).unwrap()).unwrap(),u("x")),f(F({b:[1,2]}).isErr(),!0),f(F(L(1).unwrap()).unwrap(),1),f(L(e).isOk(),!0),f(F(L(e).unwrap()).unwrap(),e),f(F(L(t).unwrap()).unwrap(),t)}function Ri(){return{prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"4320",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:[]}}function Pd(e){return L(e).unwrap()}function $s(e){let t=F(e).unwrapOr({}),r=Ri(),i=it(t.container).unwrapOr(r.container),s=tt(t.video_codec).unwrapOr(r.video_codec),n=oe(t.best_video_quality).unwrapOr(r.best_video_quality),a=oe(t.lowest_video_quality).unwrapOr(r.lowest_video_quality),o;if("prefered_video_quality"in t){let p=oe(t.prefered_video_quality);p.isSome()&&(o=p.unwrap())}let d=r.max_variants;if(typeof t.max_variants=="number"){let p=t.max_variants;Number.isInteger(p)&&p<=11&&p>0&&(d=p)}let c=r.prefer_60fps;typeof t.prefer_60fps=="boolean"&&(c=t.prefer_60fps);let g=r.ignore_low_quality_hits;typeof t.ignore_low_quality_hits=="boolean"&&(g=t.ignore_low_quality_hits);let v=[];if(Array.isArray(t.ignored_containers))for(let p of t.ignored_containers){let x=it(p);x.isSome()&&v.push(x.unwrap())}let y=[];if(Array.isArray(t.ignored_video_codecs))for(let p of t.ignored_video_codecs){let x=tt(p);x.isSome()&&y.push(x.unwrap())}let O={prefer_60fps:c,ignore_low_quality_hits:g,container:i,max_variants:d,video_codec:s,lowest_video_quality:a,best_video_quality:n,ignored_containers:v,ignored_video_codecs:y};return typeof o<"u"&&(O.prefered_video_quality=o),O}function Zs(){I("media_user_pref.mts");let e={...Ri(),container:"Mkv"};f($s(Pd(e)),e),f($s({}),Ri())}function Dd(e,t,r){if(e.protocol==="hls"&&t.protocol!="hls")return-1;if(t.protocol==="hls"&&e.protocol!="hls"||e.protocol==="non-adaptative"&&t.protocol!="non-adaptative")return 1;if(t.protocol==="non-adaptative"&&e.protocol!="non-adaptative")return-1;if(e.container.name!=t.container.name){if(e.container.name==r.container)return-1;if(t.container.name==r.container)return 1;let a=r.ignored_containers.includes(e.container.name),o=r.ignored_containers.includes(t.container.name);if(!a&&o)return-1;if(a&&!o)return 1}if(!Ws(e.av,t.av)){if(e.av.audio&&e.av.video)return-1;if(t.av.audio&&t.av.video)return 1;if(e.av.video)return-1;if(t.av.video)return 1}if(e.duration&&t.duration){if(e.duration>t.duration)return-1;if(t.duration>e.duration)return 1}if(e.av.video&&t.av.video){let a=e.av.video,o=t.av.video;if(a.codec.name!=o.codec.name){if(a.codec.name==r.video_codec)return-1;if(o.codec.name==r.video_codec)return 1;let d=r.ignored_video_codecs.includes(a.codec.name),c=r.ignored_video_codecs.includes(o.codec.name);if(!d&&c)return-1;if(d&&!c)return 1}if(a.quality.isSome()){if(o.quality.isNone())return-1;let d=a.quality.unwrap(),c=o.quality.unwrap();if(d!=c){if(d==r.prefered_video_quality)return-1;if(c==r.prefered_video_quality)return 1;let g=O=>Ge(O,r.best_video_quality)||lr(O,r.lowest_video_quality),v=g(d),y=g(c);if(!v&&y)return-1;if(v&&!y)return 1;if(Ge(d,c))return-1;if(Ge(c,d))return 1}}if(a.dimensions.isSome()){if(o.dimensions.isNone())return-1;let d=a.dimensions.unwrap(),c=o.dimensions.unwrap();if(d.height>c.height)return-1;if(c.height>d.height)return 1}if(a.bitrate.isSome()){if(o.bitrate.isNone())return-1;let d=a.bitrate.unwrap(),c=o.bitrate.unwrap();if(d>c)return-1;if(c>d)return 1}if(a.fps.isSome()){if(o.fps.isNone())return-1;let d=a.fps.unwrap(),c=o.fps.unwrap();if(d!=c){if(d==60&&r.prefer_60fps||c==60&&r.prefer_60fps||d>c)return-1;if(c>d)return 1}}}return 0}function eo(){I("media.mts");let e=[{builder:"Test",protocol:"hls",content_length:l,duration:9,container:E("Mp4"),av:{audio:{codec:M("FLAC"),bitrate:l},video:{codec:R("VP9"),fps:l,dimensions:l,quality:l,bitrate:l}}},{builder:"Test",protocol:"hls",duration:8,container:E("Mp4"),content_length:l,av:{audio:!1,video:{codec:R("VP9"),fps:l,dimensions:l,quality:l,bitrate:l}}},{builder:"Test",protocol:"hls",duration:7,container:E("WebM"),content_length:l,av:{audio:{codec:M("FLAC"),bitrate:l},video:{codec:R("VP9"),fps:l,dimensions:u({height:3e3,width:3e3}),quality:u("1080"),bitrate:l}}},{builder:"Test",protocol:"hls",duration:6,container:E("WebM"),content_length:l,av:{audio:{codec:M("FLAC"),bitrate:u(1e6)},video:{codec:R("VP9"),fps:u(60),dimensions:u({height:3e3,width:3e3}),quality:u("720"),bitrate:l}}},{builder:"Test",protocol:"hls",duration:5,container:E("WebM"),content_length:l,av:{audio:{codec:M("FLAC"),bitrate:l},video:{codec:R("VP9"),fps:l,dimensions:u({height:4e3,width:3e3}),quality:u("4320"),bitrate:l}}},{builder:"Test",protocol:"hls",duration:4,container:E("WebM"),content_length:l,av:{audio:!1,video:{codec:R("H263"),fps:u(60),dimensions:l,quality:u("1080"),bitrate:l}}},{builder:"Test",protocol:"dash",content_length:l,duration:10,container:E("Mp4"),av:{audio:{codec:M("FLAC"),bitrate:l},video:{codec:R("VP9"),fps:l,dimensions:l,quality:l,bitrate:l}}}],t={prefer_60fps:!0,ignore_low_quality_hits:!0,max_variants:3,container:"Mp4",video_codec:"H264",best_video_quality:"1080",lowest_video_quality:"480",ignored_containers:[],ignored_video_codecs:["H263"]},r=[...e];r.sort((i,s)=>Dd(i,s,t)),f(r,e)}var Mo=Er(Do(),1);function vr(e){let r=new Mo.XMLParser({attributesGroupName:"@_",ignoreDeclaration:!0,parseAttributeValue:!0,ignoreAttributes:!1,removeNSPrefix:!0,trimValues:!0,isArray:o=>o==="adaptationset"||o==="representation",transformTagName:o=>o.toLowerCase(),transformAttributeName:o=>o.toLowerCase()}).parse(e),i=r.mpd?.period?.adaptationset;if(!Array.isArray(i))return w("Invalid MPD XML");let s="unknown";{let o=r.mpd?.["@_"]?.["@_mediapresentationduration"];if(typeof o=="string"){let d=/\d+(\.\d+)?S/,c=/\d+M/,g=d.exec(o),v=c.exec(o);(g||v)&&(s=0,g&&g.length>0&&(s=parseFloat(g[0])),v&&v.length>0&&(s+=60*(parseFloat(v[0])||0)))}}let n=0,a=[];for(let o of i){let d=[o];"contentcomponent"in o&&"@_"in o.contentcomponent&&d.push(o.contentcomponent),"segmenttemplate"in o&&"@_"in o.segmenttemplate&&d.push(o.segmenttemplate);let c={bitrate:l,content_type:void 0,mime_type:void 0,codecs:void 0,width:void 0,height:void 0,framerate:l},g=(y,O)=>{let p={...O};for(let x of y){let _=x["@_"]??[];for(let A of Object.keys(_)){if(A==="@_bandwidth"){let h=_["@_bandwidth"];typeof h=="number"&&(p.bitrate=u(h))}if(A==="@_contenttype"){let h=_["@_contenttype"];typeof h=="string"&&(p.content_type=h)}if(A==="@_mimetype"){let h=_["@_mimetype"];typeof h=="string"&&(p.mime_type=h)}if(A==="@_codecs"){let h=_["@_codecs"];typeof h=="string"&&(p.codecs=h)}if(A==="@_width"){let h=_["@_width"];typeof h=="number"&&(p.width=h)}if(A==="@_height"){let h=_["@_height"];typeof h=="number"&&(p.height=h)}if(A==="@_framerate"){let h=_["@_framerate"];typeof h=="number"&&(p.framerate=u(h))}}}return p};c=g(d,c);let v=o.representation;if(!Array.isArray(v))break;for(let y of v){let O=n.toString();n++;let p=g([y],c),{codecs:x,mime_type:_,bitrate:A,width:h,height:N,framerate:C}=p,P=w("Invalid mimetype/codecs");if(typeof _=="string"&&typeof x=="string"){let ye=`${_}; codecs="${x}"`;P=S(ye)}if(P.isErr()){console.warn("Failed to parse mimetype from",_,x);continue}let{av_codecs:qe,container:_r}=P.unwrap(),Ar=We(qe,ye=>({codec:M(ye),bitrate:A}),ye=>{let br=R(ye),Tr=C,dt=l,pt=l;return typeof h=="number"&&typeof N=="number"&&(pt=u(he(N)),dt=u({width:h,height:N})),{codec:br,bitrate:A,fps:Tr,dimensions:dt,quality:pt}}),yr={builder:"MPD",protocol:"dash",content_length:l,duration:s,container:E(_r),av:Ar};a.push([yr,O])}}return T(a)}function Xp(e){if(!ot(e,"mime_type")||!ot(e,"codecs"))return w("Missing mimeType and/or codecs");let t=`${e.mime_type}; codecs="${e.codecs}"`,r=S(t);if(r.isErr())return new w(`mimetype couldn't get parsed: ${t} (${r.unwrapErr()})`);let i=r.unwrap(),s=E(i.container),n="unknown";Ce(e,"duration")&&(n=e.duration);let a=l;Ce(e,"bitrate")?a=u(e.bitrate):Ce(e,"avg_bitrate")&&(a=u(e.avg_bitrate));let o=We(i.av_codecs,d=>({codec:M(d),bitrate:a}),d=>{let c=R(d),g=l,v=l,y=l;return Ce(e,"framerate")&&(g=u(e.framerate)),Ce(e,"width")&&Ce(e,"height")&&(y=u(he(e.height)),v=u({height:e.height,width:e.width})),{codec:c,bitrate:a,fps:g,dimensions:v,quality:y}});return T({builder:"JsonMPD",protocol:"dash",content_length:l,duration:n,container:s,av:o})}function Co(){I("dash.mts");let e=`
    <?xml version="1.0" encoding="UTF-8"?>
    <MPD xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:mpeg:dash:schema:mpd:2011" profiles="urn:mpeg:dash:profile:isoff-live:2011" type="static" mediaPresentationDuration="PT3M0.200S" minBufferTime="PT6.000S">
        <BaseURL>../</BaseURL>
        <Period start="PT0.000S" duration="PT3M0.200S">
            <AdaptationSet bitstreamSwitching="true">
                <ContentComponent contentType="video"></ContentComponent>
                <Representation id="video-0c079186" codecs="avc1.640820" mimeType="video/mp4" width="1280" height="720" frameRate="25" startWithSAP="1" bandwidth="1933000">
                    <SegmentTemplate timescale="48000" media="0c079186/chop/segment-$Number$.m4s" initialization="0c079186/chop/segment-0.mp4?r=dXM%3D">
                        <SegmentTimeline>
                            <S t="0" d="291840" r="15"></S>
                            <S t="4669440" d="288000"></S>
                            <S t="4957440" d="295680"></S>
                            <S t="5253120" d="291840" r="10"></S>
                            <S t="8463360" d="186240"></S>
                        </SegmentTimeline>
                    </SegmentTemplate>
                </Representation>
                <Representation id="video-5f503586" codecs="avc1.640828" mimeType="video/mp4" width="1920" height="1080" frameRate="25" startWithSAP="1" bandwidth="4303000">
                    <SegmentTemplate timescale="48000" media="5f503586/chop/segment-$Number$.m4s" initialization="5f503586/chop/segment-0.mp4?r=dXMtZWFzdDE%3D">
                        <SegmentTimeline>
                            <S t="0" d="291840" r="15"></S>
                            <S t="4669440" d="288000"></S>
                            <S t="4957440" d="295680"></S>
                            <S t="5253120" d="291840" r="10"></S>
                            <S t="8463360" d="186240"></S>
                        </SegmentTimeline>
                    </SegmentTemplate>
                </Representation>
                <Representation id="video-69bfec13" codecs="avc1.64081F" mimeType="video/mp4" width="960" height="540" frameRate="25" startWithSAP="1" bandwidth="1072000">
                    <SegmentTemplate timescale="48000" media="69bfec13/chop/segment-$Number$.m4s" initialization="69bfec13/chop/segment-0.mp4?r=dXM%3D">
                        <SegmentTimeline>
                            <S t="0" d="291840" r="15"></S>
                            <S t="4669440" d="288000"></S>
                            <S t="4957440" d="295680"></S>
                            <S t="5253120" d="291840" r="10"></S>
                            <S t="8463360" d="186240"></S>
                        </SegmentTimeline>
                    </SegmentTemplate>
                </Representation>
                <Representation id="video-84e4565b" codecs="avc1.64081E" mimeType="video/mp4" width="640" height="360" frameRate="25" startWithSAP="1" bandwidth="432000">
                    <SegmentTemplate timescale="48000" media="84e4565b/chop/segment-$Number$.m4s" initialization="84e4565b/chop/segment-0.mp4?r=dXM%3D">
                        <SegmentTimeline>
                            <S t="0" d="291840" r="15"></S>
                            <S t="4669440" d="288000"></S>
                            <S t="4957440" d="295680"></S>
                            <S t="5253120" d="291840" r="10"></S>
                            <S t="8463360" d="186240"></S>
                        </SegmentTimeline>
                    </SegmentTemplate>
                </Representation>
            </AdaptationSet>
            <AdaptationSet>
                <ContentComponent contentType="audio"></ContentComponent>
                <Roles schemeIdUri="urn:mpeg:dash:role:2011" value="main"></Roles>
                <Representation id="audio-0c079186" codecs="mp4a.40.2" mimeType="audio/mp4" startWithSAP="1" bandwidth="139000" audioSamplingRate="48000">
                    <AudioChannelConfiguration schemeIdUri="urn:mpeg:dash:23003:3:audio_channel_configuration:2011" value="2"></AudioChannelConfiguration>
                    <SegmentTemplate timescale="48000" media="../audio/0c079186/chop/segment-$Number$.m4s" initialization="../audio/0c079186/chop/segment-0.mp4?r=dXM%3D">
                        <SegmentTimeline>
                            <S t="0" d="291840" r="15"></S>
                            <S t="4669440" d="287744"></S>
                            <S t="4957184" d="295936"></S>
                            <S t="5253120" d="291840" r="10"></S>
                            <S t="8463360" d="186368"></S>
                        </SegmentTimeline>
                    </SegmentTemplate>
                </Representation>
            </AdaptationSet>
        </Period>
    </MPD>
  `,t=`
    <MPD mediaPresentationDuration="PT634.566S" minBufferTime="PT2.00S" profiles="urn:hbbtv:dash:profile:isoff-live:2012,urn:mpeg:dash:profile:isoff-live:2011" type="static" xmlns="urn:mpeg:dash:schema:mpd:2011" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:mpeg:DASH:schema:MPD:2011 DASH-MPD.xsd">
     <BaseURL>./</BaseURL>
     <Period>
      <AdaptationSet mimeType="video/mp4" contentType="video" subsegmentAlignment="true" subsegmentStartsWithSAP="1" par="16:9">
       <SegmentTemplate duration="120" timescale="30" media="$RepresentationID$/$RepresentationID$_$Number$.m4v" startNumber="1" initialization="$RepresentationID$/$RepresentationID$_0.m4v"/>
       <Representation id="bbb_30fps_1024x576_2500k" codecs="avc1.64001f" bandwidth="3134488" width="1024" height="576" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_1280x720_4000k" codecs="avc1.64001f" bandwidth="4952892" width="1280" height="720" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_1920x1080_8000k" codecs="avc1.640028" bandwidth="9914554" width="1920" height="1080" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_320x180_200k" codecs="avc1.64000d" bandwidth="254320" width="320" height="180" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_320x180_400k" codecs="avc1.64000d" bandwidth="507246" width="320" height="180" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_480x270_600k" codecs="avc1.640015" bandwidth="759798" width="480" height="270" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_640x360_1000k" codecs="avc1.64001e" bandwidth="1254758" width="640" height="360" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_640x360_800k" codecs="avc1.64001e" bandwidth="1013310" width="640" height="360" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_768x432_1500k" codecs="avc1.64001e" bandwidth="1883700" width="768" height="432" frameRate="30" sar="1:1" scanType="progressive"/>
       <Representation id="bbb_30fps_3840x2160_12000k" codecs="avc1.640033" bandwidth="14931538" width="3840" height="2160" frameRate="30" sar="1:1" scanType="progressive"/>
      </AdaptationSet>
      <AdaptationSet mimeType="audio/mp4" contentType="audio" subsegmentAlignment="true" subsegmentStartsWithSAP="1">
       <Accessibility schemeIdUri="urn:tva:metadata:cs:AudioPurposeCS:2007" value="6"/>
       <Role schemeIdUri="urn:mpeg:dash:role:2011" value="main"/>
       <SegmentTemplate duration="192512" timescale="48000" media="$RepresentationID$/$RepresentationID$_$Number$.m4a" startNumber="1" initialization="$RepresentationID$/$RepresentationID$_0.m4a"/>
       <Representation id="bbb_a64k" codecs="mp4a.40.5" bandwidth="67071" audioSamplingRate="48000">
        <AudioChannelConfiguration schemeIdUri="urn:mpeg:dash:23003:3:audio_channel_configuration:2011" value="2"/>
       </Representation>
      </AdaptationSet>
     </Period>
    </MPD>
  `,r=`
    <MPD mediaPresentationDuration="PT634.566S" minBufferTime="PT2.00S" profiles="urn:hbbtv:dash:profile:isoff-live:2012,urn:mpeg:dash:profile:isoff-live:2011" type="static" xmlns="urn:mpeg:dash:schema:mpd:2011" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:mpeg:DASH:schema:MPD:2011 DASH-MPD.xsd">
     <BaseURL>./</BaseURL>
     <Period>
      <AdaptationSet mimeType="video/mp4" contentType="video" subsegmentAlignment="true" subsegmentStartsWithSAP="1" par="16:9">
       <SegmentTemplate duration="120" timescale="30" media="$RepresentationID$/$RepresentationID$_$Number$.m4v" startNumber="1" initialization="$RepresentationID$/$RepresentationID$_0.m4v"/>
       <Representation id="bbb_30fps_1024x576_2500k" codecs="avc1.64001f" bandwidth="3134488" width="1024" height="576" frameRate="30" sar="1:1" scanType="progressive"/>
      </AdaptationSet>
     </Period>
    </MPD>
  `;f(vr("xxx").isErr(),!0);let i=vr(e).unwrap(),s=vr(t).unwrap(),n=vr(r);f(i.length,5),f(i[0],[{builder:"MPD",content_length:l,protocol:"dash",duration:180.2,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(1933e3),fps:u(25),dimensions:u({width:1280,height:720}),quality:u("720")}}},"0"]),f(i[1],[{builder:"MPD",content_length:l,protocol:"dash",duration:180.2,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(4303e3),fps:u(25),dimensions:u({width:1920,height:1080}),quality:u("1080")}}},"1"]),f(i[2],[{builder:"MPD",content_length:l,protocol:"dash",duration:180.2,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(1072e3),fps:u(25),dimensions:u({width:960,height:540}),quality:u("480")}}},"2"]),f(i[3],[{builder:"MPD",content_length:l,protocol:"dash",duration:180.2,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(432e3),fps:u(25),dimensions:u({width:640,height:360}),quality:u("360")}}},"3"]),f(i[4],[{builder:"MPD",content_length:l,protocol:"dash",duration:180.2,container:E("Mp4"),av:{video:!1,audio:{codec:M("AAC"),bitrate:u(139e3)}}},"4"]),f(s.length,11),f(s[0],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(3134488),fps:u(30),dimensions:u({width:1024,height:576}),quality:u("480")}}},"0"]),f(s[1],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(4952892),fps:u(30),dimensions:u({width:1280,height:720}),quality:u("720")}}},"1"]),f(s[2],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(9914554),fps:u(30),dimensions:u({width:1920,height:1080}),quality:u("1080")}}},"2"]),f(s[3],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(254320),fps:u(30),dimensions:u({width:320,height:180}),quality:u("240")}}},"3"]),f(s[4],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(507246),fps:u(30),dimensions:u({width:320,height:180}),quality:u("240")}}},"4"]),f(s[5],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(759798),fps:u(30),dimensions:u({width:480,height:270}),quality:u("240")}}},"5"]),f(s[6],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(1254758),fps:u(30),dimensions:u({width:640,height:360}),quality:u("360")}}},"6"]),f(s[7],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(1013310),fps:u(30),dimensions:u({width:640,height:360}),quality:u("360")}}},"7"]),f(s[8],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(1883700),fps:u(30),dimensions:u({width:768,height:432}),quality:u("360")}}},"8"]),f(s[9],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(14931538),fps:u(30),dimensions:u({width:3840,height:2160}),quality:u("2160")}}},"9"]),f(s[10],[{builder:"MPD",content_length:l,protocol:"dash",duration:634.566,container:E("Mp4"),av:{audio:{codec:M("AAC"),bitrate:u(67071)},video:!1}},"10"]),f(n.unwrap(),[[{builder:"MPD",protocol:"dash",content_length:l,duration:634.566,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(3134488),fps:u(30),dimensions:u({width:1024,height:576}),quality:u("480")}}},"0"]]);let o=Xp({id:"292efc34",base_url:"video/",format:"dash",mime_type:"video/mp4",codecs:"avc1.64002A",bitrate:5857e3,avg_bitrate:5388e3,duration:900.9,framerate:23.976023976023978,width:1920,height:1080,max_segment_duration:7,init_segment:"AAAAIGZ0eXBkYXNoAAAAAGRhc2htcDQybXA0MWlzbzYAAAMhbW9vdgAAAGxtdmhkAAAAAOCQErXgkBK1AABdwAAAAAAAAQAAAQAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAACFpb2RzAAAAABCAgIAQAE////9//w6AgIAEAAAAAQAAAlB0cmFrAAAAXHRraGQAAAAH4JASteCQErUAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAABAAAAAB4AAAAQ4AAAAAAAkZWR0cwAAABxlbHN0AAAAAAAAAAEAAAAAAAALuwABAAAAAAHIbWRpYQAAACBtZGhkAAAAAOCQErXgkBK1AABdwAAAAABVxAAAAAAANmhkbHIAAAAAAAAAAHZpZGUAAAAAAAAAAAAAAABMLVNNQVNIIFZpZGVvIEhhbmRsZXIAAAABam1pbmYAAAAUdm1oZAAAAAEAAAAAAAAAAAAAACRkaW5mAAAAHGRyZWYAAAAAAAAAAQAAAAx1cmwgAAAAAQAAASpzdGJsAAAAsnN0c2QAAAAAAAAAAQAAAKJhdmMxAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAB4AEOABIAAAASAAAAAAAAAABCkFWQyBDb2RpbmcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP//AAAAOWF2Y0MBZAAq/+EAHWdkACqs2YB4AiflmoCAgKAAAAMAIAAXcAHjBjNAAQAFaOl7LIv9+PgAAAAAE2NvbHJuY2x4AAEAAQABAAAAABBzdHRzAAAAAAAAAAAAAAAQc3RzYwAAAAAAAAAAAAAAFHN0c3oAAAAAAAAAAAAAAAAAAAAQc3RjbwAAAAAAAAAAAAAAGHNncGQBAAAAcm9sbAAAAAIAAAAAAAAAFHNiZ3AAAAAAcm9sbAAAAAAAAAA8bXZleAAAABRtZWhkAQAAAAAAAAABSetgAAAAIHRyZXgAAAAAAAAAAQAAAAEAAAABAAAAAAABAAA=",index_segment:"292efc34.mp4?r=dXMtd2VzdDE%3D&range=833-2664"});f(o.unwrap(),{builder:"JsonMPD",protocol:"dash",content_length:l,duration:900.9,container:E("Mp4"),av:{audio:!1,video:{codec:R("H264"),bitrate:u(5857e3),fps:u(23.976023976023978),dimensions:u({width:1920,height:1080}),quality:u("1080")}}})}Ys();Ls();Xs();ks();Zs();Qs();Gs();zs();eo();Co();ji();})();
/*! Bundled license information:

m3u8-parser/dist/m3u8-parser.es.js:
  (*! @name m3u8-parser @version 7.1.0 @license Apache-2.0 *)
*/
