var Ce=Object.create;var me=Object.defineProperty;var Ee=Object.getOwnPropertyDescriptor;var ke=Object.getOwnPropertyNames;var Ie=Object.getPrototypeOf,Ve=Object.prototype.hasOwnProperty;var Pe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var je=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of ke(t))!Ve.call(e,i)&&i!==r&&me(e,i,{get:()=>t[i],enumerable:!(o=Ee(t,i))||o.enumerable});return e};var X=(e,t,r)=>(r=e!=null?Ce(Ie(e)):{},je(t||!e||!e.__esModule?me(r,"default",{value:e,enumerable:!0}):r,e));var U=Pe((ie,de)=>{(function(e,t){if(typeof define=="function"&&define.amd)define("webextension-polyfill",["module"],t);else if(typeof ie<"u")t(de);else{var r={exports:{}};t(r),e.browser=r.exports}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:ie,function(e){"use strict";if(!globalThis.chrome?.runtime?.id)throw new Error("This script should only be loaded in a browser extension.");if(typeof globalThis.browser>"u"||Object.getPrototypeOf(globalThis.browser)!==Object.prototype){let t="The message port closed before a response was received.",r=o=>{let i={alarms:{clear:{minArgs:0,maxArgs:1},clearAll:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getAll:{minArgs:0,maxArgs:0}},bookmarks:{create:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},getChildren:{minArgs:1,maxArgs:1},getRecent:{minArgs:1,maxArgs:1},getSubTree:{minArgs:1,maxArgs:1},getTree:{minArgs:0,maxArgs:0},move:{minArgs:2,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeTree:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}},browserAction:{disable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},enable:{minArgs:0,maxArgs:1,fallbackToNoCallback:!0},getBadgeBackgroundColor:{minArgs:1,maxArgs:1},getBadgeText:{minArgs:1,maxArgs:1},getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},openPopup:{minArgs:0,maxArgs:0},setBadgeBackgroundColor:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setBadgeText:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},browsingData:{remove:{minArgs:2,maxArgs:2},removeCache:{minArgs:1,maxArgs:1},removeCookies:{minArgs:1,maxArgs:1},removeDownloads:{minArgs:1,maxArgs:1},removeFormData:{minArgs:1,maxArgs:1},removeHistory:{minArgs:1,maxArgs:1},removeLocalStorage:{minArgs:1,maxArgs:1},removePasswords:{minArgs:1,maxArgs:1},removePluginData:{minArgs:1,maxArgs:1},settings:{minArgs:0,maxArgs:0}},commands:{getAll:{minArgs:0,maxArgs:0}},contextMenus:{remove:{minArgs:1,maxArgs:1},removeAll:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},cookies:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:1,maxArgs:1},getAllCookieStores:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},devtools:{inspectedWindow:{eval:{minArgs:1,maxArgs:2,singleCallbackArg:!1}},panels:{create:{minArgs:3,maxArgs:3,singleCallbackArg:!0},elements:{createSidebarPane:{minArgs:1,maxArgs:1}}}},downloads:{cancel:{minArgs:1,maxArgs:1},download:{minArgs:1,maxArgs:1},erase:{minArgs:1,maxArgs:1},getFileIcon:{minArgs:1,maxArgs:2},open:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},pause:{minArgs:1,maxArgs:1},removeFile:{minArgs:1,maxArgs:1},resume:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},extension:{isAllowedFileSchemeAccess:{minArgs:0,maxArgs:0},isAllowedIncognitoAccess:{minArgs:0,maxArgs:0}},history:{addUrl:{minArgs:1,maxArgs:1},deleteAll:{minArgs:0,maxArgs:0},deleteRange:{minArgs:1,maxArgs:1},deleteUrl:{minArgs:1,maxArgs:1},getVisits:{minArgs:1,maxArgs:1},search:{minArgs:1,maxArgs:1}},i18n:{detectLanguage:{minArgs:1,maxArgs:1},getAcceptLanguages:{minArgs:0,maxArgs:0}},identity:{launchWebAuthFlow:{minArgs:1,maxArgs:1}},idle:{queryState:{minArgs:1,maxArgs:1}},management:{get:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},getSelf:{minArgs:0,maxArgs:0},setEnabled:{minArgs:2,maxArgs:2},uninstallSelf:{minArgs:0,maxArgs:1}},notifications:{clear:{minArgs:1,maxArgs:1},create:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:0},getPermissionLevel:{minArgs:0,maxArgs:0},update:{minArgs:2,maxArgs:2}},pageAction:{getPopup:{minArgs:1,maxArgs:1},getTitle:{minArgs:1,maxArgs:1},hide:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setIcon:{minArgs:1,maxArgs:1},setPopup:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},setTitle:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0},show:{minArgs:1,maxArgs:1,fallbackToNoCallback:!0}},permissions:{contains:{minArgs:1,maxArgs:1},getAll:{minArgs:0,maxArgs:0},remove:{minArgs:1,maxArgs:1},request:{minArgs:1,maxArgs:1}},runtime:{getBackgroundPage:{minArgs:0,maxArgs:0},getPlatformInfo:{minArgs:0,maxArgs:0},openOptionsPage:{minArgs:0,maxArgs:0},requestUpdateCheck:{minArgs:0,maxArgs:0},sendMessage:{minArgs:1,maxArgs:3},sendNativeMessage:{minArgs:2,maxArgs:2},setUninstallURL:{minArgs:1,maxArgs:1}},sessions:{getDevices:{minArgs:0,maxArgs:1},getRecentlyClosed:{minArgs:0,maxArgs:1},restore:{minArgs:0,maxArgs:1}},storage:{local:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}},managed:{get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1}},sync:{clear:{minArgs:0,maxArgs:0},get:{minArgs:0,maxArgs:1},getBytesInUse:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}}},tabs:{captureVisibleTab:{minArgs:0,maxArgs:2},create:{minArgs:1,maxArgs:1},detectLanguage:{minArgs:0,maxArgs:1},discard:{minArgs:0,maxArgs:1},duplicate:{minArgs:1,maxArgs:1},executeScript:{minArgs:1,maxArgs:2},get:{minArgs:1,maxArgs:1},getCurrent:{minArgs:0,maxArgs:0},getZoom:{minArgs:0,maxArgs:1},getZoomSettings:{minArgs:0,maxArgs:1},goBack:{minArgs:0,maxArgs:1},goForward:{minArgs:0,maxArgs:1},highlight:{minArgs:1,maxArgs:1},insertCSS:{minArgs:1,maxArgs:2},move:{minArgs:2,maxArgs:2},query:{minArgs:1,maxArgs:1},reload:{minArgs:0,maxArgs:2},remove:{minArgs:1,maxArgs:1},removeCSS:{minArgs:1,maxArgs:2},sendMessage:{minArgs:2,maxArgs:3},setZoom:{minArgs:1,maxArgs:2},setZoomSettings:{minArgs:1,maxArgs:2},update:{minArgs:1,maxArgs:2}},topSites:{get:{minArgs:0,maxArgs:0}},webNavigation:{getAllFrames:{minArgs:1,maxArgs:1},getFrame:{minArgs:1,maxArgs:1}},webRequest:{handlerBehaviorChanged:{minArgs:0,maxArgs:0}},windows:{create:{minArgs:0,maxArgs:1},get:{minArgs:1,maxArgs:2},getAll:{minArgs:0,maxArgs:1},getCurrent:{minArgs:0,maxArgs:1},getLastFocused:{minArgs:0,maxArgs:1},remove:{minArgs:1,maxArgs:1},update:{minArgs:2,maxArgs:2}}};if(Object.keys(i).length===0)throw new Error("api-metadata.json has not been included in browser-polyfill");class n extends WeakMap{constructor(l,f=void 0){super(f),this.createItem=l}get(l){return this.has(l)||this.set(l,this.createItem(l)),super.get(l)}}let u=a=>a&&typeof a=="object"&&typeof a.then=="function",c=(a,l)=>(...f)=>{o.runtime.lastError?a.reject(new Error(o.runtime.lastError.message)):l.singleCallbackArg||f.length<=1&&l.singleCallbackArg!==!1?a.resolve(f[0]):a.resolve(f)},_=a=>a==1?"argument":"arguments",s=(a,l)=>function(g,...w){if(w.length<l.minArgs)throw new Error(`Expected at least ${l.minArgs} ${_(l.minArgs)} for ${a}(), got ${w.length}`);if(w.length>l.maxArgs)throw new Error(`Expected at most ${l.maxArgs} ${_(l.maxArgs)} for ${a}(), got ${w.length}`);return new Promise((O,C)=>{if(l.fallbackToNoCallback)try{g[a](...w,c({resolve:O,reject:C},l))}catch(d){console.warn(`${a} API method doesn't seem to support the callback parameter, falling back to call it without a callback: `,d),g[a](...w),l.fallbackToNoCallback=!1,l.noCallback=!0,O()}else l.noCallback?(g[a](...w),O()):g[a](...w,c({resolve:O,reject:C},l))})},y=(a,l,f)=>new Proxy(l,{apply(g,w,O){return f.call(w,a,...O)}}),x=Function.call.bind(Object.prototype.hasOwnProperty),b=(a,l={},f={})=>{let g=Object.create(null),w={has(C,d){return d in a||d in g},get(C,d,E){if(d in g)return g[d];if(!(d in a))return;let h=a[d];if(typeof h=="function")if(typeof l[d]=="function")h=y(a,a[d],l[d]);else if(x(f,d)){let H=s(d,f[d]);h=y(a,a[d],H)}else h=h.bind(a);else if(typeof h=="object"&&h!==null&&(x(l,d)||x(f,d)))h=b(h,l[d],f[d]);else if(x(f,"*"))h=b(h,l[d],f["*"]);else return Object.defineProperty(g,d,{configurable:!0,enumerable:!0,get(){return a[d]},set(H){a[d]=H}}),h;return g[d]=h,h},set(C,d,E,h){return d in g?g[d]=E:a[d]=E,!0},defineProperty(C,d,E){return Reflect.defineProperty(g,d,E)},deleteProperty(C,d){return Reflect.deleteProperty(g,d)}},O=Object.create(a);return new Proxy(O,w)},M=a=>({addListener(l,f,...g){l.addListener(a.get(f),...g)},hasListener(l,f){return l.hasListener(a.get(f))},removeListener(l,f){l.removeListener(a.get(f))}}),k=new n(a=>typeof a!="function"?a:function(f){let g=b(f,{},{getContent:{minArgs:0,maxArgs:0}});a(g)}),F=new n(a=>typeof a!="function"?a:function(f,g,w){let O=!1,C,d=new Promise(Q=>{C=function(I){O=!0,Q(I)}}),E;try{E=a(f,g,C)}catch(Q){E=Promise.reject(Q)}let h=E!==!0&&u(E);if(E!==!0&&!h&&!O)return!1;let H=Q=>{Q.then(I=>{w(I)},I=>{let ne;I&&(I instanceof Error||typeof I.message=="string")?ne=I.message:ne="An unexpected error occurred",w({__mozWebExtensionPolyfillReject__:!0,message:ne})}).catch(I=>{console.error("Failed to send onMessage rejected reply",I)})};return H(h?E:d),!0}),Y=({reject:a,resolve:l},f)=>{o.runtime.lastError?o.runtime.lastError.message===t?l():a(new Error(o.runtime.lastError.message)):f&&f.__mozWebExtensionPolyfillReject__?a(new Error(f.message)):l(f)},Z=(a,l,f,...g)=>{if(g.length<l.minArgs)throw new Error(`Expected at least ${l.minArgs} ${_(l.minArgs)} for ${a}(), got ${g.length}`);if(g.length>l.maxArgs)throw new Error(`Expected at most ${l.maxArgs} ${_(l.maxArgs)} for ${a}(), got ${g.length}`);return new Promise((w,O)=>{let C=Y.bind(null,{resolve:w,reject:O});g.push(C),f.sendMessage(...g)})},$={devtools:{network:{onRequestFinished:M(k)}},runtime:{onMessage:M(F),onMessageExternal:M(F),sendMessage:Z.bind(null,"sendMessage",{minArgs:1,maxArgs:3})},tabs:{sendMessage:Z.bind(null,"sendMessage",{minArgs:2,maxArgs:3})}},oe={clear:{minArgs:1,maxArgs:1},get:{minArgs:1,maxArgs:1},set:{minArgs:1,maxArgs:1}};return i.privacy={network:{"*":oe},services:{"*":oe},websites:{"*":oe}},b(o,$,i)};e.exports=r(chrome)}else e.exports=globalThis.browser})});var Me=X(U(),1);var fe=X(U(),1);function pe(e){return e?e.replaceAll("&","&amp;").replaceAll("<","&lt;").replaceAll(">","&gt;").replaceAll('"',"&quot;").replaceAll("'","&#039;"):""}function R(e,t,r){let o=()=>(console.error(`Requesting unknown i18n string ${e}`),e);t=t.map(i=>i.toString()).map(pe);try{if(e in r){let i=r[e],n=1;for(let u=0;u<t.length;u++)i=i.replace(`$${n}`,t[u]);return i}else{let i=fe.default.i18n.getMessage(e,t);return i||o()}}catch{return o()}}function ae(e,t){for(let r of Array.from(e.querySelectorAll("[data-i18n]"))){let o=r.dataset.i18nArgs,i=r.dataset.i18nAttr,n;o?n=R(r.dataset.i18n,JSON.parse(o),t):n=R(r.dataset.i18n,[],t),i?r.setAttribute(i,n):r.textContent=n}}function ge(e,t){let r=new Date;r.setHours(0,0,0,0);let o=new Date(e);o.setHours(0,0,0,0);let i=r.getTime(),n=o.getTime(),u=Math.floor((i-n)/(1e3*60*60*24));return u==0?R("v9_date_today",[],t):u==1?R("v9_date_yesterday",[],t):u<8?R("v9_date_x_days_ago",[u],t):R("v9_date_long_ago",[],t)}var G=X(U(),1);function N(e){var t=String(e);if(t==="[object Object]")try{t=JSON.stringify(e)}catch{}return t}var Ne=function(){function e(){}return e.prototype.isSome=function(){return!1},e.prototype.isNone=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t))},e.prototype.unwrap=function(){throw new Error("Tried to unwrap None")},e.prototype.map=function(t){return this},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t()},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t()},e.prototype.andThen=function(t){return this},e.prototype.toResult=function(t){return v(t)},e.prototype.toString=function(){return"None"},e}(),m=new Ne;Object.freeze(m);var De=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isSome=function(){return!0},e.prototype.isNone=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.unwrap=function(){return this.value},e.prototype.map=function(t){return A(t(this.value))},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.andThen=function(t){return t(this.value)},e.prototype.toResult=function(t){return p(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Some(".concat(N(this.value),")")},e.EMPTY=new e(void 0),e}(),A=De,L;(function(e){function t(){for(var i=[],n=0;n<arguments.length;n++)i[n]=arguments[n];for(var u=[],c=0,_=i;c<_.length;c++){var s=_[c];if(s.isSome())u.push(s.value);else return s}return A(u)}e.all=t;function r(){for(var i=[],n=0;n<arguments.length;n++)i[n]=arguments[n];for(var u=0,c=i;u<c.length;u++){var _=c[u];return _.isSome(),_}return m}e.any=r;function o(i){return i instanceof A||i===m}e.isOption=o})(L||(L={}));var Re=function(){function e(t){if(!(this instanceof e))return new e(t);this.error=t;var r=new Error().stack.split(`
`).slice(2);r&&r.length>0&&r[0].includes("ErrImpl")&&r.shift(),this._stack=r.join(`
`)}return e.prototype.isOk=function(){return!1},e.prototype.isErr=function(){return!0},e.prototype[Symbol.iterator]=function(){return{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return t},e.prototype.unwrapOr=function(t){return t},e.prototype.expect=function(t){throw new Error("".concat(t," - Error: ").concat(N(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.expectErr=function(t){return this.error},e.prototype.unwrap=function(){throw new Error("Tried to unwrap Error: ".concat(N(this.error),`
`).concat(this._stack),{cause:this.error})},e.prototype.unwrapErr=function(){return this.error},e.prototype.map=function(t){return this},e.prototype.andThen=function(t){return this},e.prototype.mapErr=function(t){return new v(t(this.error))},e.prototype.mapOr=function(t,r){return t},e.prototype.mapOrElse=function(t,r){return t(this.error)},e.prototype.or=function(t){return t},e.prototype.orElse=function(t){return t(this.error)},e.prototype.toOption=function(){return m},e.prototype.toString=function(){return"Err(".concat(N(this.error),")")},Object.defineProperty(e.prototype,"stack",{get:function(){return"".concat(this,`
`).concat(this._stack)},enumerable:!1,configurable:!0}),e.prototype.toAsyncResult=function(){return new se(this)},e.EMPTY=new e(void 0),e}();var v=Re,Fe=function(){function e(t){if(!(this instanceof e))return new e(t);this.value=t}return e.prototype.isOk=function(){return!0},e.prototype.isErr=function(){return!1},e.prototype[Symbol.iterator]=function(){var t=Object(this.value);return Symbol.iterator in t?t[Symbol.iterator]():{next:function(){return{done:!0,value:void 0}}}},e.prototype.else=function(t){return this.value},e.prototype.unwrapOr=function(t){return this.value},e.prototype.expect=function(t){return this.value},e.prototype.expectErr=function(t){throw new Error(t)},e.prototype.unwrap=function(){return this.value},e.prototype.unwrapErr=function(){throw new Error("Tried to unwrap Ok: ".concat(N(this.value)),{cause:this.value})},e.prototype.map=function(t){return new p(t(this.value))},e.prototype.andThen=function(t){return t(this.value)},e.prototype.mapErr=function(t){return this},e.prototype.mapOr=function(t,r){return r(this.value)},e.prototype.mapOrElse=function(t,r){return r(this.value)},e.prototype.or=function(t){return this},e.prototype.orElse=function(t){return this},e.prototype.toOption=function(){return A(this.value)},e.prototype.safeUnwrap=function(){return this.value},e.prototype.toString=function(){return"Ok(".concat(N(this.value),")")},e.prototype.toAsyncResult=function(){return new se(this)},e.EMPTY=new e(void 0),e}();var p=Fe,z;(function(e){function t(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];for(var _=[],s=0,y=u;s<y.length;s++){var x=y[s];if(x.isOk())_.push(x.value);else return x}return new p(_)}e.all=t;function r(){for(var u=[],c=0;c<arguments.length;c++)u[c]=arguments[c];for(var _=[],s=0,y=u;s<y.length;s++){var x=y[s];if(x.isOk())return x;_.push(x.error)}return new v(_)}e.any=r;function o(u){try{return new p(u())}catch(c){return new v(c)}}e.wrap=o;function i(u){try{return u().then(function(c){return new p(c)}).catch(function(c){return new v(c)})}catch(c){return Promise.resolve(new v(c))}}e.wrapAsync=i;function n(u){return u instanceof v||u instanceof p}e.isResult=n})(z||(z={}));var _e=function(e,t,r,o){function i(n){return n instanceof r?n:new r(function(u){u(n)})}return new(r||(r=Promise))(function(n,u){function c(y){try{s(o.next(y))}catch(x){u(x)}}function _(y){try{s(o.throw(y))}catch(x){u(x)}}function s(y){y.done?n(y.value):i(y.value).then(c,_)}s((o=o.apply(e,t||[])).next())})},ye=function(e,t){var r={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},o,i,n,u;return u={next:c(0),throw:c(1),return:c(2)},typeof Symbol=="function"&&(u[Symbol.iterator]=function(){return this}),u;function c(s){return function(y){return _([s,y])}}function _(s){if(o)throw new TypeError("Generator is already executing.");for(;u&&(u=0,s[0]&&(r=0)),r;)try{if(o=1,i&&(n=s[0]&2?i.return:s[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,s[1])).done)return n;switch(i=0,n&&(s=[s[0]&2,n.value]),s[0]){case 0:case 1:n=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,i=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(n=r.trys,!(n=n.length>0&&n[n.length-1])&&(s[0]===6||s[0]===2)){r=0;continue}if(s[0]===3&&(!n||s[1]>n[0]&&s[1]<n[3])){r.label=s[1];break}if(s[0]===6&&r.label<n[1]){r.label=n[1],n=s;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(s);break}n[2]&&r.ops.pop(),r.trys.pop();continue}s=t.call(e,r)}catch(y){s=[6,y],i=0}finally{o=n=0}if(s[0]&5)throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}},se=function(){function e(t){this.promise=Promise.resolve(t)}return e.prototype.andThen=function(t){var r=this;return this.thenInternal(function(o){return _e(r,void 0,void 0,function(){var i;return ye(this,function(n){return o.isErr()?[2,o]:(i=t(o.value),[2,i instanceof e?i.promise:i])})})})},e.prototype.map=function(t){var r=this;return this.thenInternal(function(o){return _e(r,void 0,void 0,function(){var i;return ye(this,function(n){switch(n.label){case 0:return o.isErr()?[2,o]:(i=p,[4,t(o.value)]);case 1:return[2,i.apply(void 0,[n.sent()])]}})})})},e.prototype.thenInternal=function(t){return new e(this.promise.then(t))},e}();var Ae=(e,t)=>typeof e[t]=="string";function V(e){try{if(Ae(e,"__serializer_tag")){if(e.__serializer_tag==="primitive")return p(e.__serializer_value);if(e.__serializer_tag==="regex"){let o=new RegExp(e.__serializer_value);return p(o)}else if(e.__serializer_tag==="array"){let o=[];for(let i of e.__serializer_value){let n=V(i);if(n.isErr())return n;o.push(n.unwrap())}return p(o)}else if(e.__serializer_tag==="map"){let o=[];for(let i of e.__serializer_value){let n=V(i);if(n.isErr())return n;o.push(n.unwrap())}return p(new Map(o))}else if(e.__serializer_tag==="set"){let o=[];for(let i of e.__serializer_value){let n=V(i);if(n.isErr())return n;o.push(n.unwrap())}return p(new Set(o))}else if(e.__serializer_tag==="result_ok"){let o=e.__serializer_value,i=V(o);return i.isErr()?i:p(p(i.unwrap()))}else if(e.__serializer_tag==="result_err"){let o=e.__serializer_value,i=V(o);return i.isErr()?i:p(v(i.unwrap()))}else if(e.__serializer_tag==="option_some"){let o=e.__serializer_value,i=V(o);return i.isErr()?i:p(A(i.unwrap()))}else if(e.__serializer_tag==="option_none")return p(m)}let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||Array.isArray(e)||e==null)return v("This object was not serialized with Serialize");let r={};for(let o of Object.keys(e))if(typeof o=="string"){let i=V(e[o]);if(i.isErr())return i;r[o]=i.unwrap()}return p(r)}catch{return v("Failed to inspect object. Not JSON?")}}function P(e){let t=typeof e;if(t==="string"||t==="number"||t==="boolean"||t==="undefined"||e==null)return p({__serializer_tag:"primitive",__serializer_value:e});if(e instanceof RegExp)return p({__serializer_tag:"regex",__serializer_value:e.source});if(Array.isArray(e)){let r=e.map(n=>P(n)),o=r.as_iter().find(n=>n.isErr());if(o.isSome())return o.unwrap();let i=r.as_iter().map(n=>n.unwrap()).toArray();return p({__serializer_tag:"array",__serializer_value:i})}else if(e instanceof Map){let r=[...e.entries()].map(n=>P(n)),o=r.as_iter().find(n=>n.isErr());if(o.isSome())return o.unwrap();let i=r.as_iter().map(n=>n.unwrap()).toArray();return p({__serializer_tag:"map",__serializer_value:i})}else if(e instanceof Set){let r=[...e.values()].map(n=>P(n)),o=r.as_iter().find(n=>n.isErr());if(o.isSome())return o.unwrap();let i=r.as_iter().map(n=>n.unwrap()).toArray();return p({__serializer_tag:"set",__serializer_value:i})}else if(z.isResult(e))if(e.isOk()){let r=e.unwrap(),o=P(r);return o.isErr()?o:p({__serializer_tag:"result_ok",__serializer_value:o.unwrap()})}else{let r=e.unwrapErr(),o=P(r);return o.isErr()?o:p({__serializer_tag:"result_err",__serializer_value:o.unwrap()})}else if(L.isOption(e))if(e.isSome()){let r=e.unwrap(),o=P(r);return o.isErr()?o:p({__serializer_tag:"option_some",__serializer_value:o.unwrap()})}else return p({__serializer_tag:"option_none"});else if(t==="object"){let r={},o=e;for(let i of Object.keys(e)){let n=o[i],u=P(n);if(u.isErr())continue;let c=u.unwrap();r[i]=c}return p(r)}else return v("Unsupported value")}function T(e){return Object.assign(e.prototype,{find:function(t){for(let r of this)if(t(r))return A(r);return m},count:function(t){return this.reduce((r,o)=>(t(o)&&r++,r),0)},reduce:function(t,r){let o=r;for(let i of this)o=t(o,i);return o},every:function(t){return!this.any(r=>!t(r))},any:function(t){for(let r of this)if(t(r))return!0;return!1},map:function(t){return this.filterMap(r=>A(t(r)))},filter:function(t){return this.filterMap(r=>t(r)?A(r):m)},enumerate:function(){let t=this;return T(function*(){let r=0;for(let o of t)yield[r,o],r++})()},filterMap:function(t){let r=this;return T(function*(){for(let o of r){let i=t(o);i.isSome()&&(yield i.unwrap())}})()},sort:function(t){let r=this.toArray();return r.sort(t),r},toArray:function(){return[...this]}}),e}Array.prototype.as_iter||(Array.prototype.as_iter=function(){let e=this;return T(function*(){for(let t of e)yield t})()});Set.prototype.as_iter||(Set.prototype.as_iter=function(){let e=this;return T(function*(){for(let t of e)yield t})()});Map.prototype.as_iter||(Map.prototype.as_iter=function(){let e=this;return T(function*(){for(let t of e)yield t})()});var J=/.^/,xe={Av1:{name:"Av1",type:"video",mimetype:/av01.*/i,defacto_container:"WebM"},H264:{name:"H264",type:"video",mimetype:/avc1.*/i,defacto_container:"Mp4"},H263:{name:"H263",type:"video",mimetype:J,defacto_container:"3gp"},H265:{name:"H265",type:"video",mimetype:/(hvc1|hevc|h265|h\.265).*/i,defacto_container:"Mp4"},MP4V:{name:"MP4V",type:"video",mimetype:/mp4v\.20.*/i,defacto_container:"Mp4"},MPEG1:{name:"MPEG1",type:"video",mimetype:J,defacto_container:"Mpeg"},MPEG2:{name:"MPEG2",type:"video",mimetype:J,defacto_container:"Mpeg"},Theora:{name:"Theora",type:"video",mimetype:/theora/i,defacto_container:"Ogg"},VP8:{name:"VP8",type:"video",mimetype:/vp0?8.*/i,defacto_container:"WebM"},VP9:{name:"VP9",type:"video",mimetype:/vp0?9.*/i,defacto_container:"WebM"},unknown:{name:"unknown",type:"video",mimetype:J,defacto_container:"Mp4"}},he={AAC:{name:"AAC",type:"audio",mimetype:/(aac|mp4a.40).*/i,defacto_container:"Mp4"},PCM:{name:"PCM",type:"audio",mimetype:/pcm.*/i,defacto_container:"Wav"},FLAC:{name:"FLAC",type:"audio",mimetype:/flac/i,defacto_container:"Flac"},MP3:{name:"MP3",type:"audio",mimetype:/(\.?mp3|mp4a\.69|mp4a\.6b).*/i,defacto_container:"Mpeg"},Opus:{name:"Opus",type:"audio",mimetype:/(opus|(mp4a\.ad.*))/i,defacto_container:"Ogg"},Vorbis:{name:"Vorbis",type:"audio",mimetype:/vorbis/i,defacto_container:"Ogg"},Wav:{name:"Wav",type:"audio",mimetype:J,defacto_container:"Wav"},unknown:{name:"unknown",type:"audio",mimetype:J,defacto_container:"Mp4"}},we=T(function*(){for(let e of Object.keys(xe))yield xe[e]}),be=T(function*(){for(let e of Object.keys(he))yield he[e]});var ve={Mp4:{name:"Mp4",extension:"mp4",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?mp4/i},Mkv:{name:"Mkv",extension:"mkv",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:we().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),supported_audio_codecs:be().filter(e=>e.name!="unknown").map(e=>e.name).toArray(),mimetype:/(?:x-)?matroska/i},WebM:{name:"WebM",extension:"webm",audio_only_extension:"oga",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","VP8","VP9","Av1"],supported_audio_codecs:["Opus","Vorbis"],mimetype:/(?:x-)?webm/i},M2TS:{name:"M2TS",extension:"mt2s",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2","VP9","unknown"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?mts/i},MP2T:{name:"MP2T",extension:"mp2t",audio_only_extension:"mp3",defacto_codecs:{audio:A("MP3"),video:A("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mp2t/i},Flash:{name:"Flash",extension:"flv",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264"],supported_audio_codecs:["AAC"],mimetype:/(?:x-)?flv/i},M4V:{name:"M4V",extension:"m4v",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H265","Av1","MP4V","MPEG2"],supported_audio_codecs:["Opus","MP3","FLAC","AAC"],mimetype:/(?:x-)?m4v/i},M4A:{name:"M4A",extension:"m4a",other_extensions:["aac"],audio_only_extension:"m4a",defacto_codecs:{audio:A("AAC"),video:m},supported_video_codecs:[],supported_audio_codecs:["Opus","MP3","FLAC","AAC","unknown"],mimetype:/(?:x-)?m4a/i},Flac:{name:"Flac",extension:"flac",audio_only_extension:"flac",defacto_codecs:{audio:A("FLAC"),video:m},supported_video_codecs:[],supported_audio_codecs:["FLAC"],mimetype:/(?:x-)?flac/i},Mpeg:{name:"Mpeg",extension:"mpeg",audio_only_extension:"mp3",defacto_codecs:{audio:A("MP3"),video:A("H264")},supported_video_codecs:["MPEG2","MPEG1"],supported_audio_codecs:["MP3"],mimetype:/(?:x-)?mpeg/i},Ogg:{name:"Ogg",extension:"ogv",audio_only_extension:"oga",defacto_codecs:{audio:m,video:m},supported_video_codecs:["VP9","VP8","Theora"],supported_audio_codecs:["Opus","Vorbis","FLAC"],mimetype:/(?:x-)?og./i},Wav:{name:"Wav",extension:"wav",audio_only_extension:"wav",defacto_codecs:{audio:A("Wav"),video:m},supported_video_codecs:[],supported_audio_codecs:["Wav","PCM"],mimetype:/(?:x-)?(?:pn-)?wave?/i},"3gp":{name:"3gp",extension:"3gpp",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["H264","H263","MP4V","VP8"],supported_audio_codecs:["MP3","AAC"],mimetype:/(?:x-)?3gpp2?/i},QuickTime:{name:"QuickTime",extension:"mov",audio_only_extension:"mp3",defacto_codecs:{audio:m,video:m},supported_video_codecs:["MPEG1","MPEG2"],supported_audio_codecs:[],mimetype:/(?:x-)?mov/i}},He=T(function*(){for(let e of Object.keys(ve))yield e}),Nt=T(function*(){for(let e of He())yield ve[e]});var Se={240:{id:"240",loose_name:"Small"},360:{id:"360",loose_name:"SD"},480:{id:"480",loose_name:"SD"},720:{id:"720",loose_name:"HD"},1080:{id:"1080",loose_name:"FullHD"},1440:{id:"1440",loose_name:"UHD"},2160:{id:"2160",loose_name:"4K"},4320:{id:"4320",loose_name:"8K"}};var Le=T(function*(){for(let e of Object.keys(Se))yield e}),zt=T(function*(){for(let e of Le())yield Se[e]});var ze=X(U(),1);function le(e,t){if(e==null||t===null||t===void 0)return e===t;if(e.constructor!==t.constructor)return!1;if(e instanceof Function||e instanceof RegExp)return e===t;if(e===t||e.valueOf()===t.valueOf())return!0;if(Array.isArray(e)&&e.length!==t.length||e instanceof Date||!(e instanceof Object)||!(t instanceof Object))return!1;let r=Object.keys(e),o=Object.keys(t).every(n=>r.indexOf(n)!==-1),i=r.every(n=>le(e[n],t[n]));return o&&i}async function ee(e,t){let r=t;e.hooks&&(r=e.hooks.setter(t)),await G.storage[e.where].set({[e.name]:r})}async function S(e){let t=await G.storage[e.where].get(e.name);if(e.name in t){let r=t[e.name];return e.hooks?e.hooks.getter(r,e):r}return e.default()}async function ue(e){await G.storage[e.where].remove(e.name)}function te(e,t){G.storage[e.where].onChanged.addListener(r=>{let o=r[e.name];if(o){if(le(o.oldValue,o.newValue))return;typeof o.newValue>"u"?t(e.default()):e.hooks?t(e.hooks.getter(o.newValue,e)):t(o.newValue)}})}var D={name:"record_download_history",default:()=>!1,where:"local"};var q={name:"view_options",default:()=>({}),where:"session"};var j={name:"download_history",where:"local",default:()=>new Map,hooks:{setter:e=>P(e).unwrap(),getter:(e,t)=>V(e).unwrapOr(t.default())}};async function ce(e){Me.default.runtime.sendMessage(e)}function Te(e,t){return t.test(e.page_url)?!0:!!t.test(e.download_result.filename)}async function K(e,t,r){let o=document.querySelector("#search"),i=o.value.length==0?null:new RegExp(o.value,"i"),n=document.querySelector("template").content;document.body.classList.toggle("recording",t);let u=document.querySelector("#noentries");e.size==0?u.removeAttribute("hidden"):u.setAttribute("hidden","true");let c=document.querySelector("#main"),_=Array.from(c.querySelectorAll(".history-entry"));for(let b of _){let M=e.get(b.id);M&&i&&Te(M,i)||b.remove()}let s=n.querySelector(".history-entry"),y=[...e.entries()].filter(([,b])=>i?Te(b,i):!0).sort(([,b],[,M])=>M.timestamp-b.timestamp).slice(0,200),x=0;for(let[b,M]of y){x++;let k=document.getElementById(b);if(!k){k=s.cloneNode(!0),ae(k,r),k.id=b;let F=new URL(M.page_url),Y=k.querySelector(".page-url");Y.textContent=F.host,Y.href=F.href;let Z=k.querySelector(".date");Z.textContent=ge(M.timestamp,r);let $=k.querySelector(".filename");$.style.backgroundImage=`url(${F.origin}/favicon.ico)`,$.textContent=M.download_result.filename,$.title=M.download_result.filename,c.appendChild(k)}k.style.order=x.toString()}}var Oe=await S(q),Je=await S(j),qe=await S(D);te(q,async e=>{let t=await S(D),r=await S(j);K(r,t,e)});te(D,async e=>{let t=await S(q),r=await S(j);K(r,e,t)});te(j,async e=>{let t=await S(q),r=await S(D);K(e,r,t)});ae(document,Oe);K(Je,qe,Oe);async function re(e){let t=e.target;if(!t)return;let r=t.closest(".history-entry")?.id;if(t.closest("#search")){let o=await S(j),i=await S(D),n=await S(q);K(o,i,n)}else if(t.closest("#button-clear-history"))ue(j);else if(t.closest("#button-start-recording"))ee(D,!0);else if(t.closest("#button-stop-recording"))ee(D,!1),ue(j);else if(t.closest(".button-rm"))ce({rm:r});else if(t.closest(".button-hide")){if(r){let o=await S(j);o.delete(r),await ee(j,o)}}else t.closest(".button-play")?ce({play:r}):t.closest(".button-dir")&&ce({show_dir:r})}window.addEventListener("click",re,!0);window.addEventListener("change",re);window.addEventListener("input",re);window.addEventListener("sl-clear",re);
